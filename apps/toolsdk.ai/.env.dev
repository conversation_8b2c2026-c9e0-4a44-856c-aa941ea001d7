
# aiapis.dev
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_cGxlYXNpbmctcG9zc3VtLTQ2LmNsZXJrLmFjY291bnRzLmRldiQ
CLERK_SECRET_KEY=sk_test_JR43lNhrcBYLVrGMXVYuRFNUM1wmNmlRvSWmvydO3h


# PG_DATABASE_URL="postgresql://bika:bikabika@127.0.0.1:5432/toolsdkdev?schema=public&connection_limit=1"
PG_DATABASE_URL="postgresql://neondb_owner:<EMAIL>/toolsdkdev?sslmode=require"
MINIO_URL="minio://bika:bikabika@127.0.0.1:9000/toolsdkdev?useSSL=false&autoCreateBucket=true"

# 默认不填，http://localhost:3000，local环境
APP_HOSTNAME=
STORAGE_PUBLIC_URL="http://127.0.0.1:9000/toolsdkdev"
ELASTIC_SEARCH_URL=***********************************/bika-dev

# 是否开启postgres日志
ENABLE_POSTGRES_LOGGING=