import { SkillsetVO } from '@bika/types/skill/vo';
import type { TalkExpertKey } from '@bika/types/space/bo';
import type { ILocaleContext } from '../../../i18n';

export const getExpertConfigs: (localeContext: ILocaleContext) => Record<
  TalkExpertKey,
  {
    visibility?: 'HIDDEN' | 'DISPLAY' | 'DEV_ONLY';
    name: string;
    description: string;
    // icon: INodeIconValue;
    url: string;
    welcomeTips?: string;
    skillsets?: SkillsetVO[] | null;
  }
> = (ctx) => {
  const { t } = ctx;
  return {
    space: {
      name: t.navbar.space_launcher,
      description: t.navbar.space_launcher_description,
      url: '',
    },
    builder: {
      name: t.navbar.agent_builder,
      description: t.navbar.agent_builder_description,
      welcomeTips: t.navbar.agent_builder_description,
      url: '/ai-app-builder',
      skillsets: [
        {
          kind: 'preset',
          name: '<PERSON><PERSON> Builder',
          description: '',
          skills: [],
          key: 'builder',
          logo: {
            type: 'PRESET',
            url: '/assets/ai/skillset/aiconsulting.png',
          },
        },
      ],
      visibility: 'DEV_ONLY',
    },
    supervisor: {
      name: t.navbar.chief_of_staff,
      description: t.navbar.chief_of_staff_description,
      url: '/supervisor',
      skillsets: [
        {
          kind: 'preset',
          name: 'Space Agent',
          description: '',
          skills: [],
          key: 'supervisor',
          logo: {
            type: 'PRESET',
            url: '/assets/ai/skillset/default.png',
          },
        },
      ],
      visibility: 'DEV_ONLY',
    },
    mission: {
      name: t.navbar.todo,
      description: 'Smart, automative, traceable tasks',
      url: '/my/todos',
    },
    report: {
      name: t.navbar.report,
      description: 'Reports',
      url: '/my/reports',
    },
    'template-center': {
      name: t.template.title,
      description: 'Template Center, create and manage templates',
      url: '/template',
    },
    trash: {
      name: t.trash.trash,
      description: 'Manage deleted items',
      url: '/trash',
    },
    // 'app-store': {
    //   visibility: 'HIDDEN',
    //   name: 'App Store',
    //   description: '',
    //   url: '/my/launcher',
    // },
    // debugger: {
    //   visibility: 'DEV_ONLY',
    //   name: 'AI Debugger',
    //   description: 'Debug and test AI agents',
    //   url: '/my/ai',
    //   iconType: 'ai',
    //   path: '/assets/ai/agent_avatars_chief_of_staff.png',
    // },
  };
};
