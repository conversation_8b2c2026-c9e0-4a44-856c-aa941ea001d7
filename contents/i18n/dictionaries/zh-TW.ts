const dict = {
  about: {
    about: '關於',
    address: '488 The Bridle Walk, Toronto, ON L6C 2Y4 Canada',
    cn_license: '备案号：粤ICP备********号-4 公安备案号：**************',
    copyright: '版權所有 © 2025 Bika.ai',
    license: '使用許可',
    permission: '應用權限說明',
    privacy_policy: '隱私政策',
    rate_us: '為我們評價',
    release_notes: '變更日誌',
    safety: '安全白皮書',
  },
  account: {
    account_binding: '帳號綁定',
    account_binding_and_security: '帳號綁定和安全',
    account_binding_description: '為了更好地接收提醒和處理任務,請選擇以下一種方式登入您的 Bika 帳戶。',
    account_binding_error: '帳號綁定錯誤',
    account_binding_error_description: '綁定帳戶時出現錯誤，請重試。',
    account_binding_error_type: '當前{type}已綁定過其他帳號',
    account_binding_success: '帳戶綁定成功',
    account_info: '帳戶資訊',
    advanced_features: '進階功能',
    bind_now: '立即綁定',
    bound: '已綁定',
    delete_account: '刪除帳戶',
    delete_account_description: '確定要刪除帳戶嗎？',
    destroy_account: '刪除帳戶',
    social_account_binding: '社交帳號綁定',
    social_account_binding_description: '綁定社交帳號以解鎖更多功能',
    subscribe_now: '立即訂閱',
    use_advanced_features: '使用進階功能',
    use_advanced_features_description: '開啟進階功能以提高團隊效率',
  },
  action: {
    accept: '接受',
    accepted: '已接受',
    add: '新增',
    added: '已新增',
    adding: '新增中...',
    again: '再試一次',
    apply: '批准',
    auth: '驗證',
    back: '返回',
    cancel: '取消',
    choose: '選擇',
    click_here: '點擊這裡',
    close: '關閉',
    coming_soon: '敬請期待',
    coming_soon_description: '此功能即將推出，敬請期待',
    comment: '評論',
    commented: '已評論',
    complete: '完成',
    completed: '已完成',
    confirm: '確認',
    connect: '連接',
    connected: '已連接',
    contact_community: '聯繫社區',
    create: '創建',
    create_data: '新增數據',
    create_folder: '創建文件夾',
    create_resource: '創建資源',
    created: '已創建',
    current: '當前',
    decline: '拒絕',
    declined: '已拒絕',
    delete: '刪除',
    delete_resource: '刪除資源',
    deleted: '已刪除',
    detail: '詳情',
    due: '到期',
    duplicate: '複製',
    edit: '編輯',
    edit_resource: '編輯資源',
    edited: '已編輯',
    email_sent: '郵件已發送',
    failed: '失敗',
    get: '獲取',
    import: '導入',
    install: '安裝',
    installed: '已安裝',
    loading: '加載中...',
    manual_complete: '手動完成',
    more: '瞭解更多',
    move: '移動',
    next: '下一步',
    no: '否',
    not_supported: '暫不支援',
    not_supported_description: '此功能暫不支援',
    ok: '確定',
    only_on_web: '僅支援 Web 版',
    only_on_web_description: '此功能僅支援 Web 版，請使用瀏覽器打開',
    only_on_web_editor: '編輯器',
    only_on_web_editor_description: '編輯器僅支援 Web 版，請使用瀏覽器打開',
    overdue: '已过期',
    process: '處理',
    processing: '處理中',
    reauthorize_integration: '重新授權',
    redirect: '重定向',
    remove: '移除',
    removed: '已移除',
    rename: '重命名',
    save: '儲存',
    saved: '已儲存',
    search: '搜索',
    search_placeholder: '搜索...',
    search_result: '搜索結果',
    search_result_description: '搜索結果',
    search_result_not_found: '未找到搜索結果',
    search_result_not_found_description: '未找到搜索結果',
    searching: '搜索中...',
    select: '選擇',
    select_all: '全選',
    selected: '已選擇',
    send: '發送',
    submit: '提交',
    submitted: '已提交',
    submitter: '提交人',
    transfer: '轉移',
    transferred: '已轉移',
    uninstall: '解除安裝',
    uninstalled: '已解除安裝',
    unselect_all: '取消全选',
    view: '檢視',
    viewed: '已檢視',
    yes: '是',
  },
  ag_grid: {
    AreaColumnCombo: '區域與列',
    addCurrentSelectionToFilter: '將當前選擇新增到篩選條件中',
    addToLabels: '添加 ${variable} 到標籤',
    addToValues: '添加 ${variable} 到值',
    advancedFilterAnd: '和',
    advancedFilterApply: '應用',
    advancedFilterBlank: '為空白',
    advancedFilterBuilder: '構建器',
    advancedFilterBuilderAddButtonTooltip: '添加篩選或群組',
    advancedFilterBuilderAddCondition: '添加篩選',
    advancedFilterBuilderAddJoin: '添加群組',
    advancedFilterBuilderApply: '應用',
    advancedFilterBuilderCancel: '取消',
    advancedFilterBuilderEnterValue: '輸入值...',
    advancedFilterBuilderMoveDownButtonTooltip: '下移',
    advancedFilterBuilderMoveUpButtonTooltip: '上移',
    advancedFilterBuilderRemoveButtonTooltip: '移除',
    advancedFilterBuilderSelectColumn: '選擇欄位',
    advancedFilterBuilderSelectOption: '選擇選項',
    advancedFilterBuilderTitle: '進階篩選',
    advancedFilterBuilderValidationAlreadyApplied: '當前篩選已應用。',
    advancedFilterBuilderValidationEnterValue: '必須輸入一個值。',
    advancedFilterBuilderValidationIncomplete: '並非所有條件都已完成。',
    advancedFilterBuilderValidationSelectColumn: '必須選擇一個欄位。',
    advancedFilterBuilderValidationSelectOption: '必須選擇一個選項。',
    advancedFilterContains: '包含',
    advancedFilterEndsWith: '結尾是',
    advancedFilterEquals: '=',
    advancedFilterFalse: '為假',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterNotBlank: '不為空白',
    advancedFilterNotContains: '不包含',
    advancedFilterNotEqual: '!=',
    advancedFilterOr: '或',
    advancedFilterStartsWith: '開頭是',
    advancedFilterTextEquals: '等於',
    advancedFilterTextNotEqual: '不等於',
    advancedFilterTrue: '為真',
    advancedFilterValidationExtraEndBracket: '結尾括號過多',
    advancedFilterValidationInvalidColumn: '找不到欄位',
    advancedFilterValidationInvalidDate: '值不是有效的日期',
    advancedFilterValidationInvalidJoinOperator: '找不到連接操作符',
    advancedFilterValidationInvalidOption: '找不到選項',
    advancedFilterValidationJoinOperatorMismatch: '條件內的連接操作符必須一致',
    advancedFilterValidationMessage: '表達式有錯誤。${variable} - ${variable}。',
    advancedFilterValidationMessageAtEnd: '表達式有錯誤。${variable} 在表達式結尾。',
    advancedFilterValidationMissingColumn: '缺少欄位',
    advancedFilterValidationMissingCondition: '缺少條件',
    advancedFilterValidationMissingEndBracket: '缺少結尾括號',
    advancedFilterValidationMissingOption: '缺少選項',
    advancedFilterValidationMissingQuote: '值缺少結尾引號',
    advancedFilterValidationMissingValue: '缺少值',
    advancedFilterValidationNotANumber: '值不是數字',
    advancedSettings: '高級設置',
    after: '之後',
    aggregate: '彙總',
    andCondition: '和',
    animation: '動畫',
    applyFilter: '應用',
    april: '四月',
    area: '區域圖',
    areaChart: '區域圖',
    areaColumnComboTooltip: '區域 & 柱形圖',
    areaGroup: '區域圖',
    ariaAdvancedFilterBuilderColumn: '欄',
    ariaAdvancedFilterBuilderFilterItem: '篩選條件',
    ariaAdvancedFilterBuilderGroupItem: '篩選組',
    ariaAdvancedFilterBuilderItem: '${variable}。級別 ${variable}。按 ENTER 鍵編輯。',
    ariaAdvancedFilterBuilderItemValidation: '${variable}。級別 ${variable}。${variable} 按 ENTER 鍵編輯。',
    ariaAdvancedFilterBuilderJoinOperator: '連接運算子',
    ariaAdvancedFilterBuilderList: '進階篩選器構建器列表',
    ariaAdvancedFilterBuilderOption: '選項',
    ariaAdvancedFilterBuilderValueP: '值',
    ariaAdvancedFilterInput: '進階篩選輸入',
    ariaChartMenuClose: '關閉圖表編輯選單',
    ariaChartSelected: '已選擇',
    ariaChecked: '已勾選',
    ariaColumn: '欄',
    ariaColumnFiltered: '欄已篩選',
    ariaColumnGroup: '欄組',
    ariaColumnPanelList: '欄位列表',
    ariaColumnSelectAll: '切換選取所有欄',
    ariaDateFilterInput: '日期篩選輸入',
    ariaDefaultListName: '列表',
    ariaDropZoneColumnComponentAggFuncSeparator: ' 的 ',
    ariaDropZoneColumnComponentDescription: '按 DELETE 刪除',
    ariaDropZoneColumnComponentSortAscending: '升序',
    ariaDropZoneColumnComponentSortDescending: '降序',
    ariaDropZoneColumnGroupItemDescription: '按 ENTER 排序',
    ariaDropZoneColumnValueItemDescription: '按 ENTER 更改聚合類型',
    ariaFilterColumn: '按 CTRL + ENTER 鍵打開篩選',
    ariaFilterColumnsInput: '篩選欄輸入',
    ariaFilterFromValue: '從值篩選',
    ariaFilterInput: '篩選輸入',
    ariaFilterList: '篩選列表',
    ariaFilterMenuOpen: '打開篩選器選單',
    ariaFilterPanelList: '篩選列表',
    ariaFilterToValue: '篩選到值',
    ariaFilterValue: '篩選值',
    ariaFilteringOperator: '篩選運算子',
    ariaHidden: '隱藏',
    ariaIndeterminate: '不確定',
    ariaInputEditor: '輸入編輯器',
    ariaLabelAdvancedFilterAutocomplete: '高級過濾自動完成',
    ariaLabelAdvancedFilterBuilderAddField: '高級過濾構建器添加欄位',
    ariaLabelAdvancedFilterBuilderColumnSelectField: '高級過濾構建器列選擇欄位',
    ariaLabelAdvancedFilterBuilderJoinSelectField: '高級過濾構建器聯接運算符選擇欄位',
    ariaLabelAdvancedFilterBuilderOptionSelectField: '高級過濾構建器選項選擇欄位',
    ariaLabelAggregationFunction: '聚合函數',
    ariaLabelCellEditor: '儲存格編輯器',
    ariaLabelColumnFilter: '欄位篩選器',
    ariaLabelColumnMenu: '欄位選單',
    ariaLabelContextMenu: '上下文菜單',
    ariaLabelDialog: '對話框',
    ariaLabelRichSelectDeleteSelection: '按下刪除鍵以取消選擇項目',
    ariaLabelRichSelectDeselectAllItems: '按下刪除鍵以取消選擇所有項目',
    ariaLabelRichSelectField: '豐富選擇欄位',
    ariaLabelRichSelectToggleSelection: '按下空格鍵以切換選擇',
    ariaLabelSelectField: '選擇欄位',
    ariaLabelSubMenu: '子菜單',
    ariaLabelTooltip: '工具提示',
    ariaMenuColumn: '按 ALT + DOWN 鍵打開欄選單',
    ariaPageSizeSelectorLabel: '每頁大小',
    ariaPivotDropZonePanelLabel: '列標籤',
    ariaRowDeselect: '按 SPACE 鍵取消選取此行',
    ariaRowGroupDropZonePanelLabel: '行群組',
    ariaRowSelect: '按 SPACE 鍵選取此行',
    ariaRowSelectAll: '按 SPACE 鍵切換所有行的選擇',
    ariaRowSelectionDisabled: '此行已禁用行選擇',
    ariaRowToggleSelection: '按 SPACE 鍵切換行的選擇',
    ariaSearch: '搜尋',
    ariaSearchFilterValues: '搜尋篩選值',
    ariaSkeletonCellLoading: '行數據加載中',
    ariaSkeletonCellLoadingFailed: '行加載失敗',
    ariaSortableColumn: '按 ENTER 鍵排序',
    ariaToggleCellValue: '按 SPACE 鍵切換單元格值',
    ariaToggleVisibility: '按 SPACE 鍵切換可見性',
    ariaUnchecked: '未勾選',
    ariaValuesDropZonePanelLabel: '數值',
    ariaVisible: '可見',
    asc_option: '選項正序',
    august: '八月',
    autoRotate: '自動旋轉',
    automatic: '自動',
    autosizeAllColumns: '自適應所有列寬',
    autosizeThisColumn: '自適應列寬',
    avg: '平均值',
    axis: '軸',
    axisType: '軸類型',
    background: '背景',
    bar: '條形圖',
    barChart: '條形',
    barGroup: '條形圖',
    before: '之前',
    blank: '空白',
    blanks: '(空白)',
    blur: '模糊',
    bold: '粗體',
    boldItalic: '粗斜體',
    bottom: '下',
    boxPlot: '箱型圖',
    boxPlotTooltip: '箱線圖',
    bubble: '氣泡圖',
    bubbleTooltip: '氣泡圖',
    callout: '突顯',
    calloutLabels: '突顯標籤',
    cancelFilter: '取消',
    cap: '帽端',
    capLengthRatio: '帽端長度比',
    categories: '類別',
    category: '類別',
    categoryAdd: '添加類別',
    categoryValues: '類別值',
    chartAdvancedSettings: '高級設置',
    chartDownload: '下載圖表',
    chartDownloadToolbarTooltip: '下載圖表',
    chartEdit: '編輯圖表',
    chartLink: '鏈接到格線',
    chartLinkToolbarTooltip: '鏈接到格線',
    chartMenuToolbarTooltip: '菜單',
    chartRange: '圖表範圍',
    chartSettingsToolbarTooltip: '菜單',
    chartStyle: '圖表樣式',
    chartSubtitle: '副標題',
    chartTitle: '圖表標題',
    chartTitles: '標題',
    chartUnlink: '從格線取消鏈接',
    chartUnlinkToolbarTooltip: '從格線取消鏈接',
    circle: '圓形',
    clearFilter: '清除',
    collapseAll: '關閉所有分組',
    color: '顏色',
    column: '柱形圖',
    columnChart: '列',
    columnChooser: '選擇列',
    columnFilter: '列過濾器',
    columnGroup: '柱形圖',
    columnLineCombo: '列與折線',
    columnLineComboTooltip: '柱形 & 折線圖',
    columns: '欄位',
    combinationChart: '組合圖',
    combinationGroup: '組合圖',
    connectorLine: '連接線',
    contains: '包含',
    copy: '複製',
    copyWithGroupHeaders: '複製包括分組標題',
    copyWithHeaders: '複製包括標題',
    copy_row: '複製行',
    count: '數量',
    cross: '十字形',
    crosshair: '準星',
    crosshairLabel: '標籤',
    crosshairSnap: '對齊節點',
    csvExport: '導出 CSV 文件',
    ctrlC: 'Ctrl+C',
    ctrlV: 'Ctrl+V',
    ctrlX: 'Ctrl+X',
    customCombo: '自訂組合圖',
    customComboTooltip: '自訂組合圖',
    cut: '剪下',
    data: '設定',
    dateFilter: '日期篩選',
    dateFormatOoo: 'yyyy-mm-dd',
    december: '十二月',
    decimalSeparator: '.',
    defaultCategory: '(無)',
    desc_option: '選項倒序',
    diamond: '菱形',
    direction: '方向',
    donut: '甜甜圈圖',
    donutTooltip: '甜甜圈圖',
    duplicate_record: '重複記錄',
    durationMillis: '持續時間 (毫秒)',
    empty: '選擇一個',
    enabled: '已啟用',
    endAngle: '終止角度',
    endsWith: '結束於',
    equals: '等於',
    excelExport: '導出為 Excel 文件',
    expandAll: '展開所有分組',
    expand_record: '展開記錄',
    export: '導出',
    false: '假',
    february: '二月',
    fillOpacity: '填充不透明度',
    filterOoo: '篩選...',
    filteredRows: '已篩選',
    filters: '篩選器',
    first: '第一個',
    firstPage: '第一頁',
    fixed: '固定',
    font: '字體',
    footerTotal: '總數',
    format: '自訂',
    greaterThan: '大於',
    greaterThanOrEqual: '大於或等於',
    gridLines: '網格線',
    group: '群組',
    groupBy: '依此分組',
    groupFilterSelect: '選擇欄位：',
    groupPadding: '組填充量',
    groupedAreaTooltip: '面積圖',
    groupedBar: '分組',
    groupedBarFull: '分組條形圖',
    groupedBarTooltip: '分組',
    groupedColumn: '分組',
    groupedColumnFull: '分組柱形圖',
    groupedColumnTooltip: '分組',
    groupedSeriesGroupType: '分組',
    groups: '行分組',
    heart: '心形',
    heatmap: '熱圖',
    heatmapTooltip: '熱圖',
    height: '高度',
    hierarchicalChart: '層次圖',
    hierarchicalGroup: '層次圖',
    histogram: '直方圖',
    histogramBinCount: '柱數',
    histogramChart: '直方圖',
    histogramFrequency: '頻率',
    histogramTooltip: '直方圖',
    horizontal: '水平',
    horizontalAxisTitle: '水平軸標題',
    inRange: '介於',
    inRangeEnd: '到',
    inRangeStart: '從',
    innerRadius: '內半徑',
    inside: '內部',
    invalidColor: '顏色值無效',
    invalidDate: '無效日期',
    invalidNumber: '無效數字',
    italic: '斜體',
    itemPaddingX: '項目填充 X',
    itemPaddingY: '項目填充 Y',
    itemSpacing: '項目間距',
    january: '一月',
    july: '七月',
    june: '六月',
    labelPlacement: '標籤放置',
    labelRotation: '旋轉',
    labels: '標籤',
    last: '最後一個',
    lastPage: '最後一頁',
    layoutHorizontalSpacing: '水平間距',
    layoutVerticalSpacing: '垂直間距',
    left: '左',
    legend: '圖例',
    legendEnabled: '啟用',
    length: '長度',
    lessThan: '小於',
    lessThanOrEqual: '小於或等於',
    line: '折線圖',
    lineDash: '虛線',
    lineDashOffset: '虛線偏移',
    lineGroup: '折線圖',
    lineTooltip: '折線圖',
    lineWidth: '線寬',
    loadingError: '錯誤',
    loadingOoo: '加載中...',
    lookup_unamed_record: '未命名記錄',
    march: '三月',
    markerPadding: '標記填充',
    markerSize: '標記大小',
    markerStroke: '標記描邊',
    markers: '標記',
    max: '最大值',
    maxSize: '最大大小',
    may: '五月',
    min: '最小值',
    minSize: '最小大小',
    miniChart: '迷你圖表',
    more: '更多',
    navigator: '導覽器',
    nextPage: '下一頁',
    nightingale: '南丁格爾玫瑰圖',
    nightingaleTooltip: '夜鶯圖',
    noAggregation: '無',
    noDataToChart: '沒有可作圖的數據。',
    noMatches: '無匹配項',
    noPin: '取消固定',
    noRowsToShow: '無顯示行',
    none: '無',
    normal: '正常',
    normalizedArea: '100% 堆疊',
    normalizedAreaFull: '100% 堆積面積圖',
    normalizedAreaTooltip: '100% 堆積面積圖',
    normalizedBar: '100% 堆疊',
    normalizedBarFull: '100% 堆疊條形圖',
    normalizedBarTooltip: '100% 堆疊',
    normalizedColumn: '100% 堆疊',
    normalizedColumnFull: '100% 堆疊柱形圖',
    normalizedColumnTooltip: '100% 堆疊',
    normalizedSeriesGroupType: '100% 堆疊',
    notBlank: '非空白',
    notContains: '不包含',
    notEqual: '不等於',
    november: '十一月',
    number: '數字',
    numberFilter: '數字篩選',
    october: '十月',
    of: '的',
    offset: '偏移',
    offsets: '偏移值',
    orCondition: '或',
    orientation: '方向',
    outside: '外部',
    padding: '填充',
    page: '頁',
    pageLastRowUnknown: '?',
    pageSizeSelectorLabel: '頁大小：',
    paired: '配對模式',
    parallel: '平行',
    paste: '貼上',
    pasting_multiple_columns_is_not_supportted_currently: '暫不支持粘貼多列數據',
    perpendicular: '垂直',
    pie: '圓餅圖',
    pieChart: '圓餅圖',
    pieGroup: '圓餅圖',
    pieTooltip: '圓餅圖',
    pinColumn: '固定欄位',
    pinLeft: '固定在左側',
    pinRight: '固定在右側',
    pivotChart: '樞軸圖',
    pivotChartAndPivotMode: '樞軸圖與樞軸模式',
    pivotChartRequiresPivotMode: '樞紐圖需要啟用樞紐模式。',
    pivotChartTitle: '樞紐圖',
    pivotColumnGroupTotals: '總計',
    pivotColumnsEmptyMessage: '拖動到此處設置欄位標籤',
    pivotMode: '樞紐分析模式',
    pivots: '欄位標籤',
    plus: '加號',
    polarAxis: '極軸',
    polarAxisTitle: '極軸標題',
    polarChart: '極地圖',
    polarGroup: '極地圖',
    polygon: '多邊形',
    position: '位置',
    positionRatio: '位置比例',
    predefined: '預設',
    preferredLength: '首選長度',
    previousPage: '上一頁',
    radarArea: '雷達面',
    radarAreaTooltip: '雷達面積圖',
    radarLine: '雷達線',
    radarLineTooltip: '雷達折線圖',
    radialBar: '輻射條形',
    radialBarTooltip: '雷達條形圖',
    radialColumn: '輻射列柱',
    radialColumnTooltip: '雷達柱形圖',
    radiusAxis: '半徑軸',
    radiusAxisPosition: '位置',
    rangeArea: '範圍區域圖',
    rangeAreaTooltip: '範圍區域圖',
    rangeBar: '範圍條形圖',
    rangeBarTooltip: '範圍條形圖',
    rangeChartTitle: '範圍圖',
    removeFromLabels: '從標籤中移除 ${variable}',
    removeFromValues: '從值中移除 ${variable}',
    resetColumns: '重設列',
    resetFilter: '重置',
    reverseDirection: '逆向',
    right: '右',
    rowDragRow: '列',
    rowDragRows: '列',
    rowGroupColumnsEmptyMessage: '拖動到此處設置行分組',
    row_group: '分組',
    scatter: '散點圖',
    scatterGroup: 'X Y (散點圖)',
    scatterTooltip: '散點圖',
    scrollingStep: '滾動步伐',
    scrollingZoom: '滾動',
    searchOoo: '搜尋...',
    secondaryAxis: '次軸',
    sectorLabels: '區段標籤',
    see_more_detail: '查看更多',
    selectAll: '(全選)',
    selectAllSearchResults: '(全選搜尋結果)',
    selectedRows: '已選擇',
    selectingZoom: '選擇',
    september: '九月',
    series: '系列',
    seriesAdd: '添加系列',
    seriesChartType: '系列圖表類型',
    seriesGroupType: '組類型',
    seriesItemLabels: '項目標籤',
    seriesItemNegative: '負數',
    seriesItemPositive: '正數',
    seriesItemType: '項目類型',
    seriesItems: '系列項目',
    seriesLabels: '系列標籤',
    seriesPadding: '系列填充量',
    seriesType: '系列類型',
    setFilter: '集合篩選',
    settings: '圖表',
    shadow: '陰影',
    shape: '形狀',
    size: '大小',
    sortAscending: '升序排列',
    sortDescending: '降序排列',
    sortUnSort: '清除排列',
    spacing: '間距',
    specializedChart: '專門圖表',
    specializedGroup: '專業圖',
    square: '方形',
    stackedArea: '堆疊',
    stackedAreaFull: '堆積面積圖',
    stackedAreaTooltip: '堆積面積圖',
    stackedBar: '堆疊',
    stackedBarFull: '堆疊條形圖',
    stackedBarTooltip: '堆疊',
    stackedColumn: '堆疊',
    stackedColumnFull: '堆疊柱形圖',
    stackedColumnTooltip: '堆疊',
    stackedSeriesGroupType: '堆疊',
    startAngle: '起始角度',
    startsWith: '開始於',
    statisticalChart: '統計圖',
    statisticalGroup: '統計圖',
    strokeColor: '線條顏色',
    strokeOpacity: '線條透明度',
    strokeWidth: '描邊寬度',
    sum: '總和',
    sunburst: '旭日圖',
    sunburstTooltip: '旭日圖',
    switchCategorySeries: '切換類別/系列',
    textFilter: '文字篩選',
    thickness: '厚度',
    thousandSeparator: ',',
    ticks: '刻度',
    tile: '平鋪',
    time: '時間',
    timeFormat: '時間格式',
    timeFormatDashesYYYYMMDD: 'YYYY-MM-DD',
    timeFormatDotsDDMYY: 'DD.M.YY',
    timeFormatDotsMDDYY: 'M.DD.YY',
    timeFormatHHMMSS: 'HH:MM:SS',
    timeFormatHHMMSSAmPm: 'HH:MM:SS AM/PM',
    timeFormatSlashesDDMMYY: 'DD/MM/YY',
    timeFormatSlashesDDMMYYYY: 'DD/MM/YYYY',
    timeFormatSlashesMMDDYY: 'MM/DD/YY',
    timeFormatSlashesMMDDYYYY: 'MM/DD/YYYY',
    timeFormatSpacesDDMMMMYYYY: 'DD MMMM YYYY',
    title: '標題',
    titlePlaceholder: '圖表標題',
    to: '至',
    tooltips: '工具提示',
    top: '上',
    totalAndFilteredRows: '行',
    totalRows: '總行數',
    treemap: '樹狀圖',
    treemapTooltip: '樹狀圖',
    triangle: '三角形',
    true: '真',
    ungroupAll: '取消所有分組',
    ungroupBy: '取消分組',
    valueAggregation: '值聚合',
    valueColumnsEmptyMessage: '拖動到此處聚合',
    values: '數值',
    vertical: '垂直',
    verticalAxisTitle: '垂直軸標題',
    waterfall: '瀑布圖',
    waterfallTooltip: '瀑布圖',
    weight: '粗細',
    whisker: '鬚狀圖',
    width: '寬度',
    xAxis: '水平軸',
    xOffset: 'X 偏移',
    xType: 'X 類型',
    xyChart: 'X Y (散點圖)',
    xyValues: 'X Y 值',
    yAxis: '垂直軸',
    yOffset: 'Y 偏移',
    zoom: '縮放',
  },
  agenda: {
    agenda: '議程',
    description: '描述',
    reminder_time: '提醒時間',
  },
  ai: {
    ai_image_generation: 'AI 圖像生成',
    ai_translate_all: 'AI 翻譯ALL',
    artifact_code: '代碼',
    artifact_preview: '預覽',
    artifact_template_code: '模板代碼',
    artifact_workflow: '工作流',
    completed: '已完成',
    generate: 'AI 生成',
    generate_image: '生成圖像',
    generated_characters: '已生成{characters}個字符，完成{percent}%',
    generated_result: '生成結果',
    generating: '生成中...',
    history: '歷史記錄',
    in_progress: '進行中',
    insert: '插入新內容',
    launcher_esc: '關閉窗口',
    launcher_open: '開啟',
    launcher_select: '選擇',
    launcher_tips_prefix: '使用',
    launcher_tips_suffix: '鍵切換篩選器',
    load_more: 'Load More',
    name: 'AI 寫作',
    new_chat: '新對話',
    overwrite: '替換原有內容',
    pick_an_image: '選擇一張圖像',
    press_enter: '按 Enter 发送, 按 Shift+Enter 换行',
    reference: '參考資料 {count}條',
    regenerate: '重新生成',
    restore_conversation: 'Restore Conversation',
    share_conversation: 'Share Conversation',
    start_chat: '開始聊天',
    thinking: '思考中...',
    thought: '思考了{seconds}秒',
    type_message: '請輸入消息',
    voice_hold_to_speak: '按住說話',
    voice_release_to_send: '鬆開發送',
  },
  ai_consultant: {
    deep_think: '深度思考',
    description: '我們是你的 AI 商業顧問團隊。我們可以幫助你分析業務，生成解決方案建議並為你構建自動化 AI 系統',
    history: '歷史記錄',
    quick: '標準',
    replay: '重播',
    title: 'AI 應用顧問',
  },
  api: {
    api: 'API',
    create_token: '創建 API Token',
    create_token_description: '這些令牌允許其他應用程序控制您的整個帳戶。請小心！',
    create_token_success: '創建令牌成功',
    created_public_api_token: '已創建公共 API Token',
    delete_token: '刪除 Token',
    delete_token_description: '確定要刪除這個 Token 嗎？',
    delete_token_success: '刪除令牌成功',
    developer_api: '開發者 API',
    e180days: '180 天',
    e1day: '1 天',
    e1month: '1 個月',
    e1year: '1 年',
    e2month: '2 個月',
    e30days: '30 天',
    e3days: '3 天',
    e6month: '6 個月',
    e7days: '7 天',
    expiration: '過期時間',
    my_tokens: '我的令牌',
    my_tokens_description: '您的 API 令牌需要像其他密碼一樣安全地處理。',
    name: '名稱',
    never: '無期限',
    select_expiration: '選擇有效期',
  },
  auth: {
    agree_description:
      '感謝您使用 Bika.ai。以下條款將幫助您了解用戶和隱私政策，以及您享有的相關權利。您可以通過 {privacy} 和 {team} 查看完整內容。',
    agree_title: '請閱讀並同意服務條款',
    auth_error: '驗證錯誤',
    auth_error_description: '驗證時出現錯誤，請重試。',
    back: '返回',
    back_website: '返回官網首頁',
    continue_with_apple: '使用 Apple 繼續',
    continue_with_email: '使用電子郵件繼續',
    continue_with_facebook: '使用 Facebook 繼續',
    continue_with_github: '使用 GitHub 繼續',
    continue_with_google: '使用 Google 繼續',
    continue_with_phone: '使用手機號繼續',
    continue_with_twitter: '使用 Twitter 繼續',
    continue_with_username: '使用帳戶名繼續',
    continue_with_weixin: '使用微信繼續',
    get_started: '開始使用',
    invite_you_to_join: '邀請你加入',
    invite_you_to_register: '{me} 邀請您註冊 Bika 帳戶。',
    link_account_already_exists: '{type} 帳戶已綁定到另一個 Bika 帳戶。您想直接登入綁定的帳戶嗎？',
    loading: '登入中...',
    login: '登入',
    login_and_register: '登入/註冊',
    login_success: '登入成功',
    logout: '登出',
    no_permission: '抱歉，該邀請鏈接僅適用於企業內部用戶。請使用您的企業郵箱登錄或聯繫管理員獲取有效的邀請鏈接',
    or: '或',
    password: '密碼',
    password_is_required: '密碼為必填項。',
    privacy_policy: '隱私政策',
    quick_login: '快速登入',
    quick_login_description: '無需密碼和驗證即可快速註冊新用戶。',
    register_agreement: '註冊即表示您同意我們的 {team} 和 {privacy}',
    scan_qrcode_to_login: '掃描二維碼登入',
    sign_in: '登入',
    sign_up: '註冊',
    switch_account: '切換帳號',
    terms_and_conditions: '請閱讀並接受我們的隱私政策和服務條款。我們致力於保護您的隱私和資料。',
    terms_of_service: '服務條款',
  },
  automation: {
    action: {
      actions: '執行器',
      ai_summary: {
        description: '通過 AI 模型，對文本內容進行摘要處理。',
        name: 'AI 文本摘要',
      },
      call_agent: {
        agent_id_subject: '代理人 ID',
        chat_id_subject: '聊天 ID',
        description: '撥打指定的代理人並發送消息。',
        message_subject: '消息',
        name: '撥打代理人',
      },
      condition: {
        description: '根據條件判斷結果，執行不同的動作。',
        name: '條件判斷',
      },
      create_document: {
        description: '通過多個步驟和變量的組合動態生成文檔。',
        name: '創建文檔',
      },
      create_mission: {
        description: '創建新的任務，並派發給指定的成員、角色或小組。',
        name: '創建智能任務',
      },
      create_node_resource: {
        description: '通過多個步驟和變量的組合動態生成資源節點（資料庫、文檔、自動化、表單等）。',
        name: '創建資源節點',
      },
      create_record: {
        description: '在指定的數據表中，創建一條新的記錄。',
        name: '創建記錄',
      },
      deepseek_generate_text: {
        description: '使用 DeepSeek API 生成文本內容。',
        name: 'DeepSeek - 生成文本',
      },
      delay: {
        description: '暫停若干時間後，再執行下一步動作。',
        name: '延時',
        queue: '隊列',
        unit: '單位',
        unit_day: '天',
        unit_hour: '小時',
        unit_minute: '分鐘',
        unit_second: '秒',
        unit_week: '週',
        value: '值',
      },
      description: '備註',
      dingtalk_webhook: {
        description: '通過釘釘自定義機器人的 Webhook URL，發送消息到指定群。',
        message_title_description: '輸入欲傳送的訊息標題，此標題將顯示於釘釘左側訊息列表的摘要內。',
        name: '發送消息至釘釘群',
      },
      dummy_action: {
        description: '用於測試和驗證自動化流程。',
        name: '模擬動作',
      },
      feishu_webhook: {
        description: '通過飛書自定義機器人的 Webhook URL，發送消息到指定群。',
        name: '發送消息至飛書群',
      },
      filter: {
        description: '若滿足篩選條件，將繼續運行後續執行器',
        name: '篩選',
      },
      find_dashboard: {
        description: '查找指定的儀表盤。',
        name: '查找儀表盤',
      },
      find_members: {
        description: '根據篩選條件，查找符合條件的空間站成員。',
        name: '獲取成員列表',
        to_email_addresses: '郵件地址',
        to_email_addresses_description: '多個郵件地址用逗號分隔，輸入"/"可插入變量',
      },
      find_missions: {
        description: '查找指定的任務。',
        name: '查找任務',
      },
      find_records: {
        description: '從指定的數據表中，根據視圖或篩選條件，獲取若干記錄。',
        find_database: '根據視圖進行篩選',
        find_database_select: '根據數據表進行篩選',
        name: '獲取記錄',
        placeholder_select_type: '請選擇查找方式',
        records_limit_description: '每次查找的最大記錄數。設為1時僅返回單條記錄。',
        records_limit_placeholder: '1 - 100',
        records_limit_title: '記錄上限',
        title_interrupt_if_no_record: '如果未找到記錄，是否中斷執行',
        type: '查找方式',
      },
      find_widget: {
        description: '查找指定的組件。',
        name: '查找組件',
        widget_empty: '組件不能為空',
      },
      formapp_ai: {
        create_and_load_formapp: '創建並加載 FormApp.ai 应用',
        description:
          'FormApp.ai 提供了大量的 AI 模型、執行器和擴充功能來幫助您自動化工作流程。您還可以在其上自定義您自己的 AI 模型、執行器和 API。',
        name: 'FormApp.ai',
      },
      loop: {
        abort_loop_help: '任一子執行器執行失敗即完全終止循環，未完成的迭代將全數跳過。',
        abort_loop_title: '循环失敗時中止',
        add_action: '添加操作',
        child_actions: '子級執行器',
        child_actions_help: '在每次循環迭代中按順序執行的執行器。',
        description: '通過遍歷數據集並根據預設條件觸發相應的動作,用於自動化處理重複性任務。',
        name: '循環',
        retrieve: '從上游步驟中選擇陣列數據',
        retrieve_description: '選擇上游步驟的輸出數據進行迭代。僅支持陣列類型的數據。',
        sequential_exec: '順序執行',
        sequential_exec_help:
          '按嚴格的索引順序處理數組元素，在開始下一次迭代之前完成當前迭代。取消勾選以啟用並行處理。',
      },
      not_found: '未找到可用的執行器',
      openai_generate_text: {
        apikey: 'API 密鈅',
        description: '使用 OpenAI API 生成文本內容。',
        model: '模型',
        name: 'OpenAI - 生成文本',
        placeholder: '請選擇模型',
        prompt: '提示 (Prompt)',
        timeout_label: '超時時間（單位：秒）',
        timeout_placeholder: '請輸入超時時間，最大值為300秒，缺省時默認為60s',
      },
      random: {
        description: '將輸入的若干選項，隨機選擇一個作為輸出。',
        name: '隨機',
      },
      replace_file: {
        description: '使用數據表中的記錄批量替換文件。',
        name: '替換文件',
      },
      round_robin: {
        description: '將輸入的若干選項，依次循環輸出。',
        name: '輪詢',
      },
      run_script: {
        description: '編寫腳本代碼，執行自定義的操作。支持Python、JavaScript。',
        name: '運行腳本',
      },
      send_email: {
        description: '發送郵件給指定的收件人。支持群發、自定義SMTP等功能。',
        name: '發送郵件',
        opens: '查看率',
        sent: '發送數 ',
      },
      send_report: {
        description: '生成一份報告，發送給指定的成員或群組。',
        name: '發送報告',
      },
      slack_webhook: {
        description: '通過 Slack 應用的 Incoming Webhook，發送消息到指定頻道。',
        name: '發送消息至 Slack 頻道',
      },
      telegram_send_message: {
        description: '通過 Telegram Bot，發送消息給指定用戶或群組。',
        help_text_chat_id: '公共頻道聊天 ID 需要以字符 "@" 開頭，例如 @channel_name',
        name: '發送消息至 Telegram',
      },
      toolsdk_ai: {
        create_and_load_toolsdk: '創建並加載 ToolSDK.ai 應用',
        description:
          'ToolSDK.ai 提供了 2000+ 個 MCP 伺服器和 10000+ 個 AI 工具。讓你可以在自動化流程中輕鬆使用第三方 AI 能力，快速擴展工作流功能。',
        name: 'MCP Server (by ToolSDK.ai)',
      },
      twitter_upload_media: {
        description: '通過 X(Twitter) API 上傳媒體文件，支持圖片和視頻。',
        media: '媒體URL',
        name: 'X(Twitter) - 上傳媒體文件',
      },
      type: '類型',
      update_record: {
        description: '在指定的數據表中，更新一條或多條記錄。',
        name: '更新記錄',
      },
      webhook: {
        description: '發起一個 HTTP 請求，與其他系統進行數據交互。',
        edit: '編輯請求體',
        name: '發送 HTTP 請求',
      },
      wecom_webhook: {
        description: '通過企業微信群機器人的 Webhook URL，發送消息到指定群。',
        name: '發送消息至企業微信群',
      },
      x_create_tweet: {
        auth_method: '認證方法',
        auth_method_help_text: '不同的認證方法需要使用不同的集成實例，請切換認證方法後重新選擇集成類型。',
        auth_method_tooltip: '了解更多關於OAuth 1.0a和OAuth 2.0之間的區別',
        description: '通過 X(Twitter) API，發布一條推文。',
        media_ids: '圖片或視頻',
        media_ids_help_text:
          '給推文添加圖片或視頻，請使用“上傳媒體文件”執行器獲取媒體id，然後通過變量選擇器插入至此處。',
        name: 'X(Twitter) - 創建推文',
        tweet_content: '推文內容',
        x_account_integration: 'X(Twitter)賬戶集成',
      },
    },
    action_help_urls: {
      DINGTALK_WEBHOOK: '/help/reference/automation-action/dingtalk-webhook',
      DINGTALK_WEBHOOK_MARKDOWN: 'https://open.dingtalk.com/document/isvapp/message-type',
      FEISHU_WEBHOOK: '/help/reference/automation-action/feishu-webhook',
      FEISHU_WEBHOOK_MARKDOWN: 'https://open.feishu.cn/document/client-docs/bot-v3/add-custom-bot#5a997364',
      SEND_EMAIL: '/help/reference/integration/smtp-email-account',
      SLACK_WEBHOOK: '/help/reference/automation-action/slack-webhook',
      SLACK_WEBHOOK_MARKDOWN: 'https://api.slack.com/reference/surfaces/formatting',
      TELEGRAM_SEND_MESSAGE: '/help/reference/automation-action/telegram-send-message',
      TELEGRAM_SEND_MESSAGE_MARKDOWN: 'https://core.telegram.org/bots/api#formatting-options',
      TWITTER_UPLOAD_MEDIA: '/help/reference/automation-action/twitter-upload-media',
      WEBHOOK: '/help/reference/automation/webhook-action',
      WECOM_WEBHOOK: '/help/reference/automation-action/wecom-webhook',
      WECOM_WEBHOOK_MARKDOWN: 'https://developer.work.weixin.qq.com/document/path/91770#markdown%E7%B1%BB%E5%9E%8B',
      WECOM_WEBHOOK_TEMPLATE_CARD:
        'https://developer.work.weixin.qq.com/document/path/91770#%E6%A8%A1%E7%89%88%E5%8D%A1%E7%89%87%E7%B1%BB%E5%9E%8B',
      X_CREATE_TWEET: '/help/reference/automation-action/x-create-tweet',
    },
    action_type_intro: {
      DINGTALK_WEBHOOK: '通過釘釘自定義機器人的 Webhook URL，發送消息到指定群。',
      FEISHU_WEBHOOK: '通過飛書自定義機器人的 Webhook URL，發送消息到指定群。',
      SLACK_WEBHOOK: '通過 Slack 應用的 Incoming Webhook，發送消息到指定頻道。',
      TELEGRAM_SEND_MESSAGE: '通過 Telegram Bot，發送消息給指定用戶或群組。',
      WEBHOOK: '構造一個自定義的 HTTP 請求，發送到指定的 URL。',
      WECOM_WEBHOOK: '通過企業微信的 Webhook URL，發送消息到指定群。',
      X_CREATE_TWEET: '通過 Twitter API，發布一條推文。',
    },
    add_action: '添加執行器',
    add_automation: '添加自動化',
    add_trigger: '添加觸發器',
    advanced_options: '進階選項',
    automation: '自動化任務',
    cancel_delay: '取消延遲',
    cancel_delay_content: '終止後將不再執行延遲的後續步驟，確定要終止運行嗎？',
    cancel_delay_success: '取消延遲成功',
    choose_imap_integration: 'IMAP 電子郵件整合',
    choose_integration: '選擇已有的集成',
    close_automation: '關閉自動化',
    closed: '已關閉',
    coming_soon_description: '此功能即將推出！任何建議都可以加快速度！',
    coming_soon_feedback: '感謝您的反饋！',
    coming_soon_placeholder: '請在此留下您的反饋，非常感謝！',
    copy_of: '{name}的副本',
    description_empty: '暫無描述',
    description_help_text: '請提供簡潔明瞭的備註，這將在自動化主界面上顯示，以便於理解。',
    disabled: '關',
    drop_files_here: 'Drop files here',
    duplicate_success: '複製成功',
    email_content_type_label: '郵件內容類型',
    empty_history: '暫無運行歷史',
    empty_step: '未設定自動化步驟',
    empty_step_log: '此步驟無歷史日誌',
    enable: '啟用',
    enabled: '開',
    history: '運行歷史',
    history_status: {
      cancelled: '已取消',
      delay: '延遲',
      failed: '失敗',
      running: '運行中',
      success: '成功',
      timeout: '超時',
    },
    http_if_change: {
      every_failed: '每次失敗',
      first_failed: '首次失敗',
      policy_title: 'HTTP變更時觸發',
      response_changed: '響應變化',
      response_compare_json_path: '響應值比較路徑',
    },
    inbound_email: {
      description_guide: '如何寫搜索條件？',
      label_mailbox_archive: '歸檔',
      label_mailbox_deleted: '已刪除',
      label_mailbox_drafts: '草稿箱',
      label_mailbox_inbox: '收件箱',
      label_mailbox_junk: '垃圾箱',
      label_mailbox_sent: '已發送',
      placeholder_mailbox_name: '輸入文件夾名稱，默認是收件箱。',
      placeholder_search_criteria: '請輸入郵件搜索條件',
      search_rule_description: '更多詳細信息請參閱 {help} 的搜索功能，不填寫默認爲信箱的所有郵件。',
      title_download_attachments: '下載附件',
      title_mailbox_name: '文件夾',
      title_search_criteria: '自定義郵件搜索條件',
      toast_imap_connection_error: 'IMAP 連接失敗，請檢查配置',
    },
    input: '輸入',
    item_output: '循環項輸出',
    label: {
      manual_input: '手動輸入 URL',
      select_integration: '從集成中提取 URL',
    },
    manual_execution_description: '確定要立即執行此自動化流程嗎?',
    manual_execution_success: '自動化流程已開始運行',
    manual_trigger_fields: '動態欄位',
    manual_trigger_fields_desc:
      '自動化運行之前需要動態傳入的欄位。這些欄位可以在整個工作流中被任意執行器通過變量的方式引用。',
    manual_trigger_result: '手動觸發結果',
    manual_trigger_result_desc: '手動觸發結果將顯示在此處',
    max_trigger_count: '觸發器最多只能添加3個',
    no_description: '未設定描述',
    off: '關閉',
    on: '自動',
    open_automation: '開啟自動化',
    output: '輸出',
    parameter_source: '參數來源',
    parameter_source_description: '你可以選擇手動配置參數或重複使用所選整合的設定。',
    parameter_source_option1: '手動配置',
    parameter_source_option2: '使用已有整合配置',
    placeholder_choose_integration: '點擊選擇或添加',
    recent_history: '最近運行',
    recent_history_detail: '詳情',
    recent_history_id: 'ID',
    recent_history_start_time: '開始時間',
    recent_history_status: '狀態',
    record_list: '記錄列表',
    repeat_for_each_in: '對列表中的每一項依序執行下方步驟',
    report_content_description: '報告內容支持 Markdown 語法，請在此輸入報告內容。',
    report_content_type_label: '報告內容類型',
    report_markdown: '報告內容',
    report_markdown_placeholder: '請輸入報告內容',
    report_prompt: '報告提示',
    report_prompt_placeholder: '請輸入報告提示',
    report_subject: '報告主題',
    report_subject_placeholder: '請輸入報告主題',
    report_type: '報告類型',
    round_robin: {
      label: {
        select_action: '選擇執行器',
        select_database: '選擇資料庫',
        select_target: '目標',
        select_view: '選擇視圖',
      },
      type: {
        database: '資料庫',
        database_view: '資料庫視圖',
        prev_action: '上游的執行器',
        round_type: '輪詢類型',
        user: '用戶',
      },
    },
    run: {
      failed: '失敗',
      status: '執行{status}，時間：{time}',
      success: '成功',
    },
    run_immediately: '立即運行',
    run_immediately_description: '此自動化流程將立即運行一次，並且不會再次運行。',
    run_test: {
      description: '運行測試以驗證自動化流程的配置是否正確。',
      preview_button: '預覽',
      run_button: '運行測試',
      title: '運行測試',
    },
    script: '腳本',
    script_language: '代碼語言',
    send_message_to_slack: '發送訊息到 Slack',
    sort: '排序',
    started: '已啟動',
    status: '狀態',
    then: '然後',
    title_manual_execution: '運行自動化流程',
    tooltip_learn_more: '了解更多',
    tooltips: {
      step_error: '當前步驟中缺少必需的配置項，此步驟可能無法正常工作，請檢查',
    },
    trigger: {
      add_action: '添加執行器',
      add_trigger: '添加觸發器',
      button_clicked: {
        description: '點擊數據表按鈕欄位中的按鈕時執行操作。',
        name: '按鈕欄位點擊時',
      },
      datetime_field_reached: {
        ahead: '提前',
        days: '天數',
        delay: '延後',
        description: '當記錄中的某個日期欄位接近或達到特定日期時自動執行操作。',
        hours: '小時',
        minutes: '分鐘',
        name: '日期欄位到期觸發',
        offset: '偏移量',
        offset_day: '提前或延後若干天',
        offset_day_placeholder: '負數表示提前的天數，正數表示延後的天數，0 表示當天',
        offset_hour: '提前或延後若干小時',
        offset_hour_placeholder: '負數表示提前的小時數，正數表示延後的小時數，0 表示當前小時',
        offset_minute: '提前或延後若干分鐘',
        offset_minute_placeholder: '負數表示提前的分鐘數，正數表示延後的分鐘數，0 表示當前分鐘',
        today: '當天',
      },
      description: '備註',
      dummy_trigger: {
        description: '用於測試和驗證自動化流程觸發條件。',
        name: '模擬觸發器',
      },
      form_submitted: {
        description: '當有新的表單提交時自動觸發。',
        name: '有新的表單提交',
      },
      http_if_change: {
        description: '當檢測到 HTTP 響應內容發生變化時自動觸發。',
        name: 'HTTP 響應體發生變更',
      },
      inbound_email: {
        description: '當收到特定郵件時自動觸發。',
        name: '收到特定郵件',
      },
      manually: {
        description: '用戶手動點擊觸發時執行操作。',
        name: '手動觸發',
      },
      member_joined: {
        description: '新成員加入空間站時執行操作。',
        name: '有新的成員加入',
      },
      not_found: '觸發器未找到',
      record_created: {
        description: '數據表新增記錄時執行操作。',
        name: '有新的記錄創建',
      },
      record_match: {
        description: '新增或編輯的記錄滿足條件時執行操作。',
        name: '有記錄滿足條件',
      },
      scheduler: {
        at_least_one_option: '至少選擇一個選項',
        description: '當達到設定時間時自動執行操作。',
        name: '定時任務',
      },
      select_database: '選擇數據表',
      select_form: '選擇表單',
      select_form_have_not: '當前沒有表單可供選擇',
      select_form_note: '該觸發器需要選擇表單才可以完成配置',
      select_form_placeholder: '請選擇一張表單',
      select_match: '匹配條件',
      triggers: '觸發器',
      type: '觸發器類型',
      webhook_received: {
        description:
          '當外部系統需要將數據發送到 Bika.ai 時使用。Webhook 提供一個唯一的網址鏈接，允許第三方應用程序（如電商平台、CRM系統等）在特定事件發生時自動向 Bika.ai 傳遞信息，從而觸發相應的自動化流程。',
        name: 'Webhook 觸發',
        placeholder: '儲存後將產生唯一的 Webhook 連結',
        url: 'Webhook 鏈接',
      },
    },
    trigger_creation_success: '觸發器已建立',
    trigger_help_urls: {
      form_submit: '/help/reference/node-resource/form',
      inbound_email: '/help/reference/automation-trigger/inbound-email',
    },
    trigger_history_empty: '此次運行由用戶手動觸發，無觸發器歷史',
    trigger_update_success: '觸發器已更新',
    updating: '更新中',
    variable: {
      placeholder: '輸入"/"可插入變量',
    },
    variable_select: {
      automation: {
        id: '自動化 ID',
        interrupt_url: '中斷延遲自動化請求URL',
        name: '自動化名稱',
        run_history_id: '運行ID',
      },
      choose: '選擇',
      createdAt: '創建時間',
      data_empty: '數據為空',
      database: {
        id: '數據庫 ID',
        name: '數據庫名稱',
        url: '數據庫 URL',
      },
      databaseId: '數據表 ID',
      database_title: '數據庫',
      field: {
        clear_all: '清除所有',
        data: '欄位數據',
        data_tips: '欄位原始數據，用於更新記錄或腳本運算。',
        doc_id: '文檔 ID',
        doc_name: '文檔名稱',
        id: '欄位 ID',
        join: '連接',
        name: '欄位名稱',
        select_all: '全選',
        type: '欄位類型',
        value: '欄位值',
        value_tips: '格式化後的欄位數據，適用於介面顯示或插入至郵件/文本。',
      },
      fields: '欄位',
      getting_variable: '獲取變量中…',
      global: {
        key: '全域',
        no_result_found: '暫無搜尋記錄',
        official_website: '官方網站',
      },
      id: 'ID',
      insert: '插入',
      item: '項',
      item_actions: '項操作',
      item_description: '項描述',
      item_title: '項',
      member_email: '成員郵箱',
      member_id: '成員 ID',
      member_ids: '成員 ID 列表',
      member_ids_tips: '由成員ID組成的陣列，可使用循環執行器逐一處理',
      member_name: '成員名稱',
      member_user_id: '成員用戶 ID',
      members: '成員列表',
      members_length: '成員數量',
      members_tips: '多名成員組成的合集，可使用循環執行器逐一處理',
      menu_insert_variable_title: '插入變量',
      name: '名稱',
      other_title: '其他',
      placeholder: '選擇變量',
      primary_component: '主要組件',
      record: {
        cell: '單元格',
        grid_list: '網格列表(文字)',
        grid_tips: '將記錄格式化為文字表格',
        id: '記錄 ID',
        list_tips: '將記錄格式化為文字列表',
        record_id_list: '記錄 ID 列表',
        record_id_list_tips: '由記錄ID組成的陣列，可使用循環執行器逐一處理',
        record_list: '記錄列表(文字)',
        record_tips: '當前操作的記錄，可以單獨選擇其中的某個欄位',
        records: '記錄',
        records_length: '記錄數量',
        records_tips: '由多條記錄組成的陣列，可使用循環執行器逐一處理',
        selected_fields: '選擇欄位',
        url: '記錄 URL',
      },
      recordId: '記錄 ID',
      record_title: '記錄',
      resume_time: '恢復時間',
      resume_time_tips: '流程將在此時間點恢復運行',
      revision: '版本號',
      round_robin_item: '輪詢項',
      run_time: '執行時間',
      run_time_tips: '自動化流程被觸發的時間',
      select_data_placeholder: '請選擇數據',
      select_data_title: '選擇數據',
      select_variable: '選擇',
      space: {
        home_page_url: '空間站主頁 URL',
        id: '空間站ID',
        name: '空間站名稱',
        report_page_url: '我的報告頁面 URL',
        todo_page_url: '我的待辦頁面 URL',
      },
      updatedAt: '更新時間',
      url: 'URL',
      variable_component: '變量組件',
    },
    webhook: {
      add_field: '添加欄位',
      add_header: '添加請求頭',
      description_guide: '配置教程',
      description_message_content: '支持 Markdown 格式，輸入"/"可插入變量',
      description_webhook_source:
        '當前的操作需要輸入Webhook URL作為消息發送的目標地址。你可以選擇手動填入Webhook URL，也可以從空間站已有的集成中選擇一個進行複用。',
      feishu_type_interactive: '消息卡片',
      feishu_type_post: '富文本',
      feishu_type_text: '文本訊息',
      field_value: '欄位值',
      message_type_actioncard: 'ActionCard',
      message_type_link: '連結',
      message_type_templatecard: '模板卡片',
      message_type_text: '文本消息',
      placeholder_field_name: '輸入欄位名稱',
      placeholder_field_value: '輸入欄位數值',
      placeholder_header_name: '名稱',
      placeholder_header_value: '數值',
      placeholder_request_method: '選擇請求方法',
      placeholder_request_url: '輸入請求URL',
      placeholder_webhook_source: '請選擇一種Webhook來源',
      placeholder_webhook_url: '請輸入Webhook URL',
      support_format: '支援的格式',
      title_body_type: '內容類型',
      title_content_type: '內容類型',
      title_form_data: '表單數據',
      title_message_content: '消息內容',
      title_message_title: '消息標題',
      title_message_type: '消息類型',
      title_request_content: '請求內容',
      title_request_headers: '標頭（Headers）',
      title_request_method: '請求方法',
      title_request_url: 'URL',
      title_webhook_source: 'Webhook 來源',
      title_webhook_url: 'Webhook URL',
      webhook_json_error: 'JSON 格式錯誤',
    },
    when: '當',
  },
  avatar: {
    avatar: '頭像',
    cancel_select: '取消選擇',
    change_avatar: '變更頭像',
    edit_image: '編輯',
    file_tip: '支持 JPG、PNG 和 GIF 格式，圖片大小需在 2 MB 以內',
    gif_no_crop_tip: 'GIF 檔案將不進行裁剪以保持動畫效果',
    paste_image_link: '粘貼圖片鏈接...',
    preview: '預覽',
    preview_avatar: '頭像預覽',
    re_select: '重新選擇',
    select_from_gallery: '從相冊選擇',
    tab_color: '顏色',
    tab_link: '鏈接',
    tab_link_tip: '適用於網絡上的任何圖像。',
    tab_upload: '上傳',
    take_photo: '拍照',
    upload_avatar: '上傳圖片',
  },
  brand: {
    about_brand: '關於 Bika.ai',
    brand: 'Bika.ai',
    website: '官方網站',
  },
  buttons: {
    add: '添加',
    add_virtual_intelligent_task: '添加虛擬智能任務',
    back: '返回',
    back_to_space: '返回我的空間站',
    cancel: '取消',
    change_bound_email: '更改綁定郵箱',
    close: '關閉',
    completed: '已完成',
    confirm: '確認',
    create: '新建',
    create_space: '創建空間站',
    delete: '刪除',
    edit: '編輯',
    more: '更多...',
    pre_fill_title_btn: '預填充',
    remove: '移除',
    run: '運行',
    save: '儲存',
    see_more: '查看更多',
    send_your_suggestion: '發送您的建議',
    submit: '提交',
    view_all: '檢視全部',
  },
  cancel: '取消',
  coming_soon: '敬請期待',
  components: {
    breadscrumb: {
      root: '根目錄',
    },
    configure_multilingual: '配置多語言',
    confirm_remove_multilingual_configuration: '確認要刪除多語言配置嗎？',
    disable_multilingual_warning: '關閉後將清空原有內容',
    enable_multilingual_warning: '啟用將清空內容重新配置語言',
    remove_multilingual_configuration_warning: '一旦刪除後，則不能恢復，只保留一種語言，如需增加需要重新配置。',
    view_all_languages: '查看所有語言',
  },
  confirm: '確認',
  copy: {
    copy: '複製',
    copy_link: '複製連結',
    copy_link_to_clipboard: '複製連結到剪貼簿',
    copy_success: '複製成功',
    create_short_url: '創建短链接',
    delete_short_url: '刪除短連結',
  },
  dashboard: {
    add_widget: '添加組件',
    select_data_source: '選擇數據源',
    widget_not_editable: '該組件不支持修改。',
  },
  dashboard_widgets: {
    ai_widget: {
      description: 'A Widget that AI-generated content',
      name: 'AI Widget',
    },
    bika: {
      description: '顯示bika的組件',
      name: 'bika',
    },
    chart: {
      description: '將表格內的數據可視化為柱狀圖、條形圖、折線圖、散點圖、餅狀圖等多種展示形式',
      name: '圖表',
    },
    embed: {
      description: '輸入一個網址即可將其他網站的內容嵌入',
      name: '嵌入',
    },
    icons: {
      description: '顯示圖標的組件',
      name: '圖標',
    },
    list: {
      description: '顯示列表的組件',
      name: '列表',
    },
    number: {
      description: '統計表格內任一列數據，並將統計值以突出的樣式顯示在組件上',
      name: '高亮數字',
    },
    pivot_table: {
      description: '一個快速對明細數據表進行分類匯總的數據分析工具',
      name: '透視表',
    },
    progress_bar: {
      description: '顯示進度條的組件',
      name: '進度條',
    },
    text: {
      description: '顯示文本的組件',
      name: '文本內容',
    },
  },
  data: {
    data: '資料',
    database: '資料庫',
  },
  database_fields: {
    ai_photo: {
      description: 'AI自動生成的圖片和圖像內容，適用於產品展示、社交媒體等場景',
      name: 'AI圖片',
    },
    ai_text: {
      ai_write: 'AI 生成',
      ai_writing: 'AI 正在寫入...',
      auto_update: '自動更新',
      description: 'AI自動生成文本內容，可引用表內數據，適用於客服回覆、產品描述、內容摘要等場景，提升創作效率',
      llm_provider: 'AI 服務提供商',
      name: 'AI文本',
      preview_btn: '預覽',
      preview_empty: '預覽結果為空',
      preview_result: '預覽結果',
      preview_result_description: '預覽結果基於此表中的第一條記錄',
      prompt_description: '告訴 AI 應該如何生成消息內容，輸入"/"可插入表中的欄位',
      prompt_menu_title: '選擇欄位',
      prompt_title: '提示（Prompt）',
    },
    ai_video: {
      description: 'AI自動生成的視頻和動畫內容，適用於廣告宣傳、社交媒體等場景',
      name: 'AI視頻',
    },
    ai_voice: {
      description: 'AI自動生成的語音和音頻內容，適用於語音助手、播客等場景',
      name: 'AI語音',
    },
    api: {
      description: '存儲API接口信息，用於與外部系統交互和數據交換',
      name: 'API',
    },
    attachment: {
      adaptive: '自適應',
      close: '關閉',
      copy_link: '複製連結',
      copy_link_success: '複製連結成功',
      delete: '刪除',
      description: '允許上傳和存儲各種類型的文件作為記錄的附件，如文檔、圖片或壓縮包',
      download: '下載',
      download_success: '下載成功',
      initial_size: '初始大小',
      name: '附件',
      rotate: '旋轉',
      zoom_in: '放大',
      zoom_out: '縮小',
    },
    auto_number: {
      description: '自動為每條新記錄生成唯一的序列號，適用於訂單編號、工單號等場景',
      name: '自動編號',
    },
    base: {
      field_description: '欄位描述',
      field_name: '欄位名稱',
      field_type: '欄位類型',
      field_type_placeholder: '請選擇列類型',
    },
    button: {
      description: '創建可點擊的交互式按鈕，點擊後可觸發預設的自動化操作或事件',
      name: '按鈕',
    },
    cascader: {
      description: '提供多級聯動的下拉選擇菜單，適用於有層級關係的數據選擇，如地區選擇',
      name: '級聯選擇',
    },
    checkbox: {
      description: '提供是/否選項的複選框，適用於狀態標記或簡單的布爾值選擇',
      name: '複選框',
    },
    created_by: {
      description: '自動記錄創建該條記錄的用戶信息，便於追蹤記錄來源',
      name: '創建者',
    },
    created_time: {
      description: '自動記錄該條記錄被創建的具體日期和時間',
      name: '創建時間',
      property: {
        auto_fill: '自動填充',
        date_format: '日期格式',
        date_format_placeholder: '請選擇日期格式',
        show_time: '顯示時間',
        time_format: '時間格式',
      },
    },
    currency: {
      description: '專門用於存儲和格式化貨幣金額，支持不同貨幣符號和精度設置',
      name: '貨幣',
      property: {
        accuracy: '精度',
        alignmen_default: '默認對齊',
        alignmen_left: '左對齊',
        alignmen_right: '右對齊',
        alignment: '符號對齊方式',
        symbol: '符號',
        symbol_placeholder: '請輸入貨幣符號',
      },
    },
    cut_video: {
      description: '存儲經過剪輯或編輯的視頻片段，保留編輯信息和時間軸',
      name: '視頻剪輯',
    },
    daterange: {
      description: '存儲時間段或日期範圍，包含開始和結束兩個時間點，適用於項目週期、活動時間等',
      name: '日期範圍',
      property: {
        date_format: '日期格式',
        date_format_placeholder: '請選擇日期格式',
        show_time: '顯示時間',
        time_format: '時間格式',
      },
    },
    datetime: {
      description: '存儲精確的日期和時間信息，適用於需要記錄確切時間點的場景',
      name: '日期時間',
      property: {
        auto_fill: '新增記錄自動填寫創建時間',
        date_format: '日期格式',
        date_format_placeholder: '請選擇日期格式',
        show_time: '顯示時間',
        time_format: '時間格式',
      },
      repeat_day: '按天',
      repeat_hour: '按小時',
      repeat_minute: '按分鐘',
      repeat_month: '按月',
      repeat_week: '按週',
      repeat_year: '按年',
    },
    email: {
      description: '專門用於存儲電子郵件地址，適用於聯繫人信息、通知等場景',
      name: '郵箱',
    },
    formula: {
      description: '通過公式自動計算值，可引用其他欄位並進行數學或邏輯運算',
      name: '公式',
      property: {
        expression: '公式',
        expression_placeholder: '請輸入公式',
      },
    },
    json: {
      description: '存儲結構化的JSON格式數據，適用於複雜數據結構或API響應內容',
      name: 'JSON',
    },
    link: {
      description: '創建與其他表的雙向關聯，實現表間數據的互相引用和關係維護',
      name: '關聯',
      property: {
        relation_database: '關聯表',
      },
    },
    long_text: {
      description: '用於存儲長篇文本內容，如詳細描述、註釋或文章正文',
      name: '多行文本',
    },
    lookup: {
      description: '從關聯表中自動查找並顯示特定欄位的值，實現數據的動態引用',
      name: '查找引用',
      property: {
        error: '未找到對應的數據庫',
        lookup_field: '查詢的欄位',
        select_link_database: '選擇關聯表',
      },
    },
    member: {
      description: '存儲系統成員信息，可選擇單個或多個成員作為欄位值',
      name: '成員',
      property: {
        allow_multiple: '允許添加多個成員',
        notify_mentioned: '選擇成員後發送消息通知',
      },
    },
    modified_by: {
      description: '自動記錄最後一次修改該記錄的用戶信息',
      name: '修改者',
    },
    modified_time: {
      description: '自動記錄該記錄最後一次被修改的日期和時間',
      name: '修改時間',
      property: {
        auto_fill: '自動填充',
        date_format: '日期格式',
        date_format_placeholder: '請選擇日期格式',
        show_time: '顯示時間',
        time_format: '時間格式',
      },
    },
    multi_select: {
      description: '從預定義的選項列表中選擇多個選項，適用於多標籤分類',
      name: '多選',
      property: {
        add_options: '添加一個選項',
        default_value: '默認值',
      },
    },
    number: {
      description: '存儲數值型數據，支持整數和小數，可設置精度和格式',
      name: '數字',
      property: {
        custom_units: '自定義單位',
        custom_units_default: '請輸入單位名稱',
        precision: '精度',
        thousand_separator: '千分位',
      },
    },
    one_way_link: {
      description: '創建與其他表的單向關聯，只能從當前表查看關聯表的數據',
      name: '單向鏈接',
    },
    percent: {
      description: '存儲百分比值，自動格式化顯示為百分比形式，適用於進度、比率等場景',
      name: '百分比',
      property: {
        default_value: '默認值',
        precision: '精度',
      },
    },
    phone: {
      description: '專門用於存儲電話號碼，適用於聯繫人信息、客戶資料等場景',
      name: '電話',
    },
    photo: {
      description: '存儲和顯示圖片文件，支持預覽和縮略圖功能',
      name: '照片',
    },
    rating: {
      description: '以星級或數值形式存儲評分信息，可視化展示評價等級',
      name: '評分',
      property: {
        icon_settings: '圖標設置',
        max_value: '最大值',
      },
    },
    single_select: {
      description: '從預定義的選項列表中選擇單一選項，適用於狀態或分類場景',
      name: '單選',
      property: {
        add_options: '添加一個選項',
        default_value: '默認值',
      },
    },
    single_text: {
      description: '存儲簡短的單行文本，適用於標題、名稱等簡潔信息',
      name: '單行文本',
    },
    url: {
      description: '存儲網頁鏈接地址，支持直接跳轉鏈接進行訪問',
      name: '網址',
    },
    video: {
      description: '存儲視頻文件，支持上傳、預覽和播放功能',
      name: '視頻',
    },
    voice: {
      description: '存儲音頻文件，支持錄製、上傳和播放功能',
      name: '語音',
    },
    work_doc: {
      description: '存儲支持Markdown格式的富文本文檔，可直接在單元格內創建和編輯文檔內容，',
      name: '文檔',
    },
  },
  database_views: {
    form: {
      description:
        '表單視圖允許使用者創建自訂表單，方便數據輸入和收集。使用者可以通過分享表單鏈接，收集來自外部使用者的數據，並自動將收集到的數據添加到系統中。適用於需要簡化數據輸入和收集過程的場景',
      name: '表單',
    },
    gallery: {
      description: '相冊視圖是將記錄以卡片的形式展示，以記錄附件中的圖片作為封面，適用於名片、物料和菜單等場景',
      name: '相冊',
    },
    gantt: {
      description:
        '甘特視圖以時間軸的方式展示項目進度，使用者可以直觀地查看任務的開始和結束時間，以及各任務之間的依賴關係。適用於需要有效計劃和管理項目進度的場景',
      name: '甘特圖',
    },
    kanban: {
      description:
        '看板視圖以卡片形式展示數據，每一列代表一個狀態或分類。使用者可以通過拖放卡片在不同列之間移動來反映任務或項目的進展情況。適用於需要直觀跟蹤工作流程和任務進度的場景',
      name: '看板',
    },
    table: {
      description:
        '表格視圖提供了一種電子表格樣式的佈局，使用者可以以結構化的方式查看和管理數據。每一列代表一個欄位，每一行代表一條記錄，便於快速瀏覽、篩選和排序數據。適用於需要清晰、有條理地管理和操作大量數據的場景',
      name: '表格',
    },
  },
  delete: {
    confirm_deletion: '確認刪除',
    confirm_to_delete_content: '確認刪除此內容?',
    confirm_to_delete_this_link: '確認刪除此連結?',
    confirm_to_delete_title: '確認是否刪除',
    delete: '刪除',
    delete_success: '删除成功',
  },
  document: {
    code_placeholder: '輸入代碼',
    list_placeholder: '輸入列表項',
    status_connected: '已連接',
    status_connecting: '連接中',
    status_disconnected: '未連接',
    text_placeholder: '輸入 "/" 插入',
    title_placeholder: '輸入標題',
  },
  editor: {
    add_button_text: '新增',
    add_column_as_sort_condition: '可以添加引用欄位或引用欄位所在表任意欄位作為排序條件',
    add_condition: '添加條件',
    add_filter_condition: '設定篩選條件',
    add_skill: '添加',
    add_sort_condition: '設定排序條件',
    aggegate_records: '對引用資料進行統計',
    all_count: '全部計數',
    all_record: '全部',
    and_cal: '與運算',
    and_condition: '並且「{field}」{condition}「{value}」',
    approval: '审批',
    average: '平均值',
    button_refresh: '刷新',
    by_filter_and_sort: '對引用欄位進行篩選和排序',
    card_style: '卡片樣式',
    collapse: '摺疊',
    collapse_all_group: '折疊所有群組',
    collapse_group: '折疊分組',
    column_required: '{name}是必填欄位',
    concat_as_text: '連接成文本',
    concat_by_semicon: '用分號連接',
    condition: '當「{field}」{condition}「{value}」',
    content_paste_failure: '粘貼失败',
    content_paste_successfully: '粘貼成功',
    count_sort_and_filter_confition: '{filterConditionCount}個篩選條件和{sortConditionCount}個排序條件',
    create_mission_line_text: '創建智能任務',
    create_record_line_text: '創建記錄',
    current_data_not_allowed: '數據無法寫入該單元格',
    custom_skill: '自定义技能',
    data_import_process: '{Percent} 資料上傳中，暫時不可更新資料',
    delete_n_records_in_list: '刪除列表所選{count}條記錄',
    delete_record_warning_content: '您確定要刪除嗎',
    enable: '启用',
    excel_import_success: '已成功導入{Count}條數據',
    expand_all_group: '展開所有群組',
    expand_group_or_sub_group: '展開分組/子分組',
    filter_no_nil: '過濾所有空值',
    find_records_line_text: '查找',
    first_record: '第一個記錄',
    grid_row_height: '行高',
    grid_row_height_default: '預設',
    grid_row_height_extra_large: '超高',
    grid_row_height_large: '高',
    grid_row_height_medium: '中等',
    grid_row_height_seeting: '高度設置',
    integration_line_text: '配置整合',
    item_not_supported_currently: '該選項暫不支援',
    lookup_count: '引用數量',
    lookup_original_values: '從關聯表原樣引用數據',
    maximal_value: '最大值',
    microphone_disabled: '麥克風使用權限被拒絕。請更新瀏覽器的權限設定後重試。',
    minimal_value: '最小值',
    no_search_result: '暫無匹配搜尋結果',
    not_nil_count: '非空數值計數',
    not_null_count: '非空值計數',
    number_value_format: '數值格式',
    operate_successfully: '操作成功',
    or_calulate: '或運算',
    or_condition: '或者「{field}」{condition}「{value}」',
    original_values: '原始值',
    please_add_link_field: '請先添加關聯字段(LINK, ONEWAY_LINK)',
    please_input_data: '請輸入資料',
    please_select_a_skillset: '请先选择一个技能集',
    please_select_at_least_one_skill: '请至少选择一个技能',
    please_select_configuration: '請選擇配置',
    record_filter_out_tips: '被篩選過濾的記錄不會被引用',
    remove_dulplicated: '去重',
    select_skill: '請為 AI Agent 添加技能',
    show_cover: '顯示封面',
    show_field_name: '顯示欄位名称',
    show_field_name_help_text: '在卡片上顯示欄位名稱。如果禁用，則只顯示欄位值。',
    show_logo: '顯示標誌',
    show_time_zone_info: '顯示時區信息',
    skillsets: '技能集',
    start_paste: '正在貼上...',
    stop_microphone: '停止',
    sum_value: '總和',
    sum_value_tooltip: '返回所有值的總和n SUM(1, 3, 5, "", "Apple") => 9 (1+3+5)',
    symbol_align_strategy: '符號對齊方式',
    table_lock_message: '暫無法更新，請稍後再試',
    text_area_tips: 'Enter 換行, Shift + Enter 結束編輯',
    this_field_configuration_missing: '這個欄位存在配置錯誤。請檢查公式或欄位使用的配置。',
    this_field_not_allow_edit: '這個欄位不允許被編輯',
    upload_image: '上傳圖片',
    upload_image_banner_size: '建議尺寸: 1440*480',
    use_micro_phone: '使用麥克風',
    xor_calculate: '異或運算',
    you_have_no_saved_change: '未儲存的更改',
    you_have_no_saved_change_content: '你確定要關閉此面板嗎？你的更改將不會被儲存。',
  },
  email: {
    bcc: '密送',
    body: '正文',
    cc: '抄送',
    from_email: '發件人郵箱',
    from_name: '發件人名稱',
    help_text_reply_to: '收件人回覆郵件時，會發送至此郵箱地址。',
    provider: {
      service: 'Bika 電子郵件服務',
      smtp: '手動填寫 SMTP',
      smtp_integration: '使用 SMTP 集成',
    },
    provider_type: '發送方式',
    recipient: '收件人',
    reply_to: '回覆至',
    send_email: '發送郵件',
    smtp_host: 'SMTP 主機',
    smtp_password: 'SMTP 密碼',
    smtp_port: 'SMTP 端口',
    smtp_username: 'SMTP 用戶名',
    subject: '主題',
  },
  error: {
    back: '返回上一頁',
    back_to_home: '返回主頁',
    error: '錯誤',
    error_code: '錯誤代碼',
    error_description: '錯誤描述',
    error_message: '錯誤訊息',
    export: {
      record_limit: '當前數據表記錄超過 50,000 行，無法進行導出操作',
    },
    node_server_error: '當前節點被刪除或者不存在',
    oops: '哎呀!',
    page_error: '頁面發生了一些錯誤',
    page_not_found: '頁面未找到',
    page_not_found_description: '抱歉，您訪問的頁面不存在。嘗試後退到上一頁或返回主頁?',
    screen_not_found: '未找到畫面',
    space: {
      back: '返回我的空間站',
      description: '請檢查鏈接是否正確，並確保您有訪問權限。如果您有任何問題，請聯繫任務發起人。',
      title: '您沒有權限查看此鏈接的內容',
    },
  },
  explore: {
    explore: '探索',
  },
  filter: {
    and: '並且',
    contains: '包含',
    date_after_or_equal: '晚於等於',
    date_before_or_equal: '早於等於',
    date_range: '日期範圍',
    does_not_contains: '不包含',
    equal: '等於',
    exact_date: '精確日期',
    function_date_time_after: '晚於',
    function_date_time_before: '早於',
    is_empty: '為空',
    is_not_empty: '不為空',
    is_repeat: '有重複',
    name: '篩選',
    not_equal: '不等於',
    or: '或者',
    previous_month: '上月',
    previous_week: '上週',
    search: '搜尋',
    settings_descritpion: '視圖更改尚未保存，只會對你生效',
    settings_name: '篩選設置',
    some_day_after: '多少天後',
    some_day_before: '多少天前',
    start_end_date: '開始時間 - 結束時間',
    the_last_month: '過去 30 天',
    the_last_week: '過去 7 天',
    the_next_month: '未來 30 天',
    the_next_week: '未來 7 天',
    this_month: '本月',
    this_week: '本週',
    this_year: '今年',
    today: '今天',
    tomorrow: '明天',
    where: '當',
    yesterday: '昨天',
  },
  formula: {
    abs: {
      description:
        '簡介\n取數值的絕對值。\n\n參數說明\nvalue：是要對其求絕對值的數值。\n絕對值：正數的絕對值是本身，負數的絕對值是去掉負號。',
      example:
        '// value > 0\n公式：ABS(1.5)\n運算結果：1.50\n\n//value = 0\n公式：ABS(0)\n運算結果：0.00\n\n// value < 0\n公式：ABS(-1.5)\n運算結果：1.50',
      name: 'Abs',
    },
    and: {
      description:
        '如果所有參數均為真（true），則返回真（true），否則返回假（false）。\n\n【logical】是邏輯參數，可以是邏輯值、數組或引用的欄位',
      example: 'AND(3>2, 4>3)\n=> true',
      name: 'And',
    },
    array: '數組',
    array_compact: {
      description:
        '從數組中刪除空字符串和空值。\n\n【item】表示數組值，比如多選、附件、關聯和查找引用欄位類型的單元格值。\n\n本函數會保留“ false”值和空白字符的字符串',
      example: 'ARRAYCOMPACT([1,2,"",3,false," ", null])\n=> [1,2,3,false," "]',
      name: 'Array Compact',
    },
    array_flatten: {
      description:
        '通過刪除任何數組嵌套來平鋪數組。 所有數據都成為同一個數組的元素。\n\n【item】表示數組值，比如多選、附件、關聯和查找引用欄位類型的單元格值。',
      example: 'ARRAYFLATTEN([1, 2, " ", 3, ],[false])\n=> [1, 2, 3 ,false]',
      name: 'Array Flatten',
    },
    array_join: {
      description:
        '將匯總數組中的所有值拼接成一個帶分隔符的字符串。\n\n【item】表示數組值，比如多選、附件、關聯和查找引用欄位類型的單元格值。',
      example: 'ARRAYJOIN({興趣愛好} , "; ")',
      name: 'Array Join',
    },
    array_unique: {
      description: '僅返回數組中的唯一項。\n\n【item】表示數組值，比如多選、附件、關聯和查找引用欄位類型的單元格值。',
      example: 'ARRAYUNIQUE([1,2,3,3,1])\n=> "[1,2,3]',
      name: 'Array Unique',
    },
    average: {
      description:
        '返回多個數值的算術平均數。\n\n【number...】是進行運算的數值參數，可以輸入數字或引用數值類型的列。數值類型的列包括數字、貨幣、百分比、評分等。\n\n如果其中某個參數是文本值，比如""八""，在運算時會被當做0。',
      example: 'AVERAGE(2, 4, "6", "八") => (2 + 4 + 6) / 4 = 3',
      name: 'Average',
    },
    blank: {
      description: '返回一個空值。\n\n可以用來判斷單元格是否為空，見例子一；\n可以在單元格內填入空值，見例子二；',
      example: 'IF({開始時間} = BLANK(), "尚未確定時間", "已確定開始時間")\n\nIF({數學成績} ≥ 60, BLANK(), "需要補考")',
      name: 'Blank',
    },
    ceiling: {
      description:
        '將數值向上捨入為最接近的指定基數的倍數。\n\n【value】是要向上捨入的值。\n【significance】非必填，是用於向上捨入的基數，返回值為基數的倍數。如果未提供，默認取1。\n【向上捨入】即它返回值是大於或等於原數值,且為最接近的基數的倍數。',
      example: 'CEILING(1.99)\n=> 2\n\nCEILING(-1.99, 0.1)\n=> -1.9',
      name: 'Ceiling',
    },
    concatenate: {
      description:
        '將多個文本值串聯成單個文本值。（其效果等同於 &）\n\n【text1..】是要串聯的多個值，可以輸入文本、數字、日期參數或者引用列數據。\n\n請用雙引號將你要串聯的文本值引起來，數字和引用列除外。\n特例：如果要串聯雙引號，你需要使用反斜線（\\）作為轉義字符。',
      example: 'CONCATENATE({姓名}, {年紀}, "歲")\n\nCONCATENATE("\\"", {年紀}, "\\"")',
      name: 'Concatenate',
    },
    count: {
      description:
        '統計「數字」類型值的數量。\n\n【number】可以是輸入的參數或引用的列。\n\n本函數可以計算輸入的參數或單元格內包含了多少個數值（數字、貨幣、百分比、評分都為數值）。',
      example: 'COUNT(1, 3, 5, "", "七")\n=> 3',
      name: 'Count',
    },
    count_a: {
      description:
        '統計非空值的數量。\n\n【textOrNumber】可以是輸入的參數或引用的列。\n\n本函數可以計算輸入的參數或單元格內包含了多少個非空值。\n比如，可以統計一個單元格內有多少個選項，多少個圖片。多少個成員等。\n還可以統計查找引用的單元格內的數組非空值。',
      example: 'COUNTA(1, 3, 5, "", "七")\n=> 4',
      name: 'CountA',
    },
    count_all: {
      description:
        '統計所有值的數量，包括空值。\n\n【textOrNumber】可以是輸入的參數或引用的列。\n\n本函數可以計算輸入的參數和單元格內包含了多少個值，包括空值。',
      example: 'COUNTALL(1, 3, 5, "", "七")\n=> 5',
      name: 'CountAll',
    },
    count_if: {
      description:
        '在values中統計keyword出現的次數。\n\nvalues：指定從哪裡查找數據。支持數組類型或文本類型的數據。\nkeyword：要查找並統計的關鍵詞。\noperation：比較符，非必填項。你可以填入條件符號大於">"，小於"<"，等於"="，不等於"!="，如果不填寫默認為等於。\n例子一中沒有填寫比較符，默認統計等於"A"的值出現的次數。\n例子二中填寫了比較符">"，意味統計大於"2"的值出現的次數。\n\n使用場景：\n1）可以統計一串文本數組[A, B , C , D, A]中，字符"A"出現的數量為2，見例子一。\n2）可以統計一串數字數組[1, 2, 3, 4, 5]中，大於3的數字數量為2，見例子二。\n3)可以統計一串文本字符串"吃葡萄不吐葡萄皮"中，"葡萄"出現的次數為2，見例子三。',
      example: 'COUNTIF({評級}, "A")\n=> 2\n\nCOUNTIF({得分}, 3, ">")\n=> 2\n\nCOUNTIF({順口溜}, "葡萄")\n=> 2\n',
      name: 'CountIf',
    },
    created_time: {
      description: '返回記錄的創建時間',
      example: 'CREATED_TIME()\n=> "2024-06-10"\n\n"創建於 ：" & CREATED_TIME()',
      name: 'Created Time',
    },
    date: '日期',
    date_add: {
      description:
        '簡介\n為指定的日期增加固定的時間間隔。\n\n參數說明\ndate：是你指定的日期。本函數將在該日期基礎上增加一定的時間間隔。\ncount：是時間間隔，支持輸入帶正負號的數字。如果為正數，即表示增加幾天（可自定義計時單位），見例子一；如果為負數，即表示減少幾天，見例子二；\nunits：是計時單位，即增加時間間隔的單位。比如按 “天” 計算也可以轉換為按 “年” 計算。\n\n計時單位包括以下符號，兩種格式都可以使用：「單位說明符 」→ 「縮寫」\n毫秒：「milliseconds」 → 「ms」\n秒：「seconds」 → 「s」\n分鐘：「minutes」 → 「m」\n小時：“hours” → “h”\n天：“days” → “d”\n週：“weeks” → “w”\n月：“months” → “M”\n季度：“quarters” → “Q”\n年：“years” → “y”\n\n點擊下方鏈接可查看全部計時單位。',
      example:
        '// 為 2024/03/25 增加 1 天的時間間隔。計時單位 "days" 表示按 “天” 計算。\n公式：DATEADD("2024/03/25", 1, "days")\n運算結果：2024/03/26\n\n// 為 2024/03/25 減少 1 天的時間間隔。計時單位 "days" 表示按 “天” 計算。\n公式：DATEADD("2024/03/25", -1, "days")\n運算結果：2024/03/24\n\n// 為 {啟動時間} 增加 10 天的時間間隔。下列欄位 {啟動時間} 是日期類型且單元格值 2024/03/25 。計時單位 "days" 表示按 “天” 計算。\n公式：DATEADD({啟動時間}, 10, "days")\n運算結果：2024/04/04',
      name: 'Date Add',
    },
    datestr: {
      description:
        '將日期格式化為“年-月-日”形式的文本（固定格式為YYYY-MM-DD）\n\n【date】是被格式化的日期\n\n日期經過格式化後，將變為一串文本，不再具有日期數據的屬性。',
      example: 'DATESTR("2024/10/01")\n=> 2024-10-01\n\nDATESTR({啟動時間})\n=> 2024-06-10',
      name: 'Datestr',
    },
    datetime_diff: {
      description:
        '返回兩個日期之間的差值（有正負），即日期1減去日期2。\n\n【date1】日期1\n【date2】日期2\n【units】計時單位，日期1與日期2差值的計算單位。比如按“天”計算也可以轉換為按“年”計算。\n\n計時單位包括以下符號，兩種格式都可以使用：「單位說明符 」→ 「縮寫」\n毫秒："milliseconds" → "ms"\n秒："seconds" → "s"\n分鐘："minutes" → "m"\n小時："hours" → "h"\n天："days" → "d"\n週："weeks" → "w"\n月："months" → "M"\n季度："quarters" → "Q"\n年："years" → "y"\n\n點擊下方鏈接可查看全部計時單位。',
      example:
        'DATETIME_DIFF( "2024-08-11"  ,"2024-08-10", "days")\n=> 1\n\nDATETIME_DIFF( "2024-08-9" ,"2024-08-10", "days")\n=> -1\n\nDATETIME_DIFF( {截止時間} , TODAY(), "hours")\n=> 48',
      name: 'Datetime Diff',
    },
    datetime_format: {
      description:
        '將日期以自定義的形式格式化為文本。\n\n【date】是需要被格式化的日期。\n【output_specifier】是選擇的格式化說明符。比如說明符可以是：\n"DD-MM-YYYY"表示"日-月-年"，見例子一；\n" YYYY / MM / DD"表示"年/月/日"，見例子二；\n"MM.DD"表示"月.日"，見例子三。\n\n日期經過格式化後，將變為一串文本。\n\n本函數支持的日期格式化說明符請看下方鏈接。',
      example:
        'DATETIME_FORMAT("2024-10-01", "DD-MM-YYYY")\n=> 01-10-2024\n\nDATETIME_FORMAT("2024-10-01", "YYYY / MM / DD")\n=>2024/10/01\n\nDATETIME_FORMAT("2024-10-01", "MM.DD")\n=>10.01\n\nDATETIME_FORMAT(TODAY(), "DD-MM-YYYY")\n=> 01-10-2024',
      name: 'Datetime Format',
    },
    datetime_parse: {
      description:
        '將文本轉換為結構化日期類型。\n\n【date】是要被格式化為日期的文本。\n【input_format】非必填，本參數是日期格式化說明符。對於系統無法識別的文本日期內容，你可以自己解釋為結構化的日期。見例子二。\n\n本函數支持的日期格式化說明符和語言環境請查看下方鏈接。',
      example:
        'DATETIME_PARSE("20241001")\n=> "2024/10/01"\n\nDATETIME_PARSE("01 10 2024 18:00", "DD MM YYYY HH:mm")\n=> "2024/10/01 18:00"\n\nDATETIME_PARSE("01號10月2024年18:00時", "DD號MM月YYYY年HH:mm時")\n=> "2024/10/01 18:00',
      name: 'Datetime Parse',
    },
    day: {
      description:
        '返回指定日期屬於當月的第幾號，輸出格式為1-31之間的整數。\n\n【date】是指定的日期。\n比如，數字1表示日期屬於當月的第1號。',
      example: 'DAY("2024.10.01")\n=>1\n\nDAY({完成日期})\n=>5',
      name: 'Day',
    },
    encode_url_component: {
      description:
        '把文本編碼為 URL的格式。\n\n【component_string】是需要被編碼的文本。不編碼以下字符：- _ . ~\n\n比如將第一個例子的輸出值複製到瀏覽器地址欄，就相當於在百度裡搜索”蘋果“的URL',
      example: '"https://www.baidu.com/s?wd=" & ENCODE_URL_COMPONENT（{搜索關鍵詞}）',
      name: 'Encode URL Component',
    },
    error: {
      description:
        '在單元格內顯示錯誤提示和原因。\n\n可以在函數內輸入文本說明錯誤原因，比如例子中的”統計錯誤“就是你定義的錯誤原因。',
      example: 'IF({年紀}< 0, ERROR("統計錯誤"), "正常")\n=>  #Error: 統計錯誤',
      name: 'Error',
    },
    even: {
      description:
        '返回沿絕對值增大方向最接近的偶數。\n\n【value】是要取偶的數值。\n【絕對值增大】即它返回值是遠離0（零）方向。',
      example: 'EVEN(1.5)\n=> 2\n\nEVEN(-1.8)\n=> -2',
      name: 'Even',
    },
    example: '函數示例',
    exp: {
      description: '返回 e 的指定次方。\n\n【e】是自然數，約為 2.718282\n【power】是冪。即指定 e 的多少次方。',
      example: 'EXP(1)\n=> 2.72\n\nEXP(2)\n=> 7.40',
      name: 'Exp',
    },
    false: {
      description:
        '返回邏輯值false\n\n可以判斷勾選類型的欄位中單元格是否為”未選中狀態“，見例子一；\n\n可以和FALSE一起使用輸出為真和假的布爾值，見例子二；',
      example: 'IF({完成狀態(勾選)}= FALSE(), "未完成"， "已完成")\n\nIF({平均成績} >60, TRUE(), FALSE())',
      name: 'False',
    },
    features_list: '公式列表',
    find: {
      description:
        '查找特定的文本在內容中第一次出現的位置。\n\n【stringToFind】是要查找到的特定文本。\n【whereToSearch】指定從哪段內容內查找文本。可以輸入文本參數或者引用欄位。\n【startFromPosition】非必填，指定從內容的哪個位置開始查找（用數字表示第幾個字符）。\n\n本函數可以在一大段內容中快速查找特定文本出現的位置。\n如果返回數字3，表示文本出現在該內容的第3個字符。\n如果未找到匹配的文本，則結果將為0。\n\n其效果與SEARCH()類似，但是未找到匹配項時，SEARCH()返回值為空而不是0。',
      example:
        'FIND("蘋果", "這個蘋果又大又圓，要買兩斤蘋果嗎？")\n=> 3\n\nFIND("香蕉", "這個蘋果又大又圓，要買兩斤蘋果嗎？")\n=> 0\n\nFIND("蘋果", "這個蘋果又大又圓，買兩斤蘋果嗎？"，10)\n=> 13',
      name: 'Find',
    },
    floor: {
      description:
        '將數值向下捨入為最接近的指定基數的倍數。\n\n【value】是要向下捨入的值。\n【significance】非必填，是用於向下捨入的基數，返回值為基數的倍數。如果未提供，默認取1。\n【向下捨入】即它返回值是小於或等於原數值,且為最接近基數的倍數。',
      example: 'FLOOR(1.01, 0.1)\n=> 1.0\n\nFLOOR(-1.99, 0.1)\n=> -2.0',
      name: 'Floor',
    },
    from_now: {
      description:
        '返回當前日期和指定日期之間的差值（無正負）。\n\n【date】是指定日期，即用指定日期減去當前日期，計算兩個日期相差的多少天（自定義計時單位），無正負。\n【units】計時單位，即指定日期與當前日期差值的計算單位，比如按”天“計算也可以轉換為按”年“計算。\n\n計時單位包括以下符號，兩種格式都可以使用：\n「單位說明符 」→ 「縮寫」\n毫秒："milliseconds" → "ms"\n秒："seconds" → "s"\n分鐘："minutes" → "m"\n小時："hours" → "h"\n天："days" → "d"\n週："weeks" → "w"\n月："months" → "M"\n季度："quarters" → "Q"\n年："years" → "y"\n點擊下方鏈接可查看全部計時單位。',
      example: 'FRONOW("2023-08-10", "y")\n=> 1\n\nFROMNOW({開始日期}, "days")\n=> 25',
      name: 'From Now',
    },
    hour: {
      description:
        '返回指定日期的對應的時刻，輸出格式為0（12:00 am）到23（11:00 pm）之間的整數。\n\n【date】是指定的日期。\n比如，18表示18:00',
      example: 'HOUR({打卡時間})\n=> 9',
      name: 'Hour',
    },
    if: {
      description:
        '判斷是否滿足某個條件，如果滿足則返回第一個值，如果不滿足則返回第二個值。\n\n【logical】是邏輯條件，表示計算結果為真（true）和假（false）的表達式。\n【value1】是當邏輯條件為真時的返回值。\n【value2】是當邏輯條件為假時的返回值。\n\nIF支持嵌套使用，並且可以用於檢查單元格是否為空白/為空。',
      example:
        'IF({分數} > 60, "及格", "不及格")\n\nIF({水溫} >  40, IF({水溫} < 60, "剛剛好", "太熱"), "太冷")\n\nIF({Date} = BLANK(), "請輸入日期", "日期已經輸入")',
      name: 'If',
    },
    input_formula: '輸入函數',
    int: {
      description:
        '將數值向下捨入為最接近的整數。\n\n【value】是要向下捨入的值。\n【向下捨入】即它返回值是小於或等於原數值。',
      example: 'INT(1.99)\n=> 1\n\nINT(-1.99)\n=> -2',
      name: 'Int',
    },
    is_after: {
      description:
        '比較日期1是否晚於日期2，如果晚於則返回真（true），否則返回假（false）。\n\n【date1】是日期1。\n【date2】是日期2。\n\n日期可以是輸入的參數，見用例一；\n日期也可以是引用日期類型的欄位，見用例二。\n\n在單元格內真和假用"已勾選"和"未勾選"表示。',
      example:
        'IS_AFTER("2024-10-02" , "2024-10-01")\n=> TRUE\n\nIS_AFTER({截止時間}, TODAY())\n=> TRUE\n\nIS_AFTER({截止時間}, "2024-10-01")\n=> TRUE',
      name: 'Is After',
    },
    is_before: {
      description:
        '比較日期1是否早於日期2，如果早於則返回真（true），否則返回假（false）。\n\n【date1】是日期1。\n【date2】是日期2。\n\n日期可以是輸入的參數，見用例一；\n日期也可以是引用日期類型的欄位，見用例二。\n在單元格內真和假用"已勾選"和"未勾選"表示。',
      example:
        'IS_BEFORE("2024-10-01" , "2024-10-02")\n=> TRUE\n\nIS_BEFORE({截止時間}, TODAY())\n=> TRUE\n\nIS_BEFORE({截止時間}, "2024-10-01")\n=> TRUE',
      name: 'Is Before',
    },
    is_error: {
      description:
        '檢查一個公式運行是否錯誤，如果錯誤則返回真（true）。\n\n【expr】是需要檢查的值。檢測值可以是算術運算、邏輯判斷等類型的公式。',
      example: 'IS_ERROR(2/0)\n=> TRUE\n\nIS_ERROR("哈哈"*2)\n=> TRUE',
      name: 'Is Error',
    },
    is_same: {
      description:
        '確定日期1是否等於日期2，如果等於則返回真（true），否則返回假（false）。\n\n【date1】是日期1。\n【date2】是日期2。\n【units】非必填， 是比較的時間單位。比如比較兩個日期是否相等，一直對比到分鐘單位。\n\n日期可以是輸入的參數，見用例一；\n日期也可以是引用日期類型的欄位，見用例四。\n在單元格內真和假用"已勾選"和"未勾選"表示。\n\n點擊下方鏈接可查看全部計時單位。',
      example:
        'IS_SAME("2024-10-01" , "2024-10-01")\n=> TRUE\n\nIS_SAME("2024-10-01" , "2024-11-11", "years")\n=> TRUE\n\nIS_SAME("2024-10-01" , "2024-11-11", "months")\n=> FALSE\n\nIS_SAME({截止時間}, {完工時間}, "days")\n=> TRUE\n\nIS_SAME("2024-10-01", {完工時間}, "days")\n=> TRUE',
      name: 'Is Same',
    },
    last_modified_time: {
      description:
        '返回每一行的單元格中進行最後一次修改的時間 。\n注意：系統只會返回計算類型列的單元格的修改。\n\n如果你只關心特定欄位的單元格的更新時間，你可以指定一個或多個列，見例子二和三。',
      example:
        'LAST_MODIFIED_TIME()\n=> "2024-06-10 6:27 下午."\n\nLAST_MODIFIED_TIME({項目進度})\n=> "2024-06-09 1:27 上午"\n\nLAST_MODIFIED_TIME({項目進度}, {任務分配})\n=> "2024-06-09 1:27 上午',
      name: 'Last Modified Time',
    },
    left: {
      description:
        '從文本的開頭提取多個字符。\n\n【string】是要被提取字符的文本。\n【howMany】是提取的字符數量。用數字表示，比如"4"，代表從左到右提取4個字符。',
      example: 'LEFT("Bika：支持API，隨意DIY", 4)\n=> Bika\n\nLEFT({出生年月}, 4)\n=> 1994',
      name: 'Left',
    },
    len: {
      description: '統計一段文本的字符長度。\n\n【string】是要計算長度的文本；標點符號、空格等也會占一個字符。',
      example: 'LEN("你猜猜我有多長？")\n=> 8\n\nLEN("a blank")\n=> 7',
      name: 'Len',
    },
    log: {
      description:
        '以指定基數為底，返回數值的對數。\n\n【number】是想要計算其對數的數值。\n【base】是對數的基數（底數），如果未指定基數，則默認為 10。',
      example: 'LOG(1024, 2)\n=> 10\n\nLOG(10000)\n=> 4',
      name: 'Log',
    },
    logic: '邏輯',
    lower: {
      description: '將文本中所有大寫字符全部轉換為小寫字符。\n\n【string】是被轉換的文本。',
      example: 'LOWER("HELLO") => "hello"',
      name: 'Lower',
    },
    max: {
      description:
        '返回最大的數值。\n\n【number...】是進行運算的數值參數，可以輸入數字或引用數值類型的列。數值類型的列包括數字、貨幣、百分比、評分等。\n\n另外，本函數的輸入值都為日期格式時，可以比較多個日期中最晚的日期。',
      example: 'MAX(1, 3, 5, 7) => 7',
      name: 'Max',
    },
    mid: {
      description:
        '從內容中特定位置提取一段固定長度的文本。\n\n【string】是你輸入的一段內容，其中包含了被提取的文本。該內容可以是輸入的文本或者引用的欄位數據。\n【whereToSearch】是你指定從哪兒提取文本，用數字表示。比如數字"3"表示從內容的第3個字符開始提取。\n【count】是提取的文本長度，用數字表示。比如數字"2"表示從指定位置提取2個字符。',
      example:
        'MID("這個蘋果又大又圓", 3, 2)\n=> 蘋果\n\nMID("這個蘋果又大又圓", 99, 2)\n=> 空值\n\nMID("這個蘋果又大又圓", 3, 99)\n=> 蘋果又大又圓\n\nMID({嘉賓姓名}, 2, 99)\n=> 彥祖',
      name: 'Mid',
    },
    min: {
      description:
        '返回最小的數值。\n\n【number…】是進行運算的數值參數，可以輸入數字或引用數值類型的列。數值類型的列包括數字、貨幣、百分比、評分等。\n\n另外，本函數的輸入值都為日期格式時，可以比較多個日期中最晚的日期。',
      example: 'MIN({數學成績}, {英語成績}, {語文成績}) => 80',
      name: 'Min',
    },
    minute: {
      description: '返回指定日期的分鐘數，輸出格式為0到59之間的整數。\n\n【date】是指定的日期。',
      example: 'MINUTE({打卡時間})\n=>30',
      name: 'Minute',
    },
    mod: {
      description:
        '返回兩數值相除的餘數。\n\n【value】是被除數。\n【divisor】是除數。\n\n返回結果的符號與除數的符號相同。',
      example: 'MOD(10, 3) => 1',
      name: 'Mod',
    },
    month: {
      description:
        '返回指定日期對應的月份。\n\n【date】是指定的日期。\n\n本函數輸出值為1（一月）至12（十二月）之間的整數。',
      example: 'MONTH("2024.10.01")\n=> 10\n\nMONTH({畢業時間})\n=> 6',
      name: 'Month',
    },
    not: {
      description:
        '反轉其參數的邏輯值。\n\n【boolean】是布爾參數，意味著你的輸入值必須是邏輯判斷且輸出值只有真和假，比如比較兩個值誰大誰小。\n當你參數的邏輯判斷為真（true）時函數返回假（false）；\n當你參數的邏輯判斷為假（false）時函數返回真（true）；\n\n如例子一：2>3輸出值是假，但經過反轉後函數輸出值的是真。\n如例子二：NOT({年紀} > 18)經過NOT函數反轉後，其實相當於判斷{年紀} ≤ 18',
      example: 'NOT({年紀} > 18)',
      name: 'Not',
    },
    now: {
      description:
        '返回今天的日期和時間，會精確到時分秒。\n\n可以直接使用此函數返回年月日，見例子一；\n\n也可以和DATEADD或DATETIME_DIFF等函數一起使用，比如用{截止時間}減去當前時間，來顯示項目的倒計時，見例子二。\n\n 注意：僅當重新刷新計算公式或刷新表格時，這個函數返回的結果才會更新。',
      example: 'NOW()\n=> "2024/06/02 07:12"\n\nDATETIME_DIFF( {截止時間} , NOW(),"days")\n=> 15',
      name: 'Now',
    },
    number: '數字',
    object: '對象',
    odd: {
      description:
        '返回沿絕對值增大方向最接近的奇數。\n\n【value】是要取奇的數值。\n【絕對值增大】即它返回值是遠離0（零）方向。',
      example: 'ODD(1.5)\n=> 3\n\nODD(-2.1)\n=> -3',
      name: 'Odd',
    },
    or: {
      description:
        '如果任何一個參數為真（true），則返回真（true），否則返回假（false）。\n\n【logical】是邏輯參數，可以是邏輯值、數組或引用的欄位。',
      example: 'OR(3>2, 2>3)\n=>  true',
      name: 'Or',
    },
    power: {
      description: '返回指定基數的冪。即指定基數的多少次方。\n\n【base】是基數。\n【power】是冪',
      example: 'POWER(2, 5)\n=> 32\n\nPOWER(-5, 3)\n=> -125',
      name: 'Power',
    },
    record_id: {
      description: '返回記錄的ID',
      example: '"https://awesomeservice.com/view?recordId=" & RECORD_ID()',
      name: 'Record ID',
    },
    replace: {
      description:
        '將內容中特定位置的一段文本替換為新文本。\n\n【string】是你輸入的一段內容，其中包含了被替換的文本。該內容可以是輸入的文本或者引用的欄位數據。\n【start_character】是你指定從哪兒替換文本，用數字表示。比如數字"3"表示從內容的第3個字符開始替換。\n【number_of_characters】是你指定要替換掉多少個字符，用數字表示。比如數字"2"表示替換掉指定位置的2個字符。\n【replacement】是替換原文本的新文本。\n\n（如果你想將內容中所有出現的原文本替換為新文本，請參見SUBSTITUTE。）',
      example:
        'REPLACE("這個蘋果又大又圓", 3, 2, "桃子")\n=> 這個桃子又大又圓\n\nREPLACE("這個蘋果又大又圓", 3, 99, "榴蓮又香又甜")\n=> 這個榴蓮又香又甜\n\nREPLACE({嘉賓姓名}, 1, 1, "X")\n=> X彥祖',
      name: 'Replace',
    },
    rept: {
      description:
        '根據指定次數重複文本。\n\n【string】是需要重複的文本。\n【mumber】是指定的重複次數。用數字表示，比如”2“，表示重複2次。',
      example: 'REPT("哈", 2)\n=> 哈哈',
      name: 'Rept',
    },
    right: {
      description:
        '從文本的末尾提取出多個字符。\n\n【string】是要被提取字符的文本。\n【howMany】是提取的字符數量。用數字表示，比如"5"，代表從右到左提取5個字符。',
      example: 'RIGHT("Bika：支持API，隨意DIY", 5)\n=> 隨意DIY\n\nRIGHT({出生年月}, 5)\n=> 07-13',
      name: 'Right',
    },
    round: {
      description:
        '按指定的位數對數值進行四捨五入。\n\n【value】是要四捨五入的值\n【precision】非必填，要進行四捨五入運算的位數。未填寫時默認為1。\n\n如果位數大於 0，則四捨五入到指定的小數位。\n如果位數等於 0，則四捨五入到最接近的整數。\n如果位數小於 0，則在小數點左側進行四捨五入。',
      example: 'ROUND(3.14159, 2) => 3.14',
      name: 'Round',
    },
    rounddown: {
      description:
        '按指定的位數將數值延絕對值減小方向捨入。\n\n【value】是要捨入的值。\n【precision】非必填，要將數字捨入到的位數。未填寫時默認為1。\n【絕對值減小】即它返回值是靠近0（零）方向。\n\n如果位數大於 0，則四捨五入到指定的小數位。\n如果位數等於 0，則四捨五入到最接近的整數。\n如果位數小於 0，則在小數點左側進行四捨五入。',
      example: 'ROUNDDOWN(3.14159, 2) => 3.14',
      name: 'Round Down',
    },
    roundup: {
      description:
        '按指定的位數將數值延絕對值增大方向捨入。\n\n【value】是要捨入的值。\n【precision】非必填，要將數字捨入到的位數。未填寫時默認為1。\n【絕對值增大】即它返回值是遠離0（零）方向。\n\n如果位數大於 0，則四捨五入到指定的小數位。\n如果位數等於 0，則四捨五入到最接近的整數。\n如果位數小於 0，則在小數點左側進行四捨五入。',
      example: 'ROUNDUP(3.14159, 2) => 3.15',
      name: 'Round Up',
    },
    search: {
      description:
        '搜索特定的文本在內容中第一次出現的位置。\n\n【stringToFind】是要搜索到的特定文本。\n【whereToSearch】指定從哪段內容搜索文本。可以輸入文本參數或者引用欄位。\n【startFromPosition】非必填，指定從內容的哪個位置開始搜索（用數字表示第幾個字符）。\n\n本函數可以在一大段內容中快速搜索特定文本出現的位置。\n如果返回數字3，表示文本出現在該內容的第3個字符。\n如果未找到匹配的文本，則結果將為空值。\n\n其效果與FIND()類似，但是未找到匹配項時，FIND()返回值為0而不是空值。',
      example:
        'SEARCH("蘋果", "這個蘋果又大又圓，要買兩斤蘋果嗎？")\n=> 3\n\nSEARCH("香蕉", "這個蘋果又大又圓，要買兩斤蘋果嗎？")\n=> 空值\n\nSEARCH("蘋果", "這個蘋果又大又圓，買兩斤蘋果嗎？"，10)\n=> 13',
      name: 'Search',
    },
    second: {
      description: '返回指定日期的秒種，輸出格式為0到59之間的整數。\n\n【date】是指定的日期。',
      example: 'SECOND({打卡時間})\n=> 1',
      name: 'Second',
    },
    select_a_formula: '選擇欄位或函數',
    set_locale: {
      description:
        '為指定日期時間設置特定的語言環境。\n\n【date】是指定日期。\n【locale_modifier】是語言環境說明符。\n\n本函數必須與DATETIME_FORMAT結合使用。可以點擊下方鏈接查看支持的語言環境說明符。',
      example: 'DATETIME_FORMAT(SET_LOCALE(NOW(), "zh-cn"), "lll")\n=> 2024年6月2日上午11點04分',
      name: 'Set Locale',
    },
    set_timezone: {
      description:
        '為指定日期設置特定的時區。\n\n【date】是指定日期。\n【tz_identifier】是時區說明符。比如"8"代表東8區，"-2"代表西2區。\n\n本函數必須與DATETIME_FORMAT結合使用。',
      example: 'DATETIME_FORMAT(SET_TIMEZONE(NOW(), -8), "M/D/YYYY h:mm")\n=> 9/20/2024 2:30',
      name: 'Set Timezone',
    },
    sqrt: {
      description: '返回數值的平方根。\n\n【value】是要對其求平方根的數值。\n\n如果數值為負數，則 SQRT 返回 NaN',
      example: 'SQRT(16) => 4',
      name: 'Sqrt',
    },
    substitute: {
      description:
        '替換內容中指定文本為新文本。\n\n【string】是你輸入的一段內容，其中包含了被替換的文本。該內容可以是輸入的文本或者引用的欄位數據。\n【old_text】是你想要替換的文本。\n【new_text】是替換原文本的新文本。\n【instance_num】非必填，指定替換第幾次出現的舊文本。如果省略，則替換所有出現的舊文本。',
      example:
        'SUBSTITUTE("小胡，小張，小王", "小", "老")\n=> 老胡，老張，老王\n\nSUBSTITUTE("小胡，小張，小王", "小", "老", 3)\n=> 小胡，老張，小王',
      name: 'Substitute',
    },
    sum: {
      description:
        '將所有數值相加。\n      【number...】是進行運算的數值參數，可以輸入數字或引用數值類型的列。\n      數值類型的列包括數字、貨幣、百分比、評分等',
      example: 'SUM(1, 3, 5, "", "VI") => 1 + 3 + 5 = 9',
      name: 'Sum',
    },
    switch: {
      description:
        '本函數為多分支選擇函數，它由表達式和多個分支+返回值組成，如果表達式等於某個分支值，則函數輸出該分支對應的返回值。\n\n【expression】是表達式，其運算的結果會與每個分支進行匹配。\n【pattern】是分支，每個分支代表表達式的可能運算結果。每一個分支都有對應的返回值。\n【result】是返回值，如果表達式的運算結果匹配了一個分支，則輸出對應的返回值。\n【default】是默認值，如果運算結果沒有匹配任何一個分支，則函數輸出默認值。默認值未填寫時為空值。\n\n比如例子一，{國家}是引用的一列數據，其輸出值可能是成千上萬個國家名稱，它是該函數中的表達式。“中國”和“中文”分別為其中一條分支和返回值，它表示如果{國家}的輸出值為“中國”時，則返回“中文”。而“通用英語”為默認值，它表示{國家}的輸出值沒有匹配任何分支時，則輸出“通用英語”',
      example:
        'SWITCH({國家}, "中國", "中文", "俄國", "俄語", "法國", "法語", "日本", "日語", "通用英語")\n\nSWITCH("C", "A", "優秀", "B", "中等", "C", "普通", "D", "較差", "沒有成績")\n=>普通',
      name: 'Switch',
    },
    t: {
      description:
        '如果輸入值為文本類型，則返回原文本，如果非文本類型則返回空值。\n\n【value】是被檢查是否為文本的值。\n\n比如，輸入值引用數字、日期等類型的欄位，那麼將會返回空值。',
      example: 'T("Bika")\n=> Bika\n\nT("55")\n=> 55\n\nT(55)\n=> 空值\n\nT({數學成績})\n=> 空值    ',
      name: 'T',
    },
    text: '文本',
    timestr: {
      description:
        '將日期格式化為"時:分:秒"形式的文本(固定格式為HH:mm:ss)\n\n【date】是被格式化的日期\n\n日期經過格式化後，將變為一串文本，不再具有日期數據的屬性。',
      example: 'TIMESTR(NOW())\n=> "04:52:12',
      name: 'Timestr',
    },
    to_now: {
      description:
        '返回當前日期和指定日期之間的差值（無正負）。\n\n【date】是指定日期，即用指定日期減去當前日期，計算兩個日期相差的多少天（自定義計時單位），無正負。\n【units】計時單位，即指定日期與當前日期差值的計算單位，比如按”天“計算也可以轉換為按”年“計算。\n\n計時單位包括以下符號，兩種格式都可以使用：\n「單位說明符 」→ 「縮寫」\n毫秒："milliseconds" → "ms"\n秒："seconds" → "s"\n分鐘："minutes" → "m"\n小時："hours" → "h"\n天："days" → "d"\n週："weeks" → "w"\n月："months" → "M"\n季度："quarters" → "Q"\n年："years" → "y"\n點擊下方鏈接可查看全部計時單位。',
      example: 'TONOW("2023-08-10", "y")\n=> 1\n\nTONOW({開始日期}, "days")\n=> 25',
      name: 'To Now',
    },
    today: {
      description:
        '返回今天的日期（年月日），但不會精確到時分秒（默認為00:00:00）。如果想要精確到時分秒，請使用函數NOW。\n\n可以直接使用此函數返回年月日，見例子一；\n也可以和DATEADD或DATETIME_DIFF等函數一起使用，比如用{截止時間}減去當前時間，來顯示項目的倒計時，見例子二。\n\n注意：僅當重新刷新計算公式或刷新表格時，這個函數返回的結果才會更新。',
      example: 'TODAY() => 2024/06/02',
      name: 'Today',
    },
    trim: {
      description: '清除文本開頭和結尾的空格。\n\n【value】是需要被處理的文本。',
      example: 'TRIM(" 兩邊空格會被清除! ")\n=>兩邊空格會被清除!',
      name: 'Trim',
    },
    true: {
      description:
        '【簡介】\n返回邏輯值真（ true ）。\n\n【參數說明】\n該函數不需要填寫參數。\n該函數可以判斷勾選類型的欄位是否為 “已選中狀態” ，見例子一；\n該函數和 FALSE() 一起使用可以輸出為真和假的布爾值，見例子二。',
      example:
        '// 判斷勾選類型的欄位的狀態。例如下列 {是否完成} 欄位為勾選類型且單元格值為 "已勾選"時：\n公式：IF({是否完成} = TRUE(), "已完成"， "未完成")\n運算結果："已完成"\n\n// TRUE() 和 FALSE() 一起使用輸出布爾值。例如下列 {成績} 欄位為數字類型且單元格值為 70 時：\n公式：IF({成績} > 60, TRUE(), FALSE())\n運算結果：true',
      name: 'True',
    },
    upper: {
      description: '將文本中所有小寫字符全部轉換為大寫字符。\n\n【string】是被轉換的文本。',
      example: 'UPPER("hello") => "HELLO"',
      name: 'Upper',
    },
    usage: '函數使用',
    value: {
      description: '將文本字符串轉換為數字。\n\n【text】表示要轉換的文本值。\n\n本函數可以將文本內的數字提取出來。',
      example: 'VALUE("$10000")\n=> 10000',
      name: 'Value',
    },
    weekday: {
      description:
        '返回指定日期對應一週中的星期幾。\n\n【date】是指定的日期。\n【startDayOfWeek】非必填，是一週的開始時間，默認情況下每週從星期日開始（即週日為0）。 你還可以將開始時間設置為"Monday"(星期一，見例子二)\n\n本函數輸出值為0到6之間的整數。 ',
      example: 'WEEKDAY("2024.10.01")\n=>4\n\nWEEKDAY("2024.10.01", "Monday")\n=>3\n\nWEEKDAY(TODAY())',
      name: 'Weekday',
    },
    weeknum: {
      description:
        '返回指定日期對應為一年中的第幾個星期。\n\n【date】是指定的日期。\n【startDayOfWeek】非必填，是一周的開始時間，默認情況下每周從星期日開始（即周日為0）。 你還可以將開始時間設置為"Monday"(星期一)\n\n本函數輸出值為整數。比如6，代表該日期屬於一年中的第6個星期。',
      example: 'WEEKNUM("2024.10.01")\n=>40\n\nWEEKNUM("2024.10.01", "Sunday")\n=>40\n\nWEEKNUM(TODAY())\n=>33',
      name: 'Weeknum',
    },
    workday: {
      description:
        '返回起始日期若干個工作日之後的日期。\n\n【startDate】是你指定的起始日期。\n【numDays】是你指定的起始日期之後的工作日天數，用正數表示。比如，數字“1”代表起始日期一個工作日之後的日期，見例子一；\n【holidays】非必填。是要從日曆中去除的特定日期，例如節假日。其輸入格式為「yyyy-mm-dd」，多個日期以逗號分隔的，見例子三。\n\n本函數的工作日不包括週末和你指定的特定日期。',
      example:
        'WORKDAY("2024/10/01" , 1)\n=> 2024/10/02\n\nWORKDAY("2024/10/01" , 1，"2024-10-02")\n=> 2024/10/05\n\nWORKDAY({啟動日期}, 100, "2024-10-01, 2024-10-02, 2024-10-03, 2024-10-04, 2024-10-05, 2024-10-06, 2024-10-07, 2024-10-08")\n=> 2024-11-11',
      name: 'Workday',
    },
    workday_diff: {
      description:
        '統計兩個日期之間相隔多少個工作日（有正負）。\n\n【startDate】起始日期。\n【endDate】截止日期。如果起始日期比截止日期晚，則會出現負數。\n【holidays】非必填。是要從工作日曆中去除的日期，例如節假日。其輸入格式為「yyyy-mm-dd」，多個日期以逗號分隔的。\n\n本函數統計起止日期之間的工作日，不包括週末和你指定的特定日期。',
      example:
        'WORKDAY_DIFF("2024-10-01", "2024-10-02")\n=> 2\n\nWORKDAY_DIFF("2024-10-02", "2024-10-01")\n=> -2\n\nWORKDAY_DIFF("2024-10-01", "2024-10-05")\n=> 3\n\nWORKDAY_DIFF({產品啟動日期}, {產品上線日期} , "2024-06-25, 2024-06-26, 2024-06-27")\n=> 100',
      name: 'Workday Diff',
    },
    xor: {
      description:
        '如果奇數個參數為真（true），則返回真（true），否則返回假（false）。\n\n【logical】是邏輯參數，可以是邏輯值、數組或引用的欄位。',
      example: 'XOR(3>2, 2>3, 4>3)\n=> false',
      name: 'Xor',
    },
    year: {
      description: '返回指定日期對應的四位數年份。\n\n【date】是指定的日期。',
      example: 'YEAR("2024/10/01")\n=> 2024\n\nYEAR({畢業時間})\n=> 2024',
      name: 'Year',
    },
  },
  global: {
    action: {
      cannot_be_empty: '不能為空',
      detail: '詳情',
      full_screen: '全屏',
      no_result_found: '暫無搜尋記錄',
      preview: '預覽',
      select: '選擇',
      toggle: '切換',
      un_named: '未命名',
      zoom_in: '放大',
      zoom_out: '縮小',
    },
    copilot: {
      delete_history: '刪除對話',
      delete_history_confirm: '確定要刪除這個對話歷史嗎？',
      history: '對話歷史',
      history_empty: '暫無對話歷史',
      history_loading: '載入中...',
      history_no_description: '無描述',
      history_no_more: '沒有更多了',
      history_no_title: '無標題',
      new_chat: '新建對話',
      title: 'AI 助手',
      upload_file: '上傳檔案',
      welcome: '你好，我是你的 AI 助手，隨時為你提供幫助！',
    },
    dl_link_unavailable: '下載鏈接不可用',
    download: '下載',
    error_description: '我們的技術團隊已經被告知並正在努力解決這個問題。請您嘗試以下操作：',
    error_reason: [
      '返回上一頁並刷新頁面，然後再次嘗試。',
      '暫時離開並稍後再試。',
      '如果問題持續存在，請聯繫客戶支持團隊以獲取進一步的幫助。',
    ],
    guest: '訪客',
    guest_management: '訪客管理',
    hooks: {
      firebase: {
        create_hardware_description: '硬件設備集成綁定',
        create_hardware_name: '硬件設備綁定',
      },
    },
    me: '我(當前訪問者)',
    page_not_found: '抱歉，您訪問的頁面未找到',
    page_not_found_description: '我們無法找到您請求的頁面。可能由於以下原因：',
    page_not_found_reason: [
      '您輸入的URL有誤或拼寫錯誤',
      '所請求的頁面已被刪除或移動',
      '我們的伺服器暫時無法找到所請求的資源',
    ],
    retry: '重試',
    select: '請選擇',
    select_path: '請選擇完整路徑',
    server_error: '抱歉，伺服器在處理您的請求時遇到了問題',
    toast: {
      description_update_success: '描述更新成功',
      open_in_database: '請開啟資料庫頁面',
    },
    welcome: '歡迎來到 {name}',
  },
  grid: {
    asc_option: '選項正序',
    batch_update_selected_record: '批量更新 {recordCount} 條記錄',
    bulk_update: '批量更新',
    bulk_update_confirm_content: '將有 {count} 條記錄會被修改，確定要保存嗎？',
    bulk_update_confirm_title: '確認批量更新',
    bulk_update_fields: '需修改的欄位',
    bulk_update_successful: '批量編輯記錄成功',
    bulk_update_title: '批量更新記錄',
    copy_row: '複製行',
    created_doc: '建立文件',
    delete_n_record: '刪除{count}條記錄',
    desc_option: '選項倒序',
    duplicate_record: '重複記錄',
    edit_record: '編輯記錄',
    filter_to_find_all_records: '篩選查找數據庫內所有需批量編輯的記錄',
    group: '群組',
    lookup_unamed_record: '未命名記錄',
    new_record: '新增記錄',
    pasting_multiple_columns_is_not_supportted_currently: '暫不支持粘貼多列數據',
    record_selected: '已選擇{count}條記錄',
    row_group: '分組',
    see_more_detail: '查看更多',
    select_record_by_name: '選擇 {name}',
    tooltip_new_record: '點擊新建一行記錄',
    un_named_doc: '未命名文件',
  },
  help: {
    description: '想要了解更多或者仍然需要幫助？',
    help: '幫助',
    help_and_support: '幫助和支援',
    help_center: '幫助中心',
    title: '我們能幫到你什麼？',
  },
  integration: {
    advertise: {
      can_do: '以下是一些富有啟發性的應用場景示例，供您參考：',
      connect_to: '連接到 {name}',
      notice: '注意：此應用程式將可供您帳戶中的所有使用者使用。安裝此應用程式即表示您同意其服務條款。',
    },
    airtable: {
      airtable_token: 'Airtable API Token',
      airtable_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      description:
        'Airtable 集成讓使用者能夠將 Airtable 的表單數據直接同步到系統中。通過自動化流程，可以在表單數據更新時自動觸發相關操作，如更新數據庫、發送通知或生成報表。適用於需要實時同步和管理數據的業務場景。',
      title: 'Airtable',
    },
    aitable: {
      aitable_token: 'AITable API Token',
      aitable_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      description:
        'AITable 集成使得使用者可以將 AITable 的數據集成到系統中。結合自動化流程，可以在數據發生變化時自動觸發各種任務，如數據同步、通知推送或報告生成。適用於需要高效數據管理和實時響應的場景。',
      title: 'AITable',
    },
    apitable: {
      apitable_token: 'APITable API Token',
      apitable_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      description:
        '通過 APITable 集成，使用者可以將 APITable 的數據與系統無縫連接。利用自動化流程，可以在數據變化時自動執行預設任務，如更新記錄、發送提醒或觸發其他操作。適用於需要靈活數據管理和快速響應的應用場景。',
      title: 'APITable',
    },
    awsocr: {
      aws_ocr_token: 'AWS Textract API Token',
      aws_ocr_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      description:
        'AWS Textract 用於連接和管理 AWS 的光學字符識別服務，支持圖像文字識別和提取，幫助用戶高效處理和存儲文本數據。',
      title: 'AWS Textract',
    },
    azure: {
      apikey: 'Azure AI API 密鈅',
      apikey_placeholder: '請輸入您的 Azure AI API 密鈅',
      description:
        '使用 Azure AI（Azure OpenAI／Azure AI Agents）服務，透過 API 密鈅呼叫模型，支援完成、聊天與嵌入功能。',
      title: 'Azure AI',
    },
    banner_description: '輕鬆連接您的應用程序，自動化您的工作流程',
    banner_title: '三方集成',
    bedrock: {
      apikey: 'Amazon Bedrock API 密鈅',
      apikey_placeholder: '請輸入您的 Amazon Bedrock API 密鈅',
      description:
        '透過 Amazon Bedrock 呼叫 Foundation 模型，實現強大的生成式 AI 能力，包括模型微調、知識庫擴充與任務代理。',
      title: 'Amazon Bedrock',
    },
    byte_doubao: {
      description: '字節豆包集成，提供智能對話和內容生成服務。',
      title: '字節豆包',
      use_cases: [
        '幫助學生解決學科問題和提供論文寫作指導',
        '支持職場人士撰寫報告和獲取專業建議',
        '提供旅行規劃、娛樂推薦和健康諮詢等日常服務',
        '為創意工作者提供靈感啟發和設計建議',
      ],
    },
    claudeai: {
      description:
        'Claude 系列模型是由 Anthropic 開發的大語言模型（包含 Haiku、Sonnet、Opus），能夠提供智能對話、內容生成和數據分析等服務。它能夠理解複雜的查詢，並提供精確的回答，幫助用戶提高工作效率和創造力。',
      title: 'Claude.ai',
      use_cases: [
        '對數據表中的記錄進行匯總並生成報告',
        '基於數據表的記錄，生成郵件正文',
        '生成博客文章、新聞稿等文本內容',
      ],
    },
    deepseek: {
      apikey: 'DeepSeek API 密鑰',
      apikey_help_text:
        '輸入由 DeepSeek 提供的 API 密鑰，您可以在 DeepSeek API Keys 找到它。https://platform.deepseek.com/api_keys',
      apikey_placeholder: 'sk-xxxxxxxxxxxxxxxxxxxxxxxx',
      custom_base_url: '自定義 Base URL',
      custom_base_url_description:
        '一些第三方平台也提供 DeepSeek 模型的調用，因此你可以自行修改基礎 URL（例如阿里雲、火山方舟、硅基流動）。',
      custom_model: '自定義模型 ID',
      custom_model_description: '您可以使用自定義模型 ID 來指定您想要使用的模型。',
      description:
        'DeepSeek-R1 是一種最先進的大型語言模型，經過強化學習和冷啟動數據優化，具有卓越的推理、數學和代碼性能。',
      organization_id: '組織 ID',
      organization_id_help_text: '組織 ID 是您組織的唯一標識符，可在 API 請求中使用。（通常不填）',
      title: 'DeepSeek',
    },
    delete_warning_content: '刪除後無法還原，您確定要刪除嗎？',
    delete_warning_title: '刪除配置',
    description_know_more: '查看幫助文檔以了解更多信息',
    dingtalk: {
      description:
        '透過釘釘自訂機器人的 Webhook，實現自動化發送各類消息至釘釘群組。搭配自動化流程，可用於任務提醒、狀態更新或項目彙報的自動通知。透過釘釘群組實現任務完成情況或重要資訊的即時推送，增強企業內部的溝通和任務管理效率。',
      form_item_1_label: 'Webhook Url',
      form_item_1_placeholder: 'Webhook Url',
      title: '釘釘自訂機器人',
    },
    explore_integration: '探索集成',
    features_list: 'Integrations List',
    feishu: {
      description:
        '透過飛書自訂機器人的 Webhook，將消息發送至群組。與自動化流程配合使用，可在飛書平台上實現定期更新、告警通知或會議安排的自動推送。這種整合幫助團隊成員在飛書群中及時了解重要動態，提升工作流程的透明度和資訊共享效率。',
      form_item_1_label: 'Webhook Url',
      form_item_1_placeholder: 'Webhook Url',
      title: '飛書自訂機器人',
    },
    general: {
      err_msg: '該配置項不能為空',
      note: '備註名稱',
      note_placeholder: '請起一個易於記憶和辨識的名稱',
    },
    github: {
      description: '將您的 Bika 帳號與 GitHub 帳號綁定，輕鬆實現通過 GitHub 帳號登錄 Bika，安全便捷。',
      title: 'GitHub',
    },
    google: {
      description: '將您的 Bika 帳號與 Google 帳號綁定，輕鬆實現通過 Google 帳號登錄 Bika，安全便捷。',
      title: 'Google',
    },
    googleai: {
      description:
        'Google AI 是由 Google 公司開發的一系列大語言模型（包含 Gemini 等），能夠提供智能對話、內容生成和數據分析等服務。它能夠理解複雜的查詢，並提供精確的回答，幫助用戶提高工作效率和創造力。',
      title: 'Google AI',
      use_cases: [
        '利用數據表記錄進行深度市場分析,洞察用戶需求和行為模式',
        '自動整合數據表信息,生成可視化的專業分析報告',
        '基於給定主題和關鍵詞,智能創作引人入勝的文章和社交媒體內容',
      ],
    },
    imap: {
      create_new_integration: '連接到新的電子郵件帳戶',
      description:
        '通過配置 IMAP 郵箱賬號，使用者可以將接收郵件的功能集成到系統中。結合自動化流程，可以在收到特定郵件時觸發相應的操作，如自動創建任務、歸檔郵件或觸發警報。適用於需要從郵件中獲取信息並做出回應的場景。',
      password_label: '密碼',
      password_placeholder: '請輸入密碼',
      port_err_msg: '端口必須是數字',
      port_helper_text: '請輸入IMAP伺服器使用的端口號。常用端口是993。',
      port_label: '端口',
      port_placeholder: '請輸入IMAP伺服器使用的端口號',
      server_helper_text: '請輸入接收郵件伺服器地址 (IMAP)。如果您沒有此信息，請聯繫您的電子郵件服務提供商。',
      server_label: 'IMAP 伺服器',
      server_placeholder: 'imap.example.com',
      title: 'IMAP 郵箱賬號',
      tls_label: '啟用 TLS',
      user_name_label: '用戶名',
      user_name_placeholder: '<EMAIL>',
    },
    integration: '三方集成',
    linkedin: {
      description:
        'LinkedIn 是一個專業的社交平台，旨在幫助用戶建立職業網絡、尋找工作機會並分享行業見解。通過集成 Bika 的自動化功能，您可以高效管理公司或個人信息。',
      title: '領英',
      use_cases: [
        '使用數據表記錄在 LinkedIn 上發布新的博客文章',
        '使用數據表記錄更新 LinkedIn 公司頁面',
        '新表單提交時創建 LinkedIn 分享更新',
      ],
    },
    make: {
      description:
        'Make.com 是一個自動化平台，通過零代碼或低代碼解決方案幫助用戶連接應用程序和服務，從而簡化工作流程。通過集成 Bika 的數據表和自動化功能，使您的數據在平台之間無縫流動。',
      title: 'Make.com',
      use_cases: [
        '使用 Make.com 將數據發佈到數千個應用程序',
        '將 Make.com 場景中的數據保存到數據表',
        '從 Make.com 場景創建新的數據表記錄',
        '當有新的表單提交時，激活 Make.com 場景',
      ],
    },
    my_integration: '我的集成',
    mysql: {
      database_name: 'Database Name',
      database_name_placeholder: ' ',
      description: 'MySQL 用於連接和管理 MySQL 數據庫，支持數據查詢、插入、更新和刪除，幫助用戶高效地處理和存儲數據。',
      host_helper_text: '請輸入 MySQL 伺服器的地址。如果您沒有此信息，請聯繫您的數據庫管理員。',
      host_label: 'MySQL 伺服器',
      host_placeholder: 'mysql.example.com',
      name_label: 'Username',
      name_placeholder: '<EMAIL>',
      password_label: 'Password',
      password_placeholder: ' ',
      port_err_msg: '連接埠必須是數字',
      port_helper_text: '請輸入 MySQL 伺服器使用的連接埠號。',
      port_label: 'Port',
      port_placeholder: ' ',
      title: 'MySQL',
    },
    openai: {
      apikey: 'OpenAI API 密鈅',
      apikey_help_text:
        '輸入由 OpenAI 提供的 API 密鑰，您可以在 OpenAI 帳戶設置中找到它。https://platform.openai.com/api-keys',
      apikey_placeholder: 'sk-xxxxxxxxxxxxxxxxxxxxxxxx',
      custom_base_url: '自定義基礎 URL',
      custom_base_url_description:
        '您可以自定義 OpenAI 的基礎 URL。所有兼容 OpenAI 的 AI 模型 API 將受到支持。 (Google Gemini, Anthropic Claude, 等)',
      custom_model: 'Custom Model Id',
      custom_model_description: 'Model Id 是您自定義的模型 ID，可在 API 請求中使用。',
      description:
        '使用 OpenAI 的 GPT 模型，您可以自動生成自然語言文本、進行智能對話、編寫代碼片段，或提供個性化建議等。',
      organization_id: '組織 ID',
      organization_id_help_text: '組織 ID 是您組織的唯一標識符，可在 API 請求中使用。',
      title: 'OpenAI',
    },
    page_description:
      '集成數百個其他應用、AI智能體和AI模型。通過使用Bika.ai，在第三方應用和您的技術棧之間創建複雜的自動化流程。',
    page_title: '第三方集成 | Bika.ai的AI工作流自動化',
    postgresql: {
      database_name: 'Database Name',
      database_name_placeholder: ' ',
      description:
        'PostgreSQL 用於連接和管理 PostgreSQL 數據庫，支持數據查詢、插入、更新和刪除，幫助用戶高效地處理和存儲數據。',
      host_helper_text:
        'Enter the address of the PostgreSQL server. If you do not have this information available, contact your database administrator.',
      host_label: 'PostgreSQL Server',
      host_placeholder: 'postgresql.example.com',
      name_label: 'Username',
      name_placeholder: '<EMAIL>',
      password_label: 'Password',
      password_placeholder: ' ',
      port_err_msg: 'Port must be a number',
      port_helper_text: 'Enter the port number used by the PostgreSQL server.',
      port_label: 'Port',
      port_placeholder: ' ',
      title: 'PostgreSQL',
    },
    siri: {
      description:
        '結合 Apple Siri、快捷指令與 Bika API，可以實現多種工作流程的自動化。例如，用戶可以通過 Siri 語音指令快速為自己或同事創建 Bika 任務，解放雙手，提高效率。',
      title: 'Siri',
      use_cases: [
        '通過 Siri 語音指令和快捷指令查找數據表中的記錄',
        '使用 Siri 和快捷指令在數據表中創建新記錄',
        '使用 Siri 和快捷指令將照片上傳到數據表',
        '通過 Siri 打開 Bika App 並查看特定數據表',
        '使用 Siri 和快捷指令同步數據表的事件至手機日曆',
      ],
    },
    slack: {
      description:
        '利用 Slack 應用的 Incoming Webhook，將消息發送到 Slack 頻道。結合自動化流程，可以定時自動推送項目進展、任務完成或緊急公告的通知，確保團隊成員在 Slack 中獲取即時資訊。',
      form_item_1_label: 'Incoming Webhook Url',
      form_item_1_placeholder: 'Incoming Webhook Url',
      title: 'Slack 應用',
    },
    smtp: {
      data_missing: '缺少資料',
      description:
        '配置基於 SMTP 協議的自訂發信郵箱。與自動化流程搭配，可以在特定事件觸發時自動發送郵件，適用於任務完成通知、故障警報和定期報告發送、行銷郵件群發等場景。',
      password_label: '密碼',
      password_placeholder: '請輸入密碼',
      port_err_msg: '端口必須是數字',
      port_helper_text: '請輸入SMTP伺服器使用的端口號。常用端口是25、465和587。',
      port_input_err_msg: '請輸入正確的端口',
      port_label: '端口',
      port_placeholder: '請輸入SMTP伺服器使用的端口號',
      server_helper_text: '請輸入發件郵件伺服器地址 (SMTP)。如果您沒有此信息，請聯繫您的電子郵件服務提供商。',
      server_label: 'SMTP 伺服器',
      server_placeholder: 'smtp.example.com',
      title: 'SMTP 郵箱帳號',
      user_name_label: '用戶名',
      user_name_placeholder: '<EMAIL>',
    },
    telegram: {
      description:
        '透過 Telegram Bot 的能力，發送消息至群組、頻道或私聊會話。結合自動化流程，能夠在事件觸發時自動推送通知，如系統狀態更新、事件提醒或團隊動態，確保用戶在 Telegram 平台上即時收到資訊，便於事件管理和快速回應。',
      field_bot_token: '機器人令牌（Bot Token）',
      field_bot_token_placeholder: '請輸入 Bot Token',
      option_manual_token: '手動輸入Bot Token',
      option_select_bot: '從集成中提取 Bot Token',
      title: 'Telegram Bot',
      title_token: 'Bot Token',
    },
    tencenthunyuan: {
      description:
        '騰訊混元是由騰訊研發的大語言模型，具備強大的中文創作能力，複雜語境下的邏輯推理能力，以及可靠的任務執行能力。',
      title: '騰訊混元 (Tencent Hunyuan)',
      use_cases: [
        '提供文檔創作、文本潤色、文本校閱、表格及圖表生成，提高創作效率',
        '提供會議答疑、會議總結、會議待辦事項整理等功能，簡化會議操作並提高效率',
        '智能化廣告素材創作，提高營銷內容製作效率',
        '構建智能導購，幫助商家提升服務質量和服務效率',
      ],
    },
    third_party_integration: '第三方整合',
    tongyiqianwen: {
      description:
        '通義千問是阿里雲研發的大規模語言模型，能夠生成各種類型的文本，如文章、故事、詩歌等，並能根據用戶的需求提供定制化的回答和服務，幫助用戶解決問題和完成任務。',
      title: '通義千問 (Qwen)',
      use_cases: [
        '對數據表中的記錄進行匯總並生成報告',
        '基於數據表的記錄，生成郵件正文',
        '生成博客文章、新聞稿等文本內容',
      ],
    },
    twitter: {
      create_new_integration: '連接到新的X(推特)賬戶',
      description:
        '透過 OAuth 方式連接 Twitter 帳號，實現推文的自動化創建。結合自動化流程，能夠在新聞發布、日常更新或行銷活動等場景中自動推送推文，實現定時資訊發布。有助於保持媒體帳號的活躍，增加與粉絲的互動。',
      form_item_1_label: 'Client ID',
      form_item_1_placeholder: '請輸入 Client ID',
      form_item_2_label: 'Client secret',
      form_item_2_placeholder: '請輸入 Client secret',
      title: 'X(Twitter) OAuth2.0',
    },
    twitter_oauth_1a: {
      access_token_label: 'Access Token',
      access_token_placeholder: '輸入 Access Token',
      access_token_secret_label: 'Access Token Secret',
      access_token_secret_placeholder: '輸入 Access Token Secret',
      api_key_helptext:
        '你可以在 Twitter 開發者平台中找到 Consumer Key， https://developer.x.com/en/portal/projects-and-apps',
      api_key_label: 'API Key',
      api_key_placeholder: '輸入 API Key（Consumer Key）',
      api_secret_label: 'API Secret',
      api_secret_placeholder: '輸入 API Secret（Consumer Secret）',
      description:
        '通過 OAuth1.0a User Context 方式連接 Twitter 帳號，實現圖片、動圖、視頻資源的上傳、編輯。結合自動化流程，可以發布帶有媒體素材的推文。',
      title: 'X(Twitter) OAuth1.0a',
    },
    vika: {
      description: 'Vika 用於連接和管理 Vika 數據庫，支持數據查詢、插入、更新和刪除，幫助用戶高效地處理和存儲數據。',
      title: 'Vika',
      vika_token: 'Vika API Token',
      vika_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
    },
    webhook: {
      description:
        '使用 Webhook 接收和處理來自外部系統的 HTTP 請求。結合自動化功能，它可以在接收到特定事件時自動觸發數據更新、通知或工作流執行等操作。這種集成有助於簡化流程，確保對外部觸發器的及時響應，從而提高整體系統的效率和連接性。',
      form_item_1_label: 'Webhook Url',
      form_item_1_placeholder: 'Webhook Url',
      title: 'Webhook',
    },
    wechat: {
      description: '將Bika帳號與您的微信帳號進行綁定，實現微信掃碼即可登錄Bika，方便快捷。',
      title: '微信登录',
    },
    wecom: {
      description:
        '透過企微群機器人的 Webhook，實現自動化發送消息至企微群組。結合自動化流程，可用於企業內部即時推送項目更新、系統通知或會議提醒。它能確保團隊成員在微信群中獲取到即時且重要的通知，有助於提升團隊協作和資訊傳遞的效率。',
      form_item_1_label: 'Webhook Url',
      form_item_1_placeholder: 'Webhook Url',
      title: '企業微信群機器人',
    },
    zapier: {
      description:
        'Zapier 是一個自動化平台，通過零代碼或低代碼解決方案幫助用戶連接應用程序和服務，從而簡化工作流程。通過集成 Bika 的數據表和自動化功能，使您的數據在平台之間無縫流動。',
      title: 'Zapier',
      use_cases: [
        '使用 Zapier 將數據發佈到數千個應用程序',
        '從 Zapier 自動化中創建新的數據表記錄',
        '當有新的表單提交時，激活 Zapier 自動化',
        '從 Zapier 自動化中更新數據表記錄',
        '從 Zapier 自動化中刪除數據表記錄',
      ],
    },
    zoom: {
      description:
        'Zoom 集成允許使用者在系統中直接安排和管理 Zoom 會議。通過自動化流程，可以在特定事件觸發時自動創建會議、發送邀請或提醒參會人員。適用於需要方便快捷地管理線上會議和視頻通話的場景。',
      title: 'Zoom',
      use_cases: [
        '為新的表單提交創建 Zoom 網絡研討會註冊人',
        '在事件觸發時自動創建 Zoom 會議',
        '向參與者發送 Zoom 會議提醒',
        '將新的 Zoom 錄音上傳到數據表',
        '生成 Zoom 會議報告和摘要',
      ],
    },
  },
  invite: {
    copy_link: '複製連結',
    create_invite_description: '通过您创建的链接加入团队的成员将被自动分配到相应的小组和角色，并且双方都会获得',
    create_invite_loading: '創建中',
    create_public_invitation_link: '創建公開邀請連結',
    created_public_invitation_link: '已創建的公開邀請連結',
    delete_link: '刪除連結',
    email_invite_table: {
      createdAt: '創建時間',
      delete: '刪除',
      email: '郵箱',
      operation: '操作',
      resend: '重發',
      status: '狀態',
    },
    input_invite_email_invalid: '郵箱格式不正確',
    input_invite_member_email: '輸入郵箱',
    input_invite_one_or_more_email: '輸入一個或多個電子郵件地址，用回車鍵確認',
    invite: '邀請',
    invite_by_email_desc: '通過郵箱邀請加入的成員，雙方都獲得',
    invite_by_email_desc_next: '可用於升級空間站、購買高級模板、兌換紀念品。',
    invite_description:
      '经由该链接加入的团队成员会被自动划分至该小组與角色之中，在成功加入之后，链接的创建者以及首次加入空間站的成員皆可獲取',
    invite_description_next: '此幣能夠用於空間站的升級、高級模板的購置以及紀念品的兌換。',
    invite_identify: '邀請身份',
    invite_identify_guest: '訪客',
    invite_identify_guest_desc: '合作夥伴或外部協作者，被邀請進入空間站參與特定任務，默認無法查看節點資源',
    invite_identify_member: '成員',
    invite_identify_member_desc: '组织或团队的内部协作者，共同参与项目工作',
    invite_link_created_by: '創建的邀請連結',
    invite_link_created_fail: '創建失敗',
    invite_link_created_success: '創建成功',
    invite_link_have_coins: '你當前有',
    invite_members: '邀請成員',
    invite_outsider_invite_input_already_exist: '郵箱输入重复',
    invite_people: '邀請人員',
    invite_people_by_email: '透過電子郵件邀請',
    invite_people_by_email_button: '邀請',
    invite_people_by_email_description: '用逗號分隔多個電子郵件地址',
    invite_people_by_email_placeholder: '電子郵件地址',
    invite_people_by_link: '透過連結邀請',
    invite_people_by_link_button: '複製',
    invite_people_by_link_copied: '已複製連結',
    invite_people_by_link_description: '分享此連結邀請人員加入您的團隊',
    invite_people_description: '邀請人員加入您的團隊',
    invite_record: '邀請記錄',
    invite_record_description:
      '當被邀請者加入後，邀請者和被邀請者都可獲得獎勵，可用於升級空間站、購買高級模板、兌換紀念品。',
    invite_role: '角色（可選）',
    invite_role_placeholder: '請選擇角色',
    invite_status: {
      accepted: '已接受',
      pending: '待接受',
      rejected: '已拒絕',
    },
    invite_team: '小組（必選）',
    invite_team_placeholder: '請選擇小組',
  },
  launcher: {
    ai: 'AI',
    ai_launcher: 'AI 啟動器',
    ai_launcher_description:
      'AI 啟動器是一款智能快捷功能，旨在幫助用戶高效地執行各種操作。通過簡潔直觀的界面，您可以輕鬆地執行命令、查找文件、配置空間站等。無論是日常任務還是複雜操作，AI 啟動器都能提供快速高效的解決方案，大幅提升工作效率。',
    ask_me_anything: '問我任何問題...',
    chat_history: '聊天歷史',
    chat_replay: '聊天回放',
    command: '指令',
    command_not_found: '未找到結果，請詢問 AI。',
    commands: '指令',
    commands_placeholder: '你想執行哪些指令...',
    database_record: '數據記錄',
    database_record_placeholder: '查找數據記錄...',
    document: '文檔',
    document_placeholder: '按名稱查找文檔...',
    file: '檔案',
    file_placeholder: '按名稱查找文件或文件夾...',
    getting_started: '新手入門',
    help: '幫助',
    help_placeholder: '你需要什麼幫助...',
    launcher: '啟動器',
    mission: '智能任務',
    node: '節點資源',
    pending_todos: '待辦任務',
    recently: '最近',
    record: '記錄',
    reminder: '提醒',
    router: '路由器',
    search_tip: '支持拼音模糊搜索',
    shortcuts: '捷徑',
    smart: '智能',
    task: '待辦',
    templates: '模板',
    ui_modal: 'UI 模態',
    unread_reports: '未讀報告',
    url: 'URL',
  },
  license: {
    apply_description: '獲取 Bika.ai 私有化安裝包及教程。',
    apply_title: 'Bika.ai 私有化版安裝申請',
    company_name: '貴公司/團隊的名稱是什麼？',
    company_size: '貴公司/團隊有多少人？',
    copy_your_license_key: '在這裡複製您的許可證密鈅',
    download_and_install_self_host: '下載並安裝私有化',
    get_self_host_license_key: '獲取私有化部署的許可證密鑰（License Key）',
    get_trial: '你可以獲得一次 30 天的試用機會',
    industry: '貴公司所在行業是什麼？',
    license_key: '許可證密鈅',
    license_key_expired: '許可證密鈅已過期',
    license_key_expired_desc: '您的試用期已過期',
    please: '請',
    submit: '安裝 Bika.ai 私有化包',
    trial_desc: '目前，您正处于30天的试用期内。如果您在使用过程中遇到任何问题',
  },
  mission: {
    after_action: '任務完成後的下一步動作',
    allow_complete_manually: '允許手動完成',
    allow_reject: '允許拒絕',
    assign_type: '分配類型',
    assign_type_dedicated: '专属任務',
    assign_type_dedicated_description: '任務會分配給多個成員，但每個成員必須獨立完成任務',
    assign_type_shared: '共享任務',
    assign_type_shared_description: '任務會分配給多個成員，任何一個成員完成任務後即標記為已完成',
    assigned: '分配給',
    assignee: '分配人',
    button_text: '按鈕文字',
    can_transfer: '允許轉移',
    complete: '標記完成',
    completedAt: '完成時間',
    confirm: '同意',
    createAt: '發起時間',
    create_record: '創建記錄',
    create_success: '創建任務成功',
    database_record: '數據表記錄',
    database_view: '數據表視圖',
    date_time_now: '現在',
    date_time_today: '今天',
    description: '任務內容',
    description_placeholder: '請輸入任務內容',
    detail: '查看詳情',
    dueAt: '截止時間',
    dynamic_datetime: '動態日期時間',
    dynamic_datetime_type: '動態日期時間類型',
    end_time: '結束時間',
    features_list: 'Missions List',
    force_popup: '強制彈出',
    go_to_mission: '前往任務',
    initiator: '發起人',
    know_and_next: '已了解，進入下一步',
    mission: '智能任務',
    mission_description:
      'Mission is a smart, automative, traceable tasks differs from typical tasks or to-do lists, which you have to check off by yourself.\nFor example, consider the Create Record Mission: when a user receives it, the mission will automatically be marked as complete only when the required record has been created.',
    mission_name: '任務名稱',
    mission_name_placeholder: '請輸入任務名稱',
    mission_type: '智能任務類型',
    modal_content_mission_invalid: '該任務所屬的資源已經無法訪問，是否刪除任務',
    modal_title_mission_invalid: '溫馨提示',
    more_setting: '更多設置',
    msg_mission_completed: '任務被完成！',
    msg_mission_rejected: '任務已拒絕。',
    msg_no_next_action: '無下一步操作。',
    msg_transfer_not_supported: '此任務不支持轉交。',
    next: '下一步',
    placeholder_assign_type: '請選擇分配類型',
    processing: '處理中',
    progress: '進度',
    reject: '拒絕任務',
    reminder: '任務的提醒時間',
    reminder_description: '提醒描述',
    reminder_title: '提醒標題',
    reminder_to: '提醒對象',
    repeat: '設置重複提醒',
    seconds: '({seconds}秒)',
    show_end_time: '設置結束時間',
    show_start_end_time: '設置任務的開始和結束時間',
    show_start_time: '設置開始時間',
    specific_datetime: '特定日期時間',
    start_time: '開始時間',
    taskIntroduction: '任務介紹',
    taskObjective: '任務目標',
    taskStatus: '任務狀態',
    time_internal: '時間間隔',
    tips_for_readme_mission: '在新標籤頁打開此文檔，有助於您邊看邊實踐。',
    transfer: '轉交任務',
    type: {
      ai_create_records: {
        description: 'AI 會根據指定的規則，自動創建記錄',
        name: 'AI 創建記錄',
      },
      approval: {
        description: '只有一個步驟的任務，任務負責人可以點擊同意、拒絕、或移交他人。比如請假審批',
        name: '審批任務',
      },
      comment_record: {
        description: '收到該任務的成員，需要對指定的記錄進行評論，方可完成任務',
        name: '對記錄進行評論',
      },
      create_multi_records: {
        description: '收到該任務的成員，需要創建指定數量的數據表記錄',
        name: '創建若干條記錄',
      },
      create_record: {
        description: '收到該任務的成員，需要創建指定數據表的一條記錄',
        name: '創建記錄',
      },
      create_task: {
        description: '收到該任務的成員，需要創建一個新的任務',
        name: '創建任務',
      },
      enter_view: {
        description: '收到該任務的成員，會被引導查看數據表的指定視圖',
        name: '查看指定視圖',
      },
      google_meet: {
        description: '帶有 Google 會議鏈接的任務，適合用於線上會議的邀約',
        name: 'Google 會議',
      },
      install_template: {
        description: '要求收到該任務的成員完成一次模板的安裝',
        name: '安裝模板',
      },
      invite_member: {
        description: '要求收到該任務的成員完成一次邀請成員的操作',
        name: '邀請成員',
      },
      quest: {
        description: '由多個業務相關性的子任務共同構成的一個任務集。比如新手任務場景',
        name: '系列任務',
      },
      read_markdown: {
        description: '收到該任務的成員，要求查看指定的 Markdown 文檔',
        name: '查看 Markdown 文檔',
      },
      read_template_readme: {
        description: '收到該任務的成員，要求查看指定模板的說明文檔',
        name: '閱讀模板說明',
      },
      redirect_space_node: {
        description: '收到該任務的成員，會被引導至指定的空間節點',
        name: '重定向空間節點',
      },
      reminder: {
        description: '收到該任務的成員，會收到一條提醒消息',
        name: '提醒',
      },
      review_record: {
        description: '收到該任務的成員，會被引導查看數據表的某條記錄',
        name: '查看指定記錄',
      },
      sequence: {
        description: '包含多個步驟，需按順序完成的任務。例如多層級的審批工單',
        name: '序列任務',
      },
      set_space_name: {
        description: '收到該任務的成員，會被引導至空間站設置界面，完成空間站名稱的設置',
        name: '設置空間站名稱',
      },
      submit_form: {
        description: '創建任務並指派給某成員，請求他填寫指定的表單',
        name: '提交表單',
      },
      submit_multiple_form: {
        description:
          '收到任務的成員，需要將指定的若干份表單全部填寫完畢，該任務才會標記完成。例如合同訂單的錄入，銷售需要填寫客戶、合同、付款記錄三份表單',
        name: '提交若干份表單',
      },
      ui_launcher: {
        description: '用於啟動用戶界面相關的功能或應用',
        name: 'UI 啟動器',
      },
      update_record: {
        description: '收到該任務的成員，需要完成對指定的記錄的編輯，方可完成任務',
        name: '更新記錄',
      },
      voov_meet: {
        description: '帶有 Voov 會議鏈接的任務，適合用於線上會議的邀約',
        name: 'Voov 會議',
      },
      zoom_meet: {
        description: '帶有 ZOOM 會議鏈接的任務，適合用於線上會議的邀約',
        name: 'Zoom 會議',
      },
    },
    update_record: '更新記錄',
  },
  navbar: {
    agent_builder: '生成助手',
    agent_builder_description: '我是你的生成助手，我不招聘人，我只建立智能體',
    beta: '測試',
    chief_of_staff: '辦公室助理',
    chief_of_staff_description: '我是你的辦公室助理，可協助你完成資訊檢索、內容生成等各類工作',
    expert: '專家',
    explore: '探索',
    home: 'AI總助',
    personal: '個人',
    personal_resources: '個人的な資源',
    private: '私人資源',
    private_description: '私人資源：專屬於你的資源空間，可以自由編輯、新增或刪除資源',
    report: '智能報告',
    resources: '資源',
    shortcuts: '快捷方式',
    shortcuts_description: '捷徑：包含管理員或自行設置的捷徑，快速訪問常用資源',
    shortcuts_resources: 'ショートカット資源',
    smart: '智能',
    smart_description: '智能：支持全局搜索、智能任務與智能報告，同時配備回收站和模板應用',
    space_launcher: '空間啟動器',
    space_launcher_description: '空間的啟動器',
    super_agent: '超級智能體',
    team: '團隊',
    team_resources: '資源',
    team_resources_description: '團隊資源：與團隊成員共同編輯、新增或刪除資源，方便團隊協作與資源共享',
    todo: '智能待辦',
  },
  node: {
    delete_node: '刪除節點',
    delete_node_description: '確定刪除節點「{name}」嗎？',
    delete_node_success: '刪除節點"{name}"成功',
    edit_folder: '編輯文件夾',
    empty_folder: '空文件夾',
    export_attachments: '導出附件',
    export_bika_file: '導出 .bika 文件',
    export_excel: '導出為 Excel 文件',
    export_template: '導出 .bika 模版(內部)',
    import_bika_file: '從 .bika 文件導入',
    jump_to_node: '跳轉到節點',
    node: '節點',
    node_detach: '脫離模板',
    node_detach_fail: '脫離模板失敗',
    node_detach_success: '脫離模板成功',
    node_guide: {
      automation: {
        description: 'Automation allows you to set triggers and actions to automate workflow processes.',
        feature1: '• Multiple trigger conditions',
        feature2: '• Rich action types',
        feature3: '• Conditional branching logic',
        feature4: '• Execution history',
        tip1: '💡 You can set multiple trigger conditions',
        tip2: '💡 Test automation rules before enabling them',
        title: 'Welcome to Automation',
      },
      dashboard: {
        description: 'Dashboards help you visualize data, create charts and reports to gain insights into data trends.',
        feature1: '• Multiple chart types',
        feature2: '• Real-time data updates',
        feature3: '• Custom layouts',
        feature4: '• Data filters',
        tip1: '💡 You can add multiple data sources',
        tip2: '💡 Support exporting charts and data',
        title: 'Welcome to Dashboard',
      },
      database: {
        description: 'Database is the core feature of Bika, allowing you to store and manage data in a structured way.',
        feature1: '• Create custom field types',
        feature2: '• Multiple views to display data',
        feature3: '• Powerful filtering and sorting',
        feature4: '• Collaboration and permission management',
        tip1: '💡 You can drag and drop to reorder fields',
        tip2: '💡 Use views to create different data display modes',
        title: 'Welcome to Database',
      },
      default: {
        description: 'This is a powerful feature module that makes your work more efficient.',
        feature1: '• Intuitive and easy-to-use interface',
        feature2: '• Powerful feature set',
        feature3: '• Flexible configuration options',
        tip1: '💡 Explore various features to discover more possibilities',
        title: 'Welcome to this Feature',
      },
      form: {
        description:
          'Forms allow you to easily collect and organize information, with data automatically syncing to the associated database.',
        feature1: '• Drag-and-drop form designer',
        feature2: '• Multiple field type support',
        feature3: '• Automatic data validation',
        feature4: '• Conditional logic support',
        tip1: '💡 You can set display conditions for fields',
        tip2: '💡 Form submissions sync to the database in real-time',
        title: 'Welcome to Form',
      },
      got_it: 'Got it',
      main_features: 'Main Features:',
      tips: 'Tips:',
    },
    node_info: '檔案資訊',
    node_name: '節點名稱',
    node_update: '更新模板',
    permission: {
      add_permission_message: '請添加需要設置權限的成員、小組或者角色。',
      can_comment: '可以評論',
      can_edit: '可以編輯',
      can_edit_content: '僅可更新',
      can_edit_content_desc: '可以對已有內容進行更新，數據表中不能增刪記錄。',
      can_edit_desc: '可以對內容進行編輯，數據表中可以增刪記錄。',
      can_view: '可以查看',
      can_view_desc: '不能編輯，只可查看節點資源的內容。',
      description:
        '您可以根據不同角色、成員、部門，進行精細地分配權限，從而有效控制數據訪問和操作權限。通過權限管理，您可以確保每個團隊成員只能訪問和操作與其職責相關的功能和數據，大大提高了數據的安全性和隱私性。',
      full_access: '可以管理',
      full_access_desc: '擁有節點資源的所有操作權限。',
      login_to_edit: '登錄後可以編輯',
      no_access: '禁止訪問',
      no_access_desc: '無權限查看資源。',
      permission_description: {
        CAN_COMMENT: '您可以查看並評論該資源',
        CAN_EDIT: '您可以編輯內容並管理資料庫中的記錄',
        CAN_EDIT_CONTENT: '您可以更新現有內容，但不能增刪記錄',
        CAN_VIEW: '您只能查看該資源的內容',
        FULL_ACCESS: '您擁有該資源的所有操作權限',
        NO_ACCESS: '您沒有權限訪問該資源',
      },
      remove: '移除權限',
      title: '權限管理',
    },
    publish_to_template_center: '發布到模板中心',
    republish: '再次發布',
    view_original_template: '查看原模板',
  },
  notification: {
    all: '全部',
    all_notifications_marked_as_read: '所有通知已標記為已讀',
    app_notification: 'App 通知',
    app_push_notification: 'App 推送通知',
    browser_notification: '瀏覽器通知',
    clean_all_notifications: '清除所有通知',
    confirm_to_clean_all_notifications: '確認清除所有通知?',
    mail_notification: '郵件通知',
    mark_all_as_read: '全部標記為已讀',
    new_agenda: '新議程',
    new_mission: '新任務',
    new_report: '新報告',
    no_notification_so_far: '暫無通知',
    notification: '通知',
    notification_settings: '通知設定',
    notification_type: '通知類型',
    sms_notification: '簡訊通知',
    system_message: '系統訊息',
    unread: '未讀',
  },
  ok: '確定',
  pagination: {
    load_more: '加載更多',
    loading: '加載中...',
    no_more: '沒有更多了',
  },
  pricing: {
    business: '商業版',
    change_your_plan: '變更您的方案',
    community: '社區版',
    currency_symbol: '$',
    customize: '定製',
    customize_seat: '定製席位',
    enterprise: '企業版',
    enterprise_private_cloud: '專有雲版',
    enterprise_self_hosted: '私有化版',
    experience_now: '立即體驗',
    features: {
      advanced_automation_integrations: '高級自動化集成',
      advanced_automation_integrations_tips: '高級自動化觸發器和連接器，用於連接外部工具，如高級AI模型等',
      api_calls_per_month: '每月API調用次數',
      authorized_email_domain: '授權的電子郵件域名',
      automation_integrations: '自動化集成',
      automation_integrations_tips: '自動化觸發器和執行器，用於連接外部工具',
      automation_run_history: '自動化運行歷史保留天數',
      automation_runs_per_month: '每月的自動化運行次數',
      browser_notifications: '瀏覽器通知',
      byok_support: '使用私有的AI API KEY',
      coming_soon: '即將推出',
      community: '社區',
      credits_per_seat_per_month: '每位成員每月獲得AI積分',
      credits_per_seat_per_month_tips:
        '積分將發放至空間站，按「成員數 × 每月積分」計算，並於每月重置。積分用於調用 AI 模型。',
      custom_domain: '自定義域名',
      data_sync: '數據同步',
      email_support: '郵件支持',
      export_bika_file: '導出 .bika 文件',
      export_bika_file_tips: '.bika 是一種文件格式，可以將完整的數據和結構（如關聯、自動化等）導入到 Bika 中',
      export_excel_csv: '導出 Excel/CSV 文件',
      help_center: '幫助中心',
      im_support: 'Whatsapp專屬客服',
      im_support_tips: '您可以通過Whatsapp與我們的客服人員聯繫，獲得即時的幫助',
      integration_instances: '集成實例',
      managed_emails_per_month: 'Bika郵件服務(每月)',
      managed_emails_per_month_tips: 'Bika官方郵件服務,在自动化流程中使用bika域名發送郵件',
      max_guest: '訪客上限',
      max_records_per_database: '單個數據表的記錄總數',
      max_records_per_space: '空間站的記錄總數',
      max_seat: '人數上限',
      mirror_sync: '鏡像',
      missions_per_month: '每月的任務總量',
      mobile_notifications: 'APP 通知',
      planned_feature: '計劃中的功能',
      private_template: '私有模板',
      professional_services: '專屬 V+ 顧問',
      publish_and_share: '發布與分享',
      publish_template: '發布模板',
      remove_logos: '移除品牌標識',
      remove_logos_tips: '界面中將不會出現 Bika 的品牌標識',
      reports_per_month: '每月的報告總量',
      resource_nodes: '資源節點總數',
      resource_permissions: '資源權限',
      self_hosted: '私有化部署',
      self_hosted_tips: '在自己的伺服器上部署 Bika，甚至可以將安裝實例進行白標（即去除品牌標識，進行品牌定制）',
      sell_template: '銷售模板',
      sms_notifications: '短信通知',
      smtp_emails_per_month: 'SMTP郵件數',
      space_audit_log: '空間站審計日誌',
      space_sessions_log: '空間站會話日誌',
      storage: '存儲容量',
      storage_tips: '單個工作空間中所有資料庫和文檔中存儲的附件總存儲上限',
      sub_admin: '子管理員',
      sub_domain: '子域名',
      unlimited: '無限制',
      user_sessions_log: '用戶會話日誌',
      webinar: '在線會議',
    },
    for_businesses_and_enterprises: '面向企業和大型企業',
    for_individual_and_teams: '面向個人和團隊',
    free: '免費',
    free_number: '$0',
    free_trial_7_days: '7天免費，之後每年 {price}',
    includes_word: '包括',
    modal_title: '變更您的方案',
    month: '月',
    monthly: '每月',
    oncely: {
      oncely_code: 'Oncely 兌換碼',
      oncely_code_management: '兌換碼管理',
      oncely_code_placeholder: '請輸入 Oncely 兌換碼',
    },
    page_section_detail_title: '比較方案和功能',
    page_section_question_title: '產品價格問答',
    payment_successful: 'Congratulations on successfully upgrading to {plan}',
    payment_successful_description:
      'You have successfully upgraded to {plan}. You have received the following benefits:',
    per_seat: '/ 人',
    plus: 'Plus版',
    popular: '最受歡迎',
    price: '價格',
    pro: 'Pro',
    question: {
      answer_1: '答案 1',
      question_1: '問題 1',
    },
    renew_and_cancel: '計劃每年自動續訂，直至取消',
    seat: '席位數',
    team: '團隊版',
    user: '使用者',
    view_benefit_details: '檢視權益詳情',
    view_detail: '查看詳情',
    year: '年',
    yearly: '每年',
  },
  publish_template: {
    allow: '允許',
    allow_detach_description: '允許後，安裝過模板的用戶，可以脫離模板，脫離後，無法從原模板升級，但可以用於二次發布',
    allow_users_to_detach_template: '允許其他用戶脫離模板',
    author_space_title: '顯示空間站名稱',
    author_user_title: '顯示我的暱稱',
    cancel: '取消',
    category: '分類',
    coming_soon: '敬請期待的模板應用程式',
    coming_soon_description: '所有使用者都可以在模板應用程式中搜尋到您的模板,但無法安裝',
    configure_template: '配置模板',
    forbid: '禁止',
    init_mission: '初始化任务',
    init_mission_description: '你可以为该模版设置初始化任务，更好的引导使用者',
    keywords: '關鍵字',
    private: '私有',
    private_description: '私有則僅空間站成員可見，公開則所有 bika 用戶都可見',
    public: '公開的模板應用程式',
    public_description: '所有使用者都可以在「公開的模板應用程式」中搜尋並安裝您的模板',
    publish_data: '發布數據',
    publish_data_description: '發布數據將會被公開，請確保數據不包含任何隱私信息',
    publish_success: '發布成功',
    publish_template: '發布模板',
    publish_to_template_center: '發布到模板中心',
    space: '空間站內的模板應用',
    space_description: '只有此空間站的成員可以在「您空間站的模板應用程式」中搜尋並安裝您的模板',
    template_author: '作者資訊',
    template_id: '模板 ID',
    template_published: '模板已發布',
    template_published_description_coming_soon: '所有使用者都可以在模板應用程式中搜尋到您的模板',
    template_published_description_public: '所有使用者都可以在「公開的模板應用程式」中搜尋並安裝您的模板',
    template_published_description_space: '只有此空間站的成員可以在「您空間站的模板應用程式」中搜尋並安裝您的模板',
    template_visibility: '模板公開',
    use_cases: '使用場景',
    version_already_exist: '版本已存在',
    version_description: '版本說明',
    version_number: '版本號',
    view_template_center: '去模板中心查看',
  },
  record: {
    active: '動態',
    activity: {
      anonymous: '匿名者',
      comment_tip: 'Shift+Enter 換行，Enter 發送',
      empty_activity: '暫無動態',
      empty_comment: '暫無評論',
      just_changelog: '僅變更記錄',
      just_comment: '僅評論',
      load_more: '加载更多',
      loading: '加载中...',
      no_more: '没有更多了',
      record_comment_and_change: '記錄的評論和修改歷史',
    },
    add_attachment: '添加附件',
    add_local_file: '添加本地文件',
    create: '創建',
    create_record: '創建紀錄',
    create_record_button: '創建紀錄',
    create_record_description: '創建新紀錄',
    create_record_failed: '創建紀錄失敗',
    create_record_success: '創建紀錄成功',
    delete_comment: '刪除評論',
    delete_comment_description: '確定要刪除這則評論嗎？',
    delete_record: '刪除紀錄',
    drop_file_upload: '拖放文件上傳',
    empty_comment: '暫無評論',
    first_record: '第一條記錄',
    go_next: '下一條記錄',
    go_previous: '返回上一條記錄',
    input_comment_placeholder: '輸入評論或@提及某人',
    max_file_size: ': 文件最大不超過 500MB',
    modify_record: '修改紀錄',
    no_next_record: '沒有下一條記錄',
    no_previous_record: '沒有上一條記錄',
    paste_or_drop_file_upload: '粘貼或拖放文件上傳',
    record: '紀錄',
    record_comment: '紀錄評論',
    record_delete: '紀錄刪除',
    record_detail: '紀錄詳情',
    record_detail_description:
      '在 Bika.ai 中，使用者可以點開每一條記錄。記錄詳情是該條記錄的展開視圖，包含該條記錄的所有詳細信息。',
    record_pin: '紀錄置頂',
    record_unnamed: '未命名記錄',
    request_modify: '請求修改',
    request_new_record: '請求新建記錄',
    select_date: '選擇日期',
    select_from_files: '從文件選擇',
    select_from_gallery: '從相冊選擇',
    select_member: '選擇成員',
    select_option: '選擇選項',
    tab_general: '基礎',
    take_photo_or_record_video: '拍照或錄製視頻',
  },
  redeem: {
    oncely: {
      congratulations: '恭喜您！',
      contact_service: '聯繫客服',
      email: '電子郵件',
      email_code: '驗證碼',
      enter_space: '進入空間站',
      input_email: '請輸入電子郵件地址',
      input_email_code: '請輸入驗證碼',
      input_oncely_code: '請輸入兌換碼',
      logout: '退出登錄',
      new_user_tip: '請知悉，通過兌換碼將獲得一個包含付費權益的全新空間站，舊空間站不支持兌換碼升級！',
      new_user_tip_ignore_code:
        '啟用成功後，將創建一個新的空間站，可全面使用對應套餐的所有功能。請注意，現存空間站無法通過此方法升級。',
      old_user_tip:
        '檢測到您曾經註冊過，已有 {spaceCount} 個空間站存在。啟用成功後，將創建一個新的空間站，可全面使用對應套餐的所有功能。請注意，現存空間站無法通過此方法升級。',
      oncely_code: '兌換碼',
      question: '遇到問題',
      reedem_oncely: '兌換您的交易',
      submit: '提交',
      success_redeem: '✨ 新空間已建立，包含的高級功能已解鎖！',
      you_have_used_fragment_1: '您目前已登入為',
      you_have_used_fragment_2: '。點擊下方的「提交」按鈕，立即建立一個新空間，並啟用您的訂閱方案對應的所有功能。',
    },
  },
  referral: {
    bika_coins_description: '積分可用來兌換各種服務和產品',
    check_usage: '檢查使用情況',
    current_space_plan: '當前空間站計劃',
    earn_bika_coins: '賺取積分',
    method_1: '方法1：通過邀請鏈接邀請',
    method_2: '方法2：通過邀請碼邀請',
    method_3: '方法3：邀請加入空間',
    method_4: '方法4：安裝移動應用程式',
    other_referral_code: '邀請碼',
    referral: '推薦',
    referral_code: '推薦碼',
    referral_rewards: '填寫他人的邀請碼，雙方都可獲得',
    reward_history: '獎勵歷史',
    total: '總計：',
    view_my_referral_code: '查看我的邀請碼',
    your_bika_coins: '你的積分',
  },
  reminder: {
    no_reminder_so_far: '暫無提醒',
    remind: '提醒',
    remind_me: '提醒我',
    reminder: '提醒',
    reminders: '提醒',
  },
  report: {
    create_report: '發送報告',
    create_report_description:
      '您可以通過自動化流程觸發生成報告，報告內容會以Markdown等格式發送至指定人員或群組。此報告為AI根據您的設定自動生成數據和信息，以幫助您更好地了解項目進展。',
    mark_all_as_read: '全部標記為已讀',
    mark_all_as_read_content: '確認是否一鍵將所有未讀報告標記為已讀',
    mark_as_read: '標記為已讀',
    no_report_so_far: '暫無報告',
    read: '已讀',
    read_report: '已閱讀報告',
    report: '報告',
    report_description: '由AI或自動化基於設定的規則或數據進行生成報告材料，形式類似於一篇郵件、文章或者文檔。',
    report_detail: '報告詳情',
    report_detail_description:
      '展示報告的詳細信息，每當自動化流程結束後AI會根據用戶設定的內容自動生成報告，幫助用戶更好地回顧工作的進程。',
    report_info: '報告資訊',
    reports: '報告',
    unread: '未讀',
  },
  resource: {
    add_filter_condition: '添加篩選條件',
    add_shortcut_success: '添加到捷徑成功',
    ai_page: {
      settings_html_copilot: 'AI 助手',
      settings_html_description: '在下方輸入 HTML 頁面代碼，點擊保存後，頁面將會自動生成',
      settings_html_placeholder: '請輸入 HTML 代碼',
      settings_html_title: 'HTML 頁面',
      welcome: '在右側告訴 AI 助手你的想法，它會為你生成一個頁面',
    },
    all_resources: '所有資源',
    automation_name: '自動化名稱',
    can_not_create_integration: '无法添加集成，找管理员配置',
    cancel_excel_import: '取消導入',
    cancel_excel_import_description: '你確定要取消導入嗎',
    cancel_incremental_import: '取消增量導入',
    cancel_incremental_import_description: '你確定要取消增量導入嗎',
    change_cover: '修改封面',
    change_form_logo: '修改Logo',
    close_export_modal_warning: '你正在導出數據中，確定要關閉嗎？',
    content_changed_warning: '數據表內容已被更新，請刷新查看最新內容',
    content_is_empty: '內容為空',
    create_ai_agent_success: '成功創建 AI 智能體 "{name}"',
    create_ai_page_success: '成功創建 AI 頁面 "{name}"',
    create_automation_action_success: '成功創建自動化執行器',
    create_automation_success: '成功創建自動化 "{name}"',
    create_dashboard_success: '成功創建儀表板 "{name}"',
    create_database_success: '成功創建資料表 "{name}"',
    create_document_success: '成功創建文檔 "{name}"',
    create_folder_success: '成功創建文件夾 "{name}"',
    create_form_success: '成功創建表單 "{name}"',
    create_from_blank: '從空白創建',
    create_from_blank_automation_description: '從頭構建自動化流程',
    create_from_blank_dashboard_description: '創建新儀表板以可視化數據',
    create_from_blank_database_description: '創建新數據表以存儲數據',
    create_from_blank_document_description: '創建新文檔以編寫內容',
    create_from_blank_folder_description: '創建新資料夾以組織資源',
    create_from_template:
      '從下方中選擇一個符合場景的的模板快速開始，如果沒有合適的模板，也可以點擊“創建空白資源”進行自定義搭建。',
    create_integration: '添加整合',
    create_mirror_success: '成功創建鏡像 "{name}"',
    create_view: '添加視圖',
    create_view_success: '成功創建視圖 "{name}"',
    dashboard_description: '儀表板描述',
    dashboard_name: '儀表板名稱',
    data_is_fetching: '數據正在獲取中...',
    day: '天',
    delete_field: '刪除欄位',
    delete_field_description: '刪除欄位後將無法恢復，確定要刪除欄位 ',
    delete_folder_error: '删除文件夹 "{name}" 失败',
    delete_folder_success: '成功刪除文件夾 "{name}"',
    delete_resource_description: '确定要刪除資源 "{name}" 吗?',
    delete_resource_error: '删除资源 "{name}" 失败',
    delete_resource_success: '成功刪除資源 "{name}"',
    delete_view: '刪除視圖',
    delete_view_description: '您確定要刪除視圖：{name}？',
    delete_view_error: '删除视图 "{name}" 失败',
    delete_view_success: '成功刪除視圖 "{name}"',
    description: '節點資源是一種特殊的資源，它可以是數據庫、自動化、表單等節點實現。',
    download_again_file: '下載失敗？點此重新下載',
    download_done_file: '已解析並開始下載，您可以在瀏覽器的下載記錄中查看',
    download_loading_file: '正在處理您的請求，可能需要幾分鐘。請勿刷新或返回，以免取消操作',
    download_template: '下載模板.xlsx',
    edit_automation: '編輯自動化流程',
    edit_automation_action: '設置執行器',
    edit_automation_trigger: '設置觸發器',
    edit_dashboard: '編輯儀表板',
    edit_database: '編輯資料表',
    edit_database_view: '編輯視圖',
    edit_field: '編輯欄位',
    edit_form: '編輯表單',
    edit_template: '編輯模板',
    edit_widget: '編輯組件',
    error_import_excel: '導入失敗，錯誤信息：{message}',
    error_import_excel_button: '繼續導入',
    export_bika_file_include_data:
      '匯出成 bika 檔案（.bika），可以將你的資料夾、數據表等資源打包在一起，備份到本地。如果勾選“包含數據表記錄”，bika 檔案的體積會相應增大',
    export_bika_file_title: '導出 Bika File',
    export_data_include_data: '包含數據表記錄',
    export_for_excel: '導出為 Excel 文件',
    features_list: '節點資源類型列表',
    field: '欄位',
    field_not_found: '欄位未找到',
    fields: '欄位',
    filter_condition: '篩選條件',
    first_field_not_allow_drag: '第一列不允許拖動',
    folder_description: '文件夾描述',
    folder_empty_description: '這裡空空如也，請創建新的資源。',
    folder_empty_title: '空資料夾',
    folder_loading_description: '正在加載子資源...',
    folder_loading_title: '正在加載子資源...',
    folder_name: '文件夾名稱',
    folder_no_content: '該文件夾內暫無其他文件夾',
    folder_readme: '文件夾說明',
    form: {
      add_logo: '添加 Logo',
      click_to_view: '點擊查看',
      form_description: '請輸入描述',
      link_to_resource: '鏈接到資源：',
      submitted_successfully: '提交成功',
    },
    gallery: {
      cover: '封面圖片',
      cover_help_text: '選擇附件字段作為封面圖片，顯示在畫廊視圖中每張卡片的頂部。',
      crop_cover_image: '裁剪封面圖片',
      crop_cover_image_help_text: '圖片將被裁剪以居中並填充卡片，確保沒有空白區域。',
      custom_cards_per_row: '設置每行卡片數',
      custom_cards_per_row_help_text: '手動設置每行顯示的卡片數量。默認是自動佈局（基於屏幕分辨率）。',
    },
    home: '編輯資源',
    import_bika_file_success: '已成功匯入',
    import_bika_file_support: '支援上傳 .bika 檔案',
    import_excel_import_button: '導入',
    import_excel_records_count: '預覽僅顯示前 10 行資料，實際匯入將包含 {count} 行數據',
    import_excel_step1:
      '第一步：下載模板並填寫資料。請注意：不要更改模板中的標題，以免匯入失敗。不支持計算欄位、成員欄位、附件欄位和關聯欄位。',
    import_excel_step2: '第二步：將填寫好的 Excel 檔案拖到此處進行上傳。',
    import_file_for_box: '從 .bika 文件導入數據並新建資源',
    import_from_excel: '從 Excel 導入',
    import_from_vika: '從 Vika 導入',
    import_from_vika_helper_text:
      '輸入Vika資源ID，多個資源以逗號分隔，例如折疊1，折疊2。 \n注意：\n1.首先，確保您已整合 VIKA 應用程式。 \n2.若表格中有會員字段，請務必先將會員資料從Vika平台匯入Bika，否則匯入資料後會員欄位會自動清除。 \n3. Vika 使用者和 Bika 使用者透過電子郵件關聯。',
    import_from_vika_label: 'Vika資源ID',
    include_widgets: '包含組件',
    included_resources: '包含資源',
    incremental_import_from_excel: '從 Excel 增量導入',
    kanban: {
      add_kanban_group_card: '添加記錄',
      delete_kanban_tip_content: '刪除該分組會將其原有的記錄移至未指定分組裡',
      delete_kanban_tip_title: '刪除這個分組',
      editing_group: '編輯分組',
      group_already_exists: '該分組已經存在',
      group_by_option_or_member: '根據特定的“成員”或“單選”欄位將記錄分成若干組。',
      hide_kanban_grouping: '隱藏分組',
      kanban_add_new_group: '增加一個分組',
      kanban_no_data: '暫無數據',
      kanban_not_group: '未指定分組',
      kanban_view_limit: '看板視圖中最多可以顯示1000條記錄。請使用“篩選”功能來縮小範圍。',
      no_multiple_selection_member_field: '不支持多選的成員欄位',
      no_single_choice_or_member_field: '你當前沒有任何單選或成員欄位，去',
      please_select_single_or_member_field: '請選擇一個單選欄位或成員欄位以創建看板視圖',
    },
    layout: '佈局',
    layout_help_text: '數據記錄在視圖中的排列和顯示方式。',
    mirror_type_label: {
      database_view: '選擇已有視圖',
      node_resource: '選擇已有資源',
      view: '獨立視圖',
    },
    month: '月',
    move_resource_error: '移動失敗',
    move_resource_success: '成功移動',
    move_resource_to: '移動 {name} 至當前文件夾下',
    move_resource_to_public_description: '確定要移動至團隊資源嗎？',
    move_resource_to_public_error: '移動至團隊資源失敗',
    move_resource_to_public_success: '成功移動至團隊資源',
    new_field: '新建欄位',
    no_cover: '無封面',
    no_member_field: '沒有成員欄位',
    no_permission_operation: '您沒有權限進行操作',
    no_permission_operation_description: '抱歉，您沒有權限進行該操作，請通知管理員',
    node_detail: '節點詳情',
    not_support_import_field: '從 Excel 匯入資料並建立新數據表。請注意：不支持計算欄位、成員欄位、附件欄位和關聯欄位。',
    operation_failed: '操作失敗',
    parsing_excel_data: '數據解析中',
    placeholder_no_field: '沒有欄位',
    placeholder_no_number_field: '沒有數字類型欄位',
    placeholder_select_field: '請選擇一個欄位',
    placeholder_select_member_field: '請選擇成員欄位',
    placeholder_select_record: '請選擇一條記錄',
    placeholder_select_resource: '請選擇資源',
    placeholder_select_view: '請選擇資料表中的視圖',
    placeholder_select_widget: '請選擇一個組件',
    placeholder_view_name: '請輸入視圖名稱',
    record_detail: {
      link_new_record: '關聯新記錄',
      link_record: '關聯已有記錄',
      linked_from: '從 "{name}" 關聯記錄',
      no_linkable_record: '沒有可關聯的記錄',
      no_related_records: '暫無關聯記錄',
      record_detail_not_found: '未找到記錄詳情',
      select_record: '選擇記錄',
      tip_refresh: '你正在編輯的內容已發生變化，請複製你編輯的內容到別處保存並刷新頁面',
    },
    record_index: '記錄 {index}',
    remove_field_success: '刪除欄位成功',
    remove_folder_description: '文件夾及其包含的所有資源將被刪除，刪除後無法恢復，確定要刪除文件夾 "{name}" 嗎?',
    remove_shortcut_success: '移除捷徑成功',
    required_field: '必填字段',
    resource: '節點資源',
    resources: '資源',
    set_form_required: '設置為必填項',
    set_form_required_description: '當您將字段設置為必填時，用戶在創建或編輯記錄時必須填寫該字段。',
    success: '成功',
    success_import_excel: '已开始導入 {columns} 列欄位下的 {rows} 行數據',
    success_import_excel_button: '去查看',
    support_upload_file: '支持上傳 .xlsx/.csv 檔案',
    template_creator: '模板創建者',
    template_folder_editor: '模板文件夾編輯器',
    title_create_folder: '文件夾',
    title_dashboard_id: '儀表板',
    title_database_id: '數據表',
    title_delete_resource: '刪除資源',
    title_edit_resource: '編輯資源',
    title_export: '導出數據',
    title_field_id: '欄位',
    title_form_id: '表單',
    title_form_name: '表單名稱',
    title_import: '導入數據',
    title_member_field_id: '成員欄位',
    title_mirror_type: '鏡像類型',
    title_move_resource_to_public: '移動至團隊資源',
    title_move_to: '移動至',
    title_new_ai: 'AI 智能體(Beta)',
    title_new_automation: '自動化',
    title_new_computer: 'New Computer(alpha)',
    title_new_dashboard: '儀表板',
    title_new_database: '資料庫',
    title_new_document: '文檔',
    title_new_file: '文件附件',
    title_new_form: '表單',
    title_new_mirror: '鏡像',
    title_new_other_resource: '從模板創建',
    title_new_page: 'AI 頁面(Beta)',
    title_new_resource: '新建資源',
    title_new_view: '新建視圖',
    title_record_id: '記錄',
    title_resource: '資源',
    title_resource_description: '資源描述',
    title_resource_id: '資源',
    title_resource_name: '資源名稱',
    title_resource_type: '資源類型',
    title_shortcut: '添加到捷徑',
    title_shortcut_cancel: '取消捷徑',
    title_shortcut_personal: '添加到個人捷徑',
    title_shortcut_personal_remove: '從個人捷徑中移除',
    title_shortcut_space: '添加到空間捷徑',
    title_shortcut_space_remove: '從空間站捷徑中移除',
    title_show_hidden_field: '顯示隱藏',
    title_space_admin: '管理員功能',
    title_space_shortcut: '添加到捷徑（所有人可見）',
    title_update_folder: '更新文件夹',
    title_view_id: '視圖',
    title_view_type: '視圖類型',
    toggle_view: '切換視圖',
    type: {
      ai: 'AI 智能體',
      ai_agent: {
        data_source: '數據源',
        description: '輸入你的問題，AI 智能體會根據你的需求生成相應的內容和數據',
        prompt_placeholder: '請輸入提示詞',
        settings_datasource_sitemap_placeholder: '請輸入數據源 Sitemap',
        settings_datasource_url_placeholder: '請輸入數據源 URL',
        settings_tool_toolsdks_package_key_placeholder: '請輸入 Package Key',
        settings_tool_toolsdks_package_version_placeholder: '請輸入 Package Version',
        skillset: '技能集',
        system_prompt_description: '關於 AI 智能體回應的指示和限制，告訴它如何更好地回答問題。',
        system_prompt_placeholder:
          '向 AI 智能體的指示和限制。\n您可以參考以下寫作風格:\n\n# 角色\n您是一位專業的附近旅遊顧問，可以為用戶提供多種短途旅行和附近旅行計劃，並生動描述各種景點的特點。\n\n# 限制\n\n僅討論與短途旅行和附近旅行相關的內容，拒絕回答無關的話題。\n所有輸出必須按照給定格式組織，不得偏離框架要求。\n功能介紹部分不得超過100字。\n',
        title: 'AI 智能體',
      },
      ai_description: '用於人工智能功能的資源',
      ai_wizard_description: 'AI 向導是 Bika.ai 中的聊天界面。它可以在平台內用於各種類型和目的的 AI 對話。',
      ai_wizard_features_list: 'AI Wizard List',
      ai_wizard_intent_ui_description:
        '在 Bika.ai 的 AI 向導中，各種類型和目的的 AI 對話被分類，這種分類被稱為「意圖」。\n        不同的意圖會導致不同的操作結果，因為每個意圖都被設計用來觸發系統內的特定響應或功能。',
      ai_wizard_intent_ui_features_list: 'AI Wizard Intent UI List',
      app_page: 'App Page',
      app_page_description: 'App Page',
      automation: '自動化',
      automation_action_description:
        '自動化動作是指執行任務、活動、事件或變更的步驟，比如發送電子郵件。\n      你可以這樣理解動作：當某事發生（觸發器）且滿足指定條件時，就會執行這個事件（動作）。',
      automation_action_features_list: '執行器清單',
      automation_description: '用於設置和管理自動化流程的資源',
      automation_trigger_description:
        '自動化觸發器作為一個「開關」，當滿足特定條件時就會啟動自動化流程。\n      你可以這樣理解觸發器：當特定事件發生（觸發器）且某些條件成立時，就會執行相應的事件（動作）。',
      automation_trigger_features_list: '觸發器清單',
      canvas: '畫布',
      canvas_description: '用於繪圖和設計的畫布資源',
      chat: '聊天',
      chat_description: '通過 AI 向導，你可以與系統進行自然語言對話，獲取所需信息和支持，提升工作效率。',
      chat_menu: {
        fold: '不顯示',
        pin: '置頂',
        unpin: '取消置頂',
      },
      code_page: 'AI 頁面',
      code_page_description: '利用React、Vue代碼區定界面',
      create_node_resource: '創建節點資源',
      create_node_resource_description:
        '通過 AI 向導，你可以輕鬆快速地生成和管理各種節點資源，方便你進行項目的規劃和管理。通過這一功能，你可以更有效地組織和分配資源，確保項目的順利進行。',
      create_record: '創建記錄',
      create_record_description: '通過 AI 向導，你可以輕鬆快速地創建新的數據記錄，確保信息的準確性和及時性。',
      create_reminder: '創建提醒',
      create_reminder_description:
        '通過 AI 向導，你可以輕鬆快速地設置各種任務和事件的提醒，確保你不會錯過重要的事情。你可以自定義提醒的時間和內容，以滿足你的具體需求。',
      dashboard: '儀表板',
      dashboard_description: '用於彙總和展示關鍵數據的儀表板',
      database: '數據表',
      database_description:
        '數據表，類似於 Excel 電子表格，但功能更強大。每個數據表包含行和列，行代表記錄，列代表欄位。你可以在一個文件夾中創建多個數據表，以組織和分類不同類型的數據。數據表支持多種欄位類型，如文本、數字、附件、鏈接等，便於存儲多樣化信息。你可以通過視圖篩選、排序功能，提升數據管理和分析的效率。',
      database_field_description:
        '數據表欄位包含每條記錄的詳細信息或元數據。\n        數據表欄位保存每個條目的信息或元數據。這些欄位可以採用各種形式，允許以文本、單選或多選、圖片、複選框、數字、用戶標籤等形式存儲數據。',
      database_field_features_list: '數據庫字段列表',
      database_view_description:
        '數據表視圖提供了一種特定的方式來可視化和排列數據表中的基礎數據。\n        標準視圖是網格形式，但其他格式包括表單、日曆、圖庫和看板佈局。\n        一個數據表可以支持多個視圖和各種類型的視圖。',
      database_view_features_list: '數據表視圖列表',
      doc: '文檔',
      doc_description: '用於創建和存儲文檔的資源',
      file: '文件',
      file_description: '用於存儲和管理文件的資源',
      folder: '文件夾',
      folder_description: '用於存儲和管理文件的文件夾',
      form: '表單',
      form_description:
        '表單功能讓你可以創建自定義的表單，以便收集和輸入數據到指定的數據表。你可以通過指定數據表的一個視圖，快速生成一個表單，然後分享至各類社交群組。提交的數據會自動更新到相應的表格中，方便管理和分析。表單功能支持文本、附件、複選框等多種欄位類型，滿足不同數據收集需求。',
      integration_description:
        '集成是 Bika.ai 與外部服務或應用程序之間的連接，可實現平台之間的無縫數據傳輸。\n        你選擇的集成最終取決於你想要用數據解決的具體問題。\n        例如，如果你有一個追蹤任務的數據庫記錄，並且想要使用 AI 進行總結，你可以利用 OpenAI 集成將數據發送到 OpenAI，然後使用返回的信息發送郵件。',
      mirror: '鏡像',
      mirror_description: '用於同步和反映數據的鏡像資源',
      report_template: '報告模板',
      report_template_description: '用於創建和管理報告的模板',
      view: '視圖',
      view_description: '用於展示和瀏覽數據的視圖資源',
      web_page: '網頁資源',
      web_page_description: '用於創建和發布網頁的資源',
    },
    unbind_template_modal_content: '脫離模板之後，您可以任意修改此文件，但無法再獲取該模板的後續更新',
    unbind_template_modal_title: '您需要先脫離模板後才能進行該操作',
    update_automation_action_success: '成功更新自動化執行器',
    update_automation_success: '成功更新自動化 "{name}"',
    update_dashboard_success: '成功更新儀表板 "{name}"',
    update_database_success: '成功更新資料表 "{name}"',
    update_folder_success: '成功更新文件夾 "{name}"',
    update_view_success: '更新視圖 "{name}" 成功',
    view_count: '{count} 個視圖',
    view_hidden_all_field: '隱藏所有列',
    view_hidden_field: '隱藏列',
    view_name: '視圖名稱',
    view_show_all_field: '顯示所有列',
    view_show_field: '顯示列',
    view_type: {
      form: '表單',
      gantt: '甘特圖',
      grid: '表格',
      kanban: '看板',
    },
    views: '視圖',
    week: '週',
    widget_setting: '设定組件',
    widgets: {
      description:
        '組件是一種用於展示和操作數據的工具，你可以在數據表、視圖、儀表板中添加各種組件，以滿足不同的數據展示和操作需求。',
      features_list: '儀錶盤組件列表',
      name: '組件',
    },
  },
  role: {
    allow: '允許',
    msg_create_role_success: '角色創建成功',
    msg_delete_role_error: '刪除{name}成功，{message}',
    msg_delete_role_success: '刪除{name}成功',
    msg_load_role_error: '加載角色信息失敗',
    msg_update_role_success: '角色更新成功',
    role: '角色',
    roles: '角色',
    select_role: '選擇角色',
  },
  scheduler: {
    daily_base: '按天',
    friday: '星期五',
    hourly_base: '按小時',
    last_day: '最後 1 天',
    minute_base: '按分鐘',
    monday: '星期一',
    monthly_base: '按月',
    repeat_frequency: '重複頻率',
    repeat_interval: '重複間隔',
    repeat_per_monthday: '每月重複',
    repeat_per_weekday: '每週重複',
    saturday: '星期六',
    started_at: '開始時間',
    sunday: '星期日',
    thursday: '星期四',
    timezone: '時區',
    tuesday: '星期二',
    wednesday: '星期三',
    weekly_base: '按周',
    yearly_base: '按年',
  },
  settings: {
    about: {
      about_brand: '關於 Bika.ai',
      help_center: '幫助中心',
    },
    account: {
      account: '帳戶',
      account_information: '帳戶資訊',
      api: '開發者',
      api_description: '請妥善保管你的開發者API Token！',
      connected_account: '绑定第三方帐号',
      connected_account_description: '綁定下方平台的帳號，實現快捷登入 Bika',
      login_record_description: '記錄了您在系統中的登入活動信息，以幫助保護您的帳戶安全並提供更好的用戶體驗。',
      notification: '通知',
      referral: '推薦',
      session_logs: '會話日誌',
      top_up: '充值',
      unbind: '解除綁定',
      unbind_description: '解除綁定後，您將無法使用該帳號登入 Bika',
    },
    audit: {
      action: '操作',
      action_name: {
        invitation_email_accept: '接受邀請',
        invitation_email_delete: '邀請郵件',
        invitation_email_resend: '邀請郵件',
        invitation_email_send: '邀請郵件',
        invitation_link_accept: '邀請鏈接',
        invitation_link_create: '邀請鏈接',
        invitation_link_delete: '邀請鏈接',
        node_create: '創建資源',
        node_delete: '刪除資源',
        node_detach: '模板脫離',
        node_export: '導出資源',
        node_get: '訪問資源',
        node_import: '導入資源',
        node_publish: '發布資源',
        node_update: '更新資源',
        node_upgrade: '模板升級',
        share_grant: '授權資源',
        share_password_create: '分享加密',
        share_password_delete: '分享加密',
        share_password_update: '分享加密',
        share_restore: '恢復資源',
        share_revoke: '撤銷資源',
        share_scope_update: '分享範圍',
        space_update: '更新空間',
        template_install: '安裝模板',
      },
      actor: '操作人',
      description: '描述',
      empty: '暫無審計記錄',
      page_info: '顯示第 1 到 {size} 條，共 {total} 條事件',
      search_empty: '未找到相關審計記錄',
      template: {
        invitation_email_accept: '接受邀請 {email}',
        invitation_email_delete: '刪除邀請 {email}',
        invitation_email_resend: '重發 {email}',
        invitation_email_send: '邀請 {emails}',
        invitation_link_accept: "接受<a href='/space/join/{token}'>邀請鏈接</a>",
        invitation_link_create: "創建<a href='/space/join/{token}'>邀請鏈接</a>",
        invitation_link_delete: "刪除<a href='/space/join/{token}'>邀請鏈接</a>",
        node_create: "在 {parent} 下創建資源 <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_delete: '刪除了資源 {name}',
        node_detach: "分離了資源 <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_export: "導出了資源 <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_get: "訪問資源 <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_import: "導入了資源 <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_publish: "發布了資源 <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_update: "更新了資源 <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_upgrade: "升級模板文件夾 <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        share_grant: '授予 {units} 對資源 {name} 的訪問權限',
        share_password_create: '為訪問共享資源 {name} 創建了密碼',
        share_password_delete: '刪除了訪問共享資源 {name} 的密碼',
        share_password_update: '更新訪問共享資源 {name} 的密碼',
        share_restore: '恢復對資源 {name} 的訪問權限',
        share_revoke: '撤銷 {unit} 對資源 {name} 的訪問權限',
        share_scope_update: '更新資源 {name} 的共享範圍',
        space_update: '更新了空間站配置',
        template_install: "安裝了模板 (<a href='/space/{spaceid}/node/{nodeid}'>{name}</a>)",
      },
      time: '時間',
    },
    billing: {
      already_support: '已解鎖',
      apply_refund: '申請退款',
      cancel_subscribe: '取消訂閱',
      cancel_subscription_content: '您確定要取消訂閱嗎？',
      cancel_subscription_tips: '取消成功，您仍可在當前賬單週期結束前享有所有功能的完整訪問權限',
      cancel_subscription_title: '取消訂閱',
      canceled_at: '取消時間',
      change_payment_method: '更改付款方式',
      change_plan: '更改計劃',
      current_plan: '當前計劃',
      manage: '管理賬單',
      next_invoice_date: '下一次賬單日期',
      no_billing_day: '無',
      not_support: '不支援',
      payment_status: {
        buying_plan: '您正在購買{plan}計劃',
        guest_limit: '🌟 1000 guests',
        payment_failed: 'Payment failed',
        payment_failed_description: 'Sorry, subscription to {plan} plan failed, please try again',
        privileges_gained: 'You have gained the following privileges:',
        storage_limit: '🌟 200GB storage space',
        try_again: 'Try again',
        try_now: 'Try now',
        unlimited_members: '🌟 Unlimited members',
        upgrade_success: 'Congratulations on upgrading to {plan} successfully!',
        waiting_payment: '等待付款',
      },
      resume_subscription: 'Resume Subscription',
      start_now: '立即開始',
      usage: '空間站用量',
      usage_detail: '用量詳情',
      usage_limit_template: '抱歉，席位已達空間上限，請升級訂閱方案以獲取更多席位。',
      usage_tips: '如果需要購買幫助可以聯繫我們',
      usages: {
        automation_runs: '每月自動化運行次數',
        emails: '每月郵件發送數',
        guests: '訪客數',
        missions: '每月智能任務數',
        records: '空間站記錄总數',
        reports: '每月報告數',
        resources: '節點資源數',
        seats: '席位數',
        space_integrations: '空間站整合實例數',
        storages: '存儲空間',
        unused: '剩餘',
        used: '已用',
      },
      wating_payment: '等待付款',
      will_cancel_subscribe: 'Will cancel subscription, you can cancel the subscription',
      you_will_lose_all_privileges: '取消訂閱後您將失去所有特權',
    },
    coin_rewards: {
      all_plan: '所有計劃',
      all_plan_description: '如果需要購買幫助可以聯繫我們',
      and: '和',
      choose_plan: '選擇計劃',
      coin_description: '可用於',
      copy_invitation_code: '複製邀請碼',
      copy_invitation_link: '複製邀請鏈接',
      current_plan: '當前計劃',
      get_coin_history: '獎勵記錄',
      get_rewards_methos_1: '方式一：通过邀请链接邀请新用户时，双方都可获得',
      get_rewards_methos_2: '方式二：将您的邀请码提供给未填写邀请码的用户，双方都可获得',
      get_rewards_methos_3: '方式三：邀請成員進入空間站，雙方都獲得',
      get_rewards_methos_4: '方式四：安裝 App，您將獲得',
      invite_member_rewards_coin: '邀請成員送積分',
      no_coin_history: '暫無獎勵記錄',
      plan_unit: '每個成員/月',
      purchase_advanced_mode: '購買高級模式',
      recommend_get_bika_coin: '推薦獎積分',
      to_invite_member: '去邀請成員',
      upgrade_space: '升級空間站',
      view_usage: '查看用量',
      your_coin: '你的積分',
    },
    general: '一般',
    guest: {
      create_guest_team: '創建訪客組',
      delete_guest_team_description: '訪客組刪除後將無法恢復，確定要刪除此訪客組嗎?',
      delete_guest_team_success: '刪除訪客組成功',
      delete_guest_team_title: '刪除訪客組',
      edit_guest_team: '編輯訪客組',
      no_guest: '暫無訪客',
      select_guest: '選擇訪客',
      select_guest_team: '選擇訪客組',
    },
    member: {
      assign_members: '分配成員',
      choose_member_role: '選擇成員角色（可選）',
      choose_member_role_placeholder: '請選擇角色',
      choose_member_team: '設置小組',
      choose_member_team_placeholder: '請選擇小組',
      delete_member: '刪除成員',
      delete_member_confirm: '確定將成員移出空間站？',
      description: '描述',
      edit_error: '編輯失敗',
      edit_member: '編輯成員',
      edit_success: '編輯成功',
      empty_email: '暫無郵箱',
      member_name_setting_description: '成員名是在空間站內部使用的暱稱，它在不同的空間站之間不會改變',
      member_or_group_not_found: '未查找到成員或小組',
      remove_member: '移除成員',
      remove_member_confirm: '確定要將所選成員移出該角色嗎？',
      remove_member_confirm_content: '所選成員可能將會失去部分權限，確定要將所選成員移出該角色嗎？',
      remove_members: '移除 {count} 個成員',
      set_space_member_name: '設置空間站內的暱稱',
    },
    message_member_name_modified_failed: '站內暱稱修改失敗',
    message_member_name_modified_successfully: '站內暱稱修改成功',
    nickname_modified_failed: '暱稱修改失敗',
    notification: {
      advertise: '建议下载 Bika.ai 的手机客户端，获得更及时的提醒和专属体验',
      description:
        'Bika 支持将任务、日程、报告的最新情况，通过多种通知方式给你发送提醒消息，让你可以更实时地掌握各种动态',
      email: {
        description: '綁定後您可以透過郵件收到通知',
        title: '接收電子郵件通知',
      },
      push: {
        description: '綁定後可以透過瀏覽器接收通知',
        title: '接收瀏覽器通知',
      },
      sms: {
        description: '綁定後可以收到簡訊通知',
        title: '接收簡訊通知',
      },
      title: '通知集成',
      wechat_push: {
        setting: '设置',
        title: '微信公众号通知',
      },
    },
    other: '其他',
    role: {
      confirm_delete_role: '確定要刪除該角色嗎？',
      create_role: '創建角色',
      create_role_empty_error: '角色名稱不能爲空',
      create_role_placeholder: '請輸入角色名稱',
      delete_role: '刪除角色',
      delete_role_confirm_content: '刪除角色不可恢復，確定要刪除嗎？',
      delete_roles: '刪除 {count} 個角色',
      deselect: '取消選擇',
      edit_role: '編輯角色',
      edit_role_error: '無法編輯, 請重新刷新頁面',
      edit_role_placeholder: '請輸入角色名稱',
      management_role: '管理角色',
      member_no_role_tips: '該角色暫無成員',
      need_select_a_role: '請選擇角色',
      need_select_role: '請選擇角色',
      non_management_role: '非管理角色',
      role_name: '角色名稱',
      role_permission: '角色權限',
      role_type: '角色類型',
      select: '選擇',
    },
    space: {
      announcement_description:
        '公告內容將出現在空間站首頁，所有成員進入空間站即可看到，同時，支持 Markdown 格式進行編輯，以提升公告的可讀性和美觀性。',
      announcement_placeholder: '請輸入公告內容，讓成員了解最新動態',
      authorized_domain: '授權郵箱域名',
      authorized_domain_description:
        '設置授權郵箱域名以限制訪問空間站。只有來自這些域名的郵箱帳號可以加入，其他將被拒絕。',
      authorized_domain_settings: '授權郵箱域名設置',
      config_watermark: '浮水印',
      create_mission: '創建智能任務',
      create_mission_description:
        '您可以通過設置自動化觸發不同類型的新任務，支持設置任務的執行人、節點資源、截止日期等詳細信息。任務創建後，將根據設定的時間與資源自動觸發，並通過AI進行任務管理和跟蹤，確保高效完成任務。',
      current_permission: '當前空間站默認的權限是',
      current_permission_tips: '這意味著當任何人創建一個新資源並且沒有分配權限時，空間站中的所有成員將默認擁有',
      delete_space: '刪除空間站',
      delete_space_confirm_desc:
        '此操作不可撤销！为避免误操作，请输入空间站 ID {spaceId} 进行确认。删除后，所有数据将永久丢失。',
      delete_space_confirm_title: '刪除空間站',
      delete_space_desc: '刪除空間站後，空間站內的所有資料（包括節點資源、附件等）將被永久清除且無法恢復',
      delete_space_success: '刪除空間站成功',
      description:
        '空間站是 Bika.ai 的核心功能，是您的工作空間，您可以在空間站中創建、管理、協作各種資源，實現團隊協作。',
      disable_global_manage_resource: '控制成員在根節點下的資源創建、訪問和操作權限',
      disable_global_manage_resource_description:
        '控制成員加入空間站後，對節點資源的初始訪問權限。若不啟用，則全體成員預設擁有「可管理」的權限。',
      disable_global_manage_resource_learn_more: '跳轉到幫助文檔了解更多',
      invite_members: '邀請成員',
      ip_address_placeholder: '請輸入 IP 地址',
      join: '加入',
      member_management: '成員管理',
      member_management_description:
        '成員管理旨在幫助您高效地管理團隊成員。通過該模塊，您可以添加、刪除和編輯成員信息，並為每個成員分配適當的角色和權限。成員管理模塊還提供了搜索和篩選功能，方便您快速找到特定成員。此外，您還可以創建和管理小組，將成員添加到小組中，以便進行更細緻的組織架構管理。',
      mission: '任務',
      mission_description:
        '由AI分配的特定任務或目標，通常是自動化工作流程的一部分。在 Bika.ai 中，根據您的設置AI會自動生成任務提示您去完成。',
      permission_settings_description: '空間站資源管理',
      placeholder_authorized_domain: '請輸入授權郵箱域名，如：example.com',
      please_input_space_id: '請輸入空間站 ID',
      request_new_record: '請求記錄',
      request_new_record_description:
        '系統中提交的請求日誌，包括時間戳和狀態等詳細信息。Bika.ai 使用它來跟蹤自動化任務和用戶輸入以進行報告。',
      rich_document: '富文本',
      rich_document_description:
        '一種支持文本、圖像和其他媒體元素的文本格式，提供增強的內容展示。Bika.ai 允許集成各種文檔類型，用於報告和自動化。',
      role_management: '角色管理',
      role_management_description:
        '角色管理旨在幫助您高效地管理和分配權限。在安裝模板時，部分模板會預設一些角色，您可以將成員添加到這些角色中，以便更好地完成模板配置。此外，您還可以自由創建自定義角色，並將任意部門或成員分配到一個或多個角色中，從而實現更靈活的權限控制。通過角色管理，您可以確保每個成員擁有適當的權限，提升團隊協作效率。',
      setting_info: '設置信息',
      setting_info_description:
        '設置信息是您在點開個人設置後彈出的設置界面，可以在此更新個人資料、選擇主題、設置時區及配置系統語言。該界面幫助使用者自訂偏好，以獲得更加個性化的體驗。',
      space: '空間',
      space_audits: '審計日誌',
      space_has_be_deleted: '空間站已刪除',
      space_has_be_deleted_desc: '抱歉，你沒有權限查看，請通知管理者',
      space_settings: '空間設定',
      space_sidebar: '空間側邊欄',
      space_sidebar_description:
        '空間側邊欄提供快速導航，幫助您輕鬆訪問空間中的各項功能和模組。通過側邊欄，您可以查看首頁、任務、報告、資源、設置等內容，簡化操作流程，提升工作效率。側邊欄的設計旨在為您提供簡潔的操作體驗，使團隊協作更加流暢。',
      third_party_integration: '第三方整合',
      upgrade: '升級',
      usage: '用量 & 帳單',
      wallpaper_button: '設置壁紙',
      wallpaper_description: '壁紙將出現在空間站首頁，所有成員進入空間站即可看到。',
      wallpaper_preset_photos: '預設壁紙',
      wallpaper_title: '主頁壁紙',
      watermark_description: '全局浮水印',
      watermark_description_2:
        '為了保證企業信息安全，資源支持顯示全局水印。水印內容為當前訪問成員的姓名+手機號後綴或者郵箱前綴，防止截屏洩密',
      workflow: '工作流',
      workflow_description:
        '一系列任務或流程，旨在實現特定的結果。在Bika.ai中，工作流程由AI自動化管理，簡化了跨不同功能的重複任務。',
    },
    upgrade: {
      action_record: '詳情',
      ai_invoke_consume: 'AI 調用消耗',
      ai_invoke_count_达标奖励: 'AI 調用次數達標獎勵',
      benefit_details: '查看權益詳情',
      bkc: '使用 BKC 抵扣',
      bkc_deduction: 'BKC 抵扣',
      cancel_subscription: '取消訂閱',
      consumption_log: '使用狀況',
      credit: '空間站積分',
      credit_desc: '空間站積分是空間站用戶通過訂閱、邀請成員、消費積分等方式獲得的積分，可以用于兌換空間站服務。',
      currency: '貨幣種類',
      currently_owns: '當前擁有',
      cycle: '訂閱周期',
      cycle_descritpion: '計劃每月自動續訂，直至取消。',
      date: '日期',
      detail: '詳情',
      get_more_bika: '獲得更多 Bika 幣',
      gift_credit: '每日',
      gift_credit_desc: '由官方每日贈送的免費積分，登入即可獲得，每日自動刷新。',
      invite_logup: '邀請註冊',
      invite_member: '邀請成員',
      invite_people: '邀請好友',
      invite_space: '邀請加入空間站',
      loading_tips: '正在計算價格，請稍等～',
      member: '成员',
      or: '或',
      other_method_bkc: '還可以通過',
      pay_annual: '年付',
      pay_monthly: '月付',
      pay_now: '支付',
      pay_tips: '繼續，即表示同意 Bika.ai 條款和條件。',
      payment: '選擇付費方式',
      permanent_credit: '贈送',
      permanent_credit_desc: '通過官方活動以及邀請成員等方式獲得的積分',
      plan: '訂閱計劃',
      quanty: '席位數',
      recharge_consumption_integral: '積分異動',
      resume_subscription: '恢復訂閱',
      serial_number: '序號',
      space: '空間站名稱',
      space_member_num: '空間站成員數量',
      subscribe_credit: '訂閱',
      subscribe_credit_desc: '由空間站的訂閱計劃決定，訂閱後可獲得相應的空間站積分，並按照訂閱日期每月自動刷新。',
      subtotal: '小計',
      total: '合計',
      unit_price: '單價',
      user_referral: '用戶推薦',
      user_topup: '用戶充值到空間站',
    },
  },
  share: {
    already_set_permission: '已限制權限，不再繼承父級文件夾的權限',
    change_permission_success: '權限修改成功',
    change_pwd: '修改密碼',
    change_pwd_success: '已修改密碼',
    change_share_pwd: '修改分享密碼',
    close_short_link_warning: '關閉短鏈接後，下方的短鏈接將失效',
    copy_pwd_and_link: '複製鏈接和密碼',
    copy_pwd_and_link_success: '已復製鏈接和密碼',
    create_short_link_warning: '創建一條短鏈接，下方的分享鏈接將失效',
    has_num_member_share: '有 {memberCount} 位成員共享中',
    member_of_share_permission: '共享權限的成員',
    network_user_need_pwd: '互聯網上的用戶需要密碼訪問，組織內用戶無需密碼可直接訪問',
    open_pwd: '啟用密碼',
    permission: {
      input_pwd: '輸入密碼',
      no_login_visit: '你可能沒有權限或者沒有登錄導致的',
      no_permission_visit: '你沒有權限查看該資源',
      no_pwd_visit: '因為該資源設置了密碼，你需要輸入密碼',
      notify_admin: '通知管理員',
      notify_admin_for_permision: '抱歉，你沒有權限查看，請通知管理員',
      notify_admin_success: '已經通知！請耐心等候！',
      right_pwd: '密碼正確',
      share_permission_can_edit: '互聯網獲得鏈接的人可編輯',
      share_permission_can_view: '互聯網獲得鏈接的人可查看',
      share_permission_default: '僅限空間站內指定成員或訪客訪問',
      share_permission_form_anonymous_write: '鏈接訪問者無需登錄即可匿名提交表單',
      share_permission_form_login_write: '鏈接訪問者需登錄後才能提交表單',
      wrong_pwd: '密碼錯誤',
    },
    pwd_pattern: '輸入新密碼',
    recover: '恢復',
    recover_permission_success: '恢復權限成功',
    set_share_pwd: '設置分享密碼',
    share: '分享與權限',
    share_text: '分享',
  },
  shortcuts: {
    no_pin_so_far: '暫無捷徑，快把常用文件設為捷徑',
    pin_to_top: '置頂',
    pinned: '已置頂',
    shortcuts: '捷徑',
    unpin_from_top: '取消置頂',
  },
  skillset: {
    page_description:
      'Bika.ai skillsets includes MCP servers, 3rd-party apps, and integrations for your AI agents and automation workflow.',
    page_title: 'Skillset, Apps, Integrations',
  },
  slogan: {
    alternatives: [
      {
        name: 'Airtable',
        url: 'https://airtable.com/',
        description:
          '與Airtable相比，Bika.ai更專注於AI自動化和主動協助。Bika.ai更適合於需要在工作和生活中獲得更多自動化和AI協助的用戶。',
      },
      {
        name: 'Zapier',
        url: 'https://zapier.com/',
        description:
          '與Zapier相比，Bika.ai更專注於開箱即用的模板和數據庫工作流。Bika.ai更適合於需要在工作和生活中獲得更多自動化和AI協助的用戶。',
      },
      {
        name: 'vika維格表(維格雲)',
        url: 'https://vika.cn/',
        description:
          '與vika維格表相比，Bika.ai 更專注於 AI 自動化和主動式協助。Bika.ai 更適合需要更多自動化和 AI 協助的用戶。',
      },
    ],
    highlights: [
      {
        icon: '/assets/icons/highlights/auto-template.png',
        name: 'AI員工們的Teams / Slack',
        description: '像聊微信一樣，協作、構建和管理一個智能化AI組織。',
        keywords: 'AI員工, AI智能體, 智能組織, 無代碼, 協作平台',
      },
      {
        icon: '/assets/icons/highlights/ai-automation.png',
        name: '全球最大的應用整合平台',
        description:
          '可連接或自訂過萬個MCP工具。預設的技能工具包括搜索（網頁、圖片）、研究和辦公工具（幻燈片、文檔、電子表格）等。',
        keywords: '應用整合, 工具連接, 自動化工作流, MCP工具',
      },
      {
        icon: '/assets/icons/highlights/data-visual.png',
        name: '超強無代碼工作站',
        description:
          '豐富的無代碼元件，億級大數據的多維表格、自動化工作流程、即時協作文檔、儀表板等盡在一處。兼容OpenAPI且可擴展。',
        keywords: '無代碼, 工作站, 多維表格, 自動化工作流, 即時協作',
      },
      {
        icon: '/assets/icons/highlights/auto-publish.png',
        name: 'AI智能體商店',
        description: '製作並發布您自己的智能化 AI 模板和 AI 員工，並與社區分享。',
        keywords: 'AI智能體, 模板商店, 社區分享, 代理AI',
      },
    ],
    keywords: 'AI 組織者, AI 協同平台, 無代碼 AI 自動化, 智能 AI 團隊構建, AI 行銷自動化, AI 線索管理',
    personas: [
      {
        name: '市場營銷人員',
        description: '市場營銷人員',
      },
      {
        name: 'KOL內容創作者',
        description: 'KOL內容創作者',
      },
      {
        name: '自動化顧問',
        description: '自動化顧問',
      },
      {
        name: '項目管理經理',
        description: '項目管理經理',
      },
      {
        name: '銷售負責人',
        description: '銷售負責人',
      },
    ],
    screenshots: ['/assets/blog/what-is-bika-ai/template.en.gif'],
    slogan_mkt_e:
      'Bika.ai 是一個商業AI智能體平臺，提供 AI Agent 驅動的 CRM、營銷自動化系統、項目管理系統、商業智能（BI）和企業資源規劃（ERP），所有這些都以驚人的價格提供。',
    slogan_mkt_s: '打造您的 AI 智能體員工團隊',
    slogan_prd_e:
      'Bika.ai 將 Airtable（數據庫）和 Zapier（自動化）結合成一個簡單易用的智能平台，提供 AI Agent 增強的 CRM、營銷自動化系統、項目管理系統、商業智能（BI）和企業資源規劃（ERP），所有這些都以驚人的價格提供。',
    slogan_prd_l: 'Bika.ai 是一個商業AI智能體平臺，通過主動式AI自動化，幫助完成各種工作',
    slogan_prd_m: '商業AI智能體平臺，讓AI積極主動地完成各種工作',
    slogan_prd_xl:
      'Bika.ai是一個商業AI智能體平臺，融合無代碼、多維表格、數據中台、企業級AI知識庫，讓AI積極主動地完成各種工作。無需與AI不斷對話，Bika.ai可以自動化重複任務，並且能夠在市場營銷和銷售等多個職能中無縫執行，讓您專注於戰略性工作',
    slogan_title: 'Bika.ai: AI 智能組織平台，打造您的 AI 智能體員工團隊',
    use_cases: [
      {
        name: '行銷自動化',
        description:
          '幫您批量、定時、間隔地自動發送電子郵件、YouTube視頻、Twitter推文、通知簡訊等行銷內容,實現快速高效的行銷自動化',
      },
      {
        name: '銷售線索管理',
        description: '自動收集、跟蹤和管理上百萬條銷售線索,幫助您系統化地跟進潛在客戶,提高銷售轉化率',
      },
      {
        name: 'AI向你匯報',
        description: '定期主動找你建議AI策略和自動化流程,在您決策後才執行,AI還會定期向你生成匯報',
      },
      {
        name: '一站式解決',
        description: '無需複雜的專業軟體,Bika.ai輕量級的AI自動化資料庫,可滿足您的客戶資料儲存、管理和跟蹤需求',
      },
      {
        name: '自訂編輯',
        description:
          'Bika.ai提供強大的低代碼/無代碼編輯器,讓您輕鬆定制各種自動化任務流程和資料系統,實現專案管理、產品工單、訂單管理等更多應用場景',
      },
    ],
    usp: 'Bika.ai 提供了一個開箱即用的自動化數據庫，內置豐富的功能和第三方整合。不管數據量有多大，哪怕是數十億條數據，Bika.ai 都能輕鬆應對。使用 Bika.ai，您無需不斷與 AI 對話，數據量也不再是問題。\n\n通過 Bika.ai 自動完成任務，工作更高效精確，節省大量時間。用戶還能輕鬆發布、分享和複製自動化模板，便於持續改進。如果你想讓市場營銷、銷售或項目管理更簡單，同時用 AI 自動化來提升數據處理能力，Bika.ai 就是你的理想選擇。',
    video_ai_agent: 'https://www.youtube.com/embed/POLa4KmVtVo?si=EdkgoshBfbHbCmML',
    video_automation: 'https://www.youtube.com/embed/g0WOF2hkSH0?si=k_hZ-m0BDdcyuSVg',
    video_dashboard: 'https://www.youtube.com/embed/VsrUHkjbbbU?si=1K6XO_liycfxNyyc',
    video_database: 'https://www.youtube.com/embed/BdP9qskz89s?si=KbWMCzSFsu9OQzVG',
    video_documents: 'https://www.youtube.com/embed/XuWV2nSvvoA?si=vYmwS-JUxduCAJMd',
    video_forms: 'https://www.youtube.com/embed/Wi6scmIzDKE?si=ggjeyXaTI2KGjPbw',
    video_marketing: 'https://www.youtube.com/embed/3jolpKcb1Zo?si=W6Mf1B8FpZRXsvle',
    video_onboarding: 'https://www.youtube.com/embed/CxJFssdj6hs?si=uE-30GbTyRCMM4kP',
    video_partners: 'https://www.youtube.com/embed/CxJFssdj6hs?si=uE-30GbTyRCMM4kP',
    video_product: 'https://www.youtube.com/embed/QzmIjaTOjo8?si=tMo3YBd09Na6Yl8f',
  },
  sort: {
    sort_setting: '排序設置',
    sort_title: '排序',
  },
  space: {
    advanced: '進階',
    all_members: '我的小組成員(包含子小組)',
    announcement: '空間站首頁公告',
    default_space_name: '我的空間站',
    email_domain: '電子郵件域',
    enter_announcement: '輸入公告',
    features_list: 'Space Features List',
    goto_space: '進入我的空間站',
    group_members: '我的小組成員(不包含子小組)',
    home: {
      installed_templates: '已安裝的模板應用',
      invite_members: '邀請好友加入',
      set_space_announcement: '設置空間公告',
      space_announcement: '空間站公告',
      view_help_document: '查看幫助文檔',
      view_templates: '查看可用模板',
    },
    import: '導入數據',
    import_description: '可以直接將本地文件導入到 Bika 空間站內',
    integration: '整合',
    integrations: '整合',
    members: '成員',
    members_and_teams: '成員和小組',
    msg_go_to_space_settings: '編輯修改',
    msg_space_name_modified_success: '名稱修改成功，改成了: {spaceName}',
    new_space: '新的空間站',
    no_data: '無資料',
    no_name: '未命名',
    no_permission_content: '請確保連結正確且您有存取權限。如有任何疑問,請聯繫任務發佈者。',
    no_permission_title: '無權查看此任務',
    preview_import: '預覽當前導入的數據',
    removal_from_space: '從空間站移除',
    removal_from_space_description: '確定要從空間站移除此成員嗎?',
    role: '角色',
    show_watermark: '顯示浮水印',
    space: '工作空間',
    space_creator: '創建人',
    space_domain: '空間域名',
    space_logo: '空間 Logo',
    space_name: '空間名稱',
    space_settings: '空間設定',
    space_subdomain: '空間子域名',
    teams: '小組',
    unnamed: '未命名空間站',
    watermark: '浮水印',
    you_will_be_assigned_a_subdomain: '您將被分配一個子域名:',
  },
  tags: {
    completed: '已完成',
    due: '已過期',
    invalid: '失效',
    pending: '進行中',
    read: '已讀',
    rejected: '已拒絕',
    request_changed: '請求更改',
    review: '審核中',
    unread: '未讀',
  },
  task: {
    cutoff_time: '截止時間',
    task: '任務',
  },
  team: {
    create_team: '創建子小組',
    delete_team: '刪除小組',
    delete_team_description: '小組刪除後將無法恢復，確定要刪除此小組嗎？',
    delete_teams: '刪除 {count} 個小組',
    edit_team: '編輯小組名稱',
    join_team_description: '通過此連結加入的小組成員將自動分配到群組和角色中。',
    menu_remove_member_from_space: '從空間移除',
    menu_remove_member_from_team: '移出小組',
    msg_add_member_success: '成員已成功加入小組',
    msg_create_team_success: '小組建立成功',
    msg_delete_team_error: '刪除小組失敗',
    msg_delete_team_success: '刪除小組成功',
    msg_remove_member_from_space_success: '已將成員從空間移除',
    msg_remove_member_from_team_success: '已將成員從小組移出',
    msg_rename_team_success: '小組成功重新命名',
    msg_team_name_not_empty: '請輸入小組名稱',
    placeholder_new_team: '請輸入小組名稱',
    placeholder_select_members: '請選擇要加入當前小組的成員',
    remove_index_members: '移除 {{index}} 位成員',
    remove_member_from_space: '將成員從空間站移除',
    remove_member_from_space_description:
      '成員將被移除，但他們的歷史、評論、上傳、任務和其他所有內容將保留，不會被刪除。被移除的成員仍可能出現在搜索和篩選中。',
    remove_member_from_team: '將成員移出小組',
    remove_member_from_team_description: '確定要將此成員移出小組嗎?',
    select_team: '選擇小組',
    show_ai: '顯示AI',
    show_all: '顯示全部',
    show_member: '顯示成員',
    team: '小組',
    teams: '小組',
    unselect_all: '取消全選',
  },
  template: {
    ai_create: 'AI 應用顧問',
    architecture: '流程圖',
    architecture_description: '{name}的流程圖',
    change_log: '變更日誌',
    check_original_template: '查看原始模板',
    coming_soon: '敬請期待',
    coming_soon_tooltip: '模板制作中，敬请期待',
    comments: '評論',
    delete_template: '刪除模板',
    delete_template_description: '確定要刪除模板「{name}」嗎?',
    delete_template_success: '刪除模板 "{name}" 成功',
    empty_change_log: '暫無變更日誌',
    export: '導出',
    favorite: '收藏',
    feedback_email: '您的郵箱',
    feedback_email_placeholder: '請輸入您的郵箱',
    feedback_ideas: '您有什麼想法？',
    feedback_placeholder: '請輸入您的想法或建議',
    feedback_thanks: '感謝您的反饋',
    get: '安裝',
    install: '安裝',
    install_template: '安裝模板',
    install_toast: '模板安装成功',
    make_it_faster: '讓工作更快',
    no_readme: '作者還沒有寫說明書呢',
    no_template: '當前分類下還沒有模板',
    not_found_template: '找不到想要的模板？請告訴我們',
    official_certification: '官方認證',
    open: '打開',
    read_more: '→ 了解更多關於 Bika.ai',
    readme: '說明',
    release_notes: '變更日誌',
    release_notes_description: '{name}的變更日誌',
    releases_history: '發布歷史',
    select_one_space: '請選擇一個空間',
    select_space: '選擇空間',
    star_success: '收藏成功',
    template: '模板',
    title: '模板應用',
    try_other_templates: '嘗試其他模板',
    unstar_success: '取消收藏成功',
    upgrade: '升級',
    website_description:
      '找到<%= category %>領域功能強大的商業AI智能體和自動化工作流程。讓AI為您工作，節省時間並優化結果。',
    website_title: '<%= category %> 領域最佳的<%= count %>個商業AI智能體模板與數據庫工作流 (<%= year %>年) | Bika.ai',
  },
  theme: {
    colorful_theme: '多色主題',
    dark: '深色',
    light: '淺色',
    single_color_gradient_theme: '單色漸變主題',
    system: '跟隨系統',
    theme: '主題',
    theme_blue: '藍色',
    theme_brown: '棕色',
    theme_color_1: '配色一',
    theme_color_2: '配色二',
    theme_color_3: '配色三',
    theme_color_4: '配色四',
    theme_deepPurple: '深紫色',
    theme_green: '綠色',
    theme_indigo: '靛青',
    theme_orange: '橙色',
    theme_pink: '粉色',
    theme_purple: '紫色',
    theme_red: '紅色',
    theme_tangerine: '橘色',
    theme_teal: '藍綠色',
    theme_yellow: '黃色',
  },
  time: {
    hour: '小時',
    minute: '分鐘',
  },
  tips: {
    drop_files_here: '拖放檔案到這裡',
    empty: '暫無資料',
    invalid_file_type_error: '無效的檔案類型: {invalidFileNames}。可接受的類型: {uploadAccept}',
    setting_announcement: '設置公告',
  },
  todo: {
    complete_all: '標記全部完成',
    create: '創建待辦',
    create_todo: '創建智能任務',
    finished: '已完成',
    my: '我的',
    my_created: '我創建的',
    no_todo_so_far: '暫無待辦事項',
    overdue: '已過期',
    pending: '進行中',
    recent: '最近',
    rejected: '已拒絕',
    today: '今日',
    todo: '待辦事項',
    todos: '待辦事項',
    unfinished: '未完成',
  },
  toolbar: {
    hide_all: '全部隱藏',
    hide_fileds: '隱藏列',
    hide_kanban_grouping: '隱藏分組',
    previous: '上一個',
    show_all: '全部顯示',
  },
  top_up: {
    choose_top_up_amount: '選擇充值金額',
    no_balance: '你的餘額不足',
    read_and_accept_toc: '閱讀並接受服務條款',
    top_up: '充值',
    top_up_success: '充值成功',
    your_bika_coins: '你的積分',
  },
  trash: {
    delete: '徹底刪除',
    delete_description: '徹底刪除後將無法恢復',
    delete_title: '徹底刪除條目',
    recover: '恢復',
    recover_description: '此條目將恢復到原路徑',
    recover_success: '恢復成功',
    recover_title: '恢復條目',
    trash: '回收站',
  },
  tutorial: '教學',
  unit: {
    pcs: '個',
    row: '行',
    to: {
      admin: '管理員',
      admin_description: '空间站内的所有管理员',
      all_members: '所有成員',
      all_members_description: '空間站內的所有成員',
      current_operator: '當前操作者',
      current_operator_description: '當前操作員',
      email_field: '郵件欄位',
      email_field_description: '在資料表的郵件欄位中選擇郵件',
      email_string: '電子郵件',
      email_subject_and_content: '郵件主題和內容',
      member_field: '表格中的成員列',
      member_field_description: '在資料表的成員欄位中選擇成員',
      recipient: '收件人',
      recipient_and_more: '收件人以及更多設置',
      recipient_description: '選擇指定的成員、角色或小組',
      role_select_label: '選擇角色',
      show_more_options: '顯示更多選項',
      specify_members_description: '選擇成員、小組或角色',
      specify_units: '成員、小組、角色',
      specify_units_description: '選擇成員、小組或角色，不支持選擇創建人、修改人',
      team_select_label: '选择小組',
      to_title: '查找',
      unit_member: '成員',
      unit_member_description: '選擇指定的一個或多個成員',
      unit_role: '指定角色',
      unit_role_description: '選擇指定的一個或多個角色',
      unit_team: '指定小組',
      unit_team_description: '選擇指定的一個或多個小組',
      user: '用戶',
      user_description: '空間站內的用戶，例如創建者、更新者',
      user_select_label: '選擇用戶',
    },
  },
  unit_selected_modal: {
    empty_data: '資料為空',
    guest: '訪客',
    organization: '組織',
    role: '角色',
    selected_team: '已選',
  },
  upgrade: {
    upgrade: '升級',
    upgrade_title: '升級空間站',
    upgrade_to_pro: '升級到 Pro',
    upgrade_to_pro_button: '升級到 Pro',
    upgrade_to_pro_description: '升級到 Pro 解鎖更多功能',
  },
  user: {
    about: '關於',
    account: '個人賬戶',
    avatar: '頭像',
    bind_email: '綁定郵箱',
    bind_phone: '綁定手機號碼',
    change_password: '變更密碼',
    confirm_password: '確認密碼',
    current_password: '目前密碼',
    custom_colors: {
      custom: '自定義',
      default: '默認',
      label: '自定義顏色（Beta）',
    },
    debug: '調試',
    download: '下载应用',
    email: '電子郵件',
    email_already_bound: '電子郵件已經綁定',
    email_bind_success: '綁定成功',
    email_send_success: '發送成功',
    enter_email: '請輸入電子郵件',
    enter_phone: '請輸入手機號碼',
    enter_verification_code: '請輸入驗證碼',
    get_verification_code: '獲取驗證碼',
    invite_your_friends_to_register_and_get_1000_bk_coins: '邀請你的朋友註冊並獲得 1,000 積分',
    language: '語言',
    loading: '載入中...',
    member_name: '成員名稱',
    name: '姓名',
    new_email: '新電子郵件',
    new_password: '新密碼',
    nickname: '暱稱',
    no_email: '沒有郵箱',
    no_name: '無名稱',
    password: '密碼',
    personal_info: '個人資訊',
    personal_settings: '個人設定',
    phone: '手機號碼',
    phone_already_bound: '手機號碼已經綁定',
    phone_bind_success: '綁定成功',
    phone_send_success: '發送成功',
    preference: '偏好設定',
    profile: '個人資料',
    sessions_current: '當前',
    settings: '設定',
    sign_out: '登出',
    theme: '顯示模式',
    theme_style: {
      bika: 'Bika',
      dracula: 'Dracula (紫色)',
      label: '主題',
      solarized: 'Solarized (綠色)',
    },
    timezone: '時區',
    update_email: '修改邮箱',
    updated: '更新成功',
    verification_code: '驗證碼',
    verification_code_send_success: '驗證碼發送成功',
    verify_email: '驗證電子郵件',
    website: '網站',
  },
  website: {
    about_bika: '關於 Bika.ai',
    api_doc: 'API 文檔',
    architecture_of: '架構圖: <%= name %>',
    blog: '博客',
    blog_description: '探索如何使用 Bika.ai 建立您自己的智慧 AI 公司，獲取實用技巧和見解。',
    blog_title: 'Bika.ai 博客',
    change_region: '切換語言',
    coming_soon: '該功能正在開發中，敬請期待',
    compares: '產品比較',
    contact: {
      contact_discord: '進入 Discord 社群',
      contact_email: '去聯繫',
      contact_form: '聯繫銷售',
      contact_us: '聯繫我們',
      discord: 'Discord',
      discourse_community: '社區',
      done_button: '我已联系',
      email: '郵件',
      line: 'LINE',
      more: '更多咨询方式',
      sales: '聯繫銷售',
      scan_code: '请使用微信扫描二维码咨询',
      support: '聯繫客服',
      via_discord: '加入我們的 Discord 社區',
      via_email: '通過電子郵件聯繫我們 <EMAIL>',
      via_line: '請掃QR Code 加到我們的 LINE',
      via_sales: '聯繫銷售',
      via_support: '聯繫服務團隊吐槽或報BUG',
      via_wecom: '請掃QR Code 加到我們的 WeChat',
      wechat: '微信',
    },
    contact_sales: '聯絡銷售',
    contact_service: '聯絡客服',
    create_template_with_ai: '使用AI創建',
    discover: '發現AI應用模板',
    help: '幫助',
    help_center: '幫助中心',
    help_video: '教學視頻',
    install_selfhosted: '下載安裝',
    other_solutions: '更多場景和解決方案',
    price: '價格',
    pricing: {
      description:
        'Bika.ai 提供靈活的定價方案，包括自託管、本地部署和專屬雲服務，以滿足您的特定需求。選擇最適合您的方案。',
      title: '定價',
    },
    search_or_build_ai_app_placeholer: '搜索或告诉AI你的需求',
    template: '模板',
    video: {
      marketing: '1.什麼是Bika.ai?',
      onboarding: '2. 快速開始',
      product: '3. 深入瞭解',
    },
    visit_website: '訪問官網',
  },
  welcome: {
    explore_templates: '探索AI智能體模板',
    get_started: '開始使用',
    message: '很高興遇見您~ Bika.AI是一個AI組織者，讓您可以聊天、構建、管理AI智能體團隊，打造您的一人AI公司。',
    mobile_get_started_description: '請訪問 {{web}} 使用網頁版',
    more: '您可以下載bika.ai的移動應用',
    title: ' 您好，我是Bika',
  },
  widget_config_invalid: '組件配置失效，請重新選擇',
  widget_database_has_delete: '綁定的關聯表被刪除',
  widget_no_data: '無資料',
  widget_settings: {
    add_number: '添加數字',
    add_summary_description: '添加統計值說明',
    add_target_value: '添加目標值',
    all_records: '所有記錄',
    chart_option_database_had_been_deleted: '數據庫已失效，請重新選擇',
    chart_option_field_had_been_deleted: '字段已失效，請重新選擇',
    chart_option_view_had_been_deleted: '視圖已失效，請重新選擇',
    column_dimension_field: '列維度',
    column_dimension_sort_config: '設置列維度的排序',
    exclude_zero_point: '排除零點',
    jump_link_url: '跳轉到關聯表',
    more_settings: '更多設置',
    null: '[空]',
    options_config: {
      aggregation_by_field: '統計指定欄位',
      ai_write: 'AI 寫作',
      asc: '升序',
      avg: '平均值',
      bar_chart: '柱狀圖',
      count_records: '記錄總數',
      custom: '自訂',
      database: '內置資料表',
      default: '默認',
      desc: '降序',
      filled: '已填寫',
      hidden: '隱藏',
      line_chart: '折線圖',
      max: '最大值',
      min: '最小值',
      mysql: 'MySQL',
      none: '無',
      not_filled: '未填寫',
      percent_empty: '未填寫佔比',
      percent_filled: '已填寫占比',
      percent_unique: '唯一值占比',
      pie_chart: '圓餅圖',
      postgresql: 'PostgreSQL',
      sort_by_x: '按 X 排序',
      sort_by_y: '按 Y 排序',
      sum: '求和',
      unique: '唯一值',
    },
    row_dimension_field: '行維度',
    row_dimension_sort_config: '設置行維度的排序',
    select_axis_sort: '選擇排序軸',
    select_chart_type: '選擇圖表類型',
    select_data_source: '選擇數據源',
    select_statistics_field: '選擇統計欄位',
    select_statistics_type: '選擇統計類型',
    select_theme_color: '選擇主題顏色',
    select_view_source: '選擇一個視圖作為數據源',
    select_widget_metrics_types: '選擇一個欄位進行統計',
    select_widget_type: '選擇小組件類型',
    separeted_multi_value: '分離多值',
    set_sort_rules: '設置排序規則',
    show_data_tips: '顯示數據提示',
    show_empty_values: '顯示空值',
    show_totals: '顯示總計',
    summary_by_field: '選擇一個欄位作為統計值',
    widget_Value_x: '值 (X軸)',
    widget_Value_y: '值 (Y軸)',
    widget_dimension: '維度 (X軸)',
    widget_name: '組件名稱',
    widget_operate_delete: '刪除組件',
    widget_url_config: '網址設置',
  },
  widget_title_agenda: '議程',
  widget_title_report: '最近報告',
  widget_title_todo: '最近待辦事項',
  wizard: {
    agent_engineer: '智能體工程師',
    ai_wizard: 'AI 嚮導',
    ai_wizard_description:
      'AI 向導是一款智能引導系統，旨在幫助用戶全面了解和掌握產品功能。通過友好的界面和逐步的介紹，AI 向導將引導您熟悉每個模塊的使用方法和應用場景。',
    already_freshed: '已刷新',
    an_error_occured: '發生錯誤',
    are_sure_to_top_up: '確認儲值',
    bika_tip1_description:
      'Bika.AI 訂閱服務正式開啟啦！當前空間站是免費版，已獲贈{count}，後續使用 Bika.AI 功能會消耗積分',
    bika_tip1_title: '🚀 會員計劃上線',
    bika_tip2_description:
      'Bika.AI 訂閱服務正式開啟啦！當前空間站是免費版，已獲贈{count}，後續使用 Bika.AI 功能會消耗積分',
    bika_tips1:
      'Bika.AI 因你的熱愛與奇思妙想不斷成長～現在，我們推出會員計劃與功能升級，要把更具突破性的 AI 體驗，帶給每一位創作者！',
    bika_upgraded_tip2_description:
      '你的邀請碼已備好！新用戶用你的邀請碼註冊，你和好友各得{count}獎勵，快喊小夥伴一起玩～',
    bika_upgraded_tip2_title: '👫 邀請好友，賺積分',
    build_scratch_description: '從零開始建立',
    build_scratch_name: '🏗️ 從零開始構建',
    check_invite_code: '查看邀請碼',
    choose_tailor_myself: '對 Bika.ai 有較高了解，希望從零開始自訂化構建自己的 AI 智能體和資源',
    close_conversation: '關閉對話',
    contact_label: '您的其它聯絡方式?如WhatsApp、LINE、Facebook、微信、電話、電子郵箱等?',
    create_space: '創建空間',
    creating: '正在創建',
    credits: '{sum}積分',
    fill_write_code: '請填寫有效的邀請碼，即可暢享系統全部精彩內容，開啟你的專屬體驗之旅 ～',
    filter_by_columns: '已經根據{Count}列過濾',
    finance: '📊 財務',
    finance_description: '面向資產追蹤、HR履歷代理的智能應用',
    get_invite_code: '去社群獲取邀請碼',
    industry_job_label: '您目前所在的行業是什麼？您的職位是什麼？',
    installation_completed: '安裝完成',
    installer: '安裝程式',
    invite_code_added: '邀請填寫成功，已獲得獎勵',
    invite_coin_description: '填寫他人的邀請碼，雙方都可獲得{count}',
    marketing: '📣 行銷',
    marketing_description: '面向行銷的智能應用，包括AI寫手、簡報助手、AI設計師、郵件行銷、X(Twitter)社群管理等',
    membership_modal_title: 'Bika.AI 全新升級！',
    mirror_from: '鏡像來自',
    my_credits: '個人積分',
    navigate_premium_plan: '了解會員計劃',
    new_gift_detail: '感謝你的支持！Bika.AI 額外送上{count}，快收下這份心意～',
    new_wizard_created: '新的聊天已创建',
    official_website: '官網',
    onboarding: '新手引導',
    one_person_company: '👨‍💻 一人公司',
    one_person_company_description: '面向一人公司的智能應用，包括AI寫手、簡報助手、AI設計師、AI程式設計師等，',
    placeholder_invite_code: '請填寫邀請碼',
    please_change_invite_code: '請更換邀請碼再試。',
    product: '📋 產品',
    product_description: '面向產品的智能應用，包括AI寫手、郵件行銷、X(Twitter)社群管理等',
    quick_start: '快速入門',
    row_group_by_columns: '已經根據{Count}列分組',
    sales: '💼 銷售',
    sales_description: '面向銷售的智能應用，包括AI寫手、郵件行銷、X(Twitter)社群管理、Github Issues創建者等',
    select_role: '請選擇您的角色。我們將為您安裝預設的Agentic Apps 模板。',
    selected_templates: '即將要安裝以下模板',
    some_columns_hided: '已隱藏{Count}列',
    sorted_by_columns: '已經根據{Count}列排序',
    support: '🤝 支援',
    support_description: '面向支援的智能應用，包括AI寫手、郵件行銷等',
    topup_space_by_credits: '你當前有{count}，全部充值到「{spaceName}」',
    topup_to_space: '充值到空間站',
    use_case_label: '您希望通過使用Bika.ai，滿足你日常生活工作中的什麼使用場景，和解決什麼問題?',
    welcome: '歡迎來到 Bika.AI',
    welcome_website: '歡迎來到Bika.ai',
    wizard: '嚮導',
    wizard_new_user_gift: ' 新用戶專屬福利！',
  },
};
export default dict;
