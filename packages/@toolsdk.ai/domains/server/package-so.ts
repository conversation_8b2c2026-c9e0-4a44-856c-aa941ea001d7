import { estypes } from '@elastic/elasticsearch';
import { iString, iStringParse } from '@bika/types/i18n/bo';
import { PaginationInfo, PaginationSchema } from '@bika/types/shared/pagination';
import { db, Package as PackagePO } from '@toolsdk.ai/orm';
import { PackageDAO } from '@toolsdk.ai/orm/prisma/dao/package-dao';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import featured from '@toolsdk.ai/registry/config/featured.mjs';
import {
  ActionTemplate,
  IntegrationTemplate,
  PackageDataBO,
  PackageDataTypes,
  ToolApp,
} from '@toolsdk.ai/sdk-ts/types/bo';
import { PackageCreateDTO } from '@toolsdk.ai/sdk-ts/types/dto';
import { PackageDetailVO, PackageSimpleVO } from '@toolsdk.ai/sdk-ts/types/vo';
import { PackageDataAdapterFactory } from './adapter/factory';
import { ConfigurationInstanceSO } from './configuration-instance-so';
import { ConfigurationSO } from './configuration-so';
import { PackageClientSO } from './package-client-so';
import { ToolSO } from './tool-so';
import { ToolSDKAttachmentSO } from './toolsdk-attachment-so';
import type { IPackageSearchDTO, IPackageToolRunFullBody } from './types';

export class PackageSO {
  private _model: PackagePO;

  private _starCount?: number;

  public constructor(model: PackagePO, starCount?: number) {
    this._model = model;
    this._starCount = starCount;
  }

  get model() {
    return this._model;
  }

  get id() {
    return this._model.id;
  }

  get key() {
    return this._model.key;
  }
  get version() {
    return this._model.version;
  }

  get slug() {
    return { slug: this._model.key, version: this._model.version };
  }

  get visibility() {
    return this._model.visibility;
  }

  get createdBy() {
    return this._model.createdBy;
  }

  get packageData() {
    const packageData = this._model.packageData as PackageDataBO;
    return packageData;
  }

  get packageDataAdapter() {
    const packageData = this._model.packageData as PackageDataBO;
    return PackageDataAdapterFactory.new(packageData);
  }

  get cacheTime() {
    return this._model.cacheTime;
  }

  /**
   *  如果超过 1 天，标记过期
   */
  isCacheExpired() {
    if (!this._model.cacheAppData) {
      return true; // If no cache data, consider it expired
    }
    if (this.cacheTime) {
      const oneDayMs = 24 * 60 * 60 * 1000; // 1 day in milliseconds
      const now = new Date();
      const cacheDate = new Date(this.cacheTime);
      return now.getTime() - cacheDate.getTime() > oneDayMs;
    }
    return true; // If no cache time is set, consider it expired
  }

  /**
   * Refresh Components, Tools Cache
   *
   * @param presetTools
   */
  async refreshCache(presetToolApp?: ToolApp) {
    const toolapp = presetToolApp || (await this.packageDataAdapter.getToolApp());

    await PackageDAO.cache(this.id, { toolapp });
  }

  get cacheAppData(): ToolApp {
    return this._model.cacheAppData as ToolApp;
  }

  async listTools(useCache: boolean = true): Promise<ToolSO[]> {
    let tools: ActionTemplate[] = [];
    if (useCache && !this.isCacheExpired()) {
      const toolApp: ToolApp = this.cacheAppData;
      if (!toolApp.tools) return [];
      tools = Object.values(toolApp.tools);
    } else {
      const toolApp = await this.packageDataAdapter.getToolApp();
      if (!toolApp.tools) return [];

      tools = Object.values(toolApp.tools);

      await this.refreshCache(toolApp);
    }

    return Object.values(tools).map((tool) => new ToolSO(tool));
  }

  async getConfiguration(useCache: boolean = true): Promise<ConfigurationSO | undefined> {
    let confBO: IntegrationTemplate | undefined;
    if (useCache && !this.isCacheExpired()) {
      const toolApp: ToolApp = this.cacheAppData;
      confBO = toolApp.authentication;
    } else {
      confBO = await this.packageDataAdapter.findConfiguration();
    }
    if (!confBO) {
      return undefined;
    }
    return new ConfigurationSO(confBO, this.key, this.version);
  }

  async getFavoriteCount(useCache: boolean = true): Promise<number> {
    if (useCache && this._starCount) {
      return this._starCount;
    }
    const count = await db.prisma.favorite.count({
      where: {
        packageId: this.id,
      },
    });
    this._starCount = count;
    return count;
  }

  async fetchConfigurationAndToolsVOs(useCache: boolean = true) {
    const [configuration, tools] = await Promise.all([this.getConfiguration(useCache), this.listTools(useCache)]);

    // const { configuration, tools } = await this.packageDataAdapter.getConfigurationAndListTools();
    const fetchTools = async () => {
      if (!tools) {
        return [];
      }
      return Promise.all(
        tools.map((t) => {
          return t.toVO();
        }),
      );
    };

    return {
      configuration: configuration?.toVO(),
      tools: await fetchTools(),
    };
  }

  async toSimpleVO(): Promise<PackageSimpleVO> {
    const { key, version, name, description, logo, packageType, updatedAt, cacheStar } = this._model;

    const stars: number = (await this.getFavoriteCount()) + (cacheStar || 0);

    return {
      packageType: packageType as PackageDataTypes,
      key,
      url: await this.packageDataAdapter.getUrl(),
      version,
      name: iStringParse(name as iString),
      description: iStringParse(description as iString),
      logo: ToolSDKAttachmentSO.buildAvatarLogo(logo),
      validated: this._model.validated,
      stars,
      updatedAt: updatedAt.toISOString(),
    };
  }

  async toDetailVO(developerId?: string): Promise<PackageDetailVO> {
    const fetchIsStarred = async () => {
      if (!developerId) {
        return false;
      }
      const count = await db.prisma.favorite.count({
        where: {
          packageId: this.id,
          createdBy: developerId,
        },
      });
      return count > 0;
    };

    const [configurationAndToolsVOs, pkg, pkgClients, isStarred, readme] = await Promise.all([
      this.fetchConfigurationAndToolsVOs(),
      this.toSimpleVO(),
      PackageClientSO.list(),
      fetchIsStarred(),
      this.packageDataAdapter.getReadme(),
    ]);
    const clients = pkgClients.map((client) => client.toVO());

    let ownerName = 'User';
    if (pkg.url && pkg.url.includes('github.com')) {
      try {
        const match = pkg.url.match(/github.com\/([^/]+)\/([^/]+)/);
        if (match && match[1]) {
          ownerName = match[1];
        }
      } catch (e) {
        console.error('Error parsing GitHub URL:', e);
      }
    }

    return {
      ...pkg,
      configuration: configurationAndToolsVOs.configuration,
      tools: configurationAndToolsVOs.tools,
      isStarred,
      clients,
      readme,
      creator: {
        avatar: {
          type: 'COLOR',
          color: 'red',
        },
        name: ownerName,
      },
    };
  }

  async runTool(body: IPackageToolRunFullBody, _opts?: { instanceId?: string; externalId?: string }) {
    // build auth data
    const buildAuthData = async () => {
      if ('envs' in body) {
        return body.envs;
      }
      const configurationInstanceId = 'configurationInstanceId' in body ? body.configurationInstanceId : undefined;
      if (!configurationInstanceId) {
        return undefined;
      }
      const cfgIns = await ConfigurationInstanceSO.findById(configurationInstanceId);
      if (cfgIns.packageId !== this.id) {
        throw new Error('Configuration instance exceptions');
      }
      return cfgIns.inputData;
    };
    const authData = await buildAuthData();

    const output = await this.packageDataAdapter.runTool(body, authData);

    // todo: save run history
    // const { instanceId = '', externalId } = opts || {};

    return output;
  }

  async runToolDynamicFields(toolKey: string, inputData?: Record<string, unknown>) {
    const tools = await this.listTools();
    // check if toolKey is valid
    const tool = tools.find((t) => t.key === toolKey);
    if (!tool) {
      throw new Error('Tool not found');
    }
    return tool.runDynamicFields(inputData);
  }

  static initWithModel(model: PackagePO, starCount?: number) {
    return new PackageSO(model, starCount);
  }

  static async getById(id: string) {
    const model = await db.prisma.package.findUnique({
      where: {
        id,
      },
      include: {
        _count: {
          select: {
            favorites: true,
          },
        },
      },
    });

    if (!model) {
      throw new Error(`Package ${id} not found`);
    }

    return new PackageSO(model, model._count.favorites);
  }

  static async getByKey(key: string, version: string = ''): Promise<PackageSO> {
    const model = await db.prisma.package.findUnique({
      where: {
        key_version: {
          key,
          version,
        },
      },
      include: {
        _count: {
          select: {
            favorites: true,
          },
        },
      },
    });

    if (!model) {
      const flag = version.length > 0 ? key + '@' + version : key;
      throw new Error(`Package ${flag} not found`);
    }

    return new PackageSO(model, model._count.favorites);
  }

  static async page(args?: IPackageSearchDTO): Promise<{ pagination: PaginationInfo; data: PackageSimpleVO[] }> {
    const { pagination, query, developerId, category, scope = 'ALL_PUBLIC' } = args || {};
    const { pageNo, pageSize } = PaginationSchema.parse(pagination ?? {});

    if (!developerId && ['DEVELOPER_STAR', 'DEVELOPER_ALL', 'DEVELOPER_PUBLIC', 'DEVELOPER_PRIVATE'].includes(scope)) {
      return { pagination: { pageNo, pageSize, total: 0 }, data: [] };
    }

    // build dsl query
    const buildDslQuery = async () => {
      // const where: Prisma.PackageWhereInput = { isDeleted: false };
      const must: estypes.QueryDslBoolQuery['must'] = [];

      if (category === 'featured') {
        // where.key = {
        //   // get featured.mjs list
        //   in: featured,
        // };
        must.push({ terms: { 'indexData.packageKey.keyword': featured } });
      } else if (category) {
        // where.categoryId = category;
        must.push({ term: { 'indexData.categoryId.keyword': category } });
      }

      switch (scope) {
        case 'ALL_VALIDATED':
          // where.validated = true;
          must.push({ term: { 'indexData.validated': true } });
          break;
        case 'ALL_PUBLIC':
          // where.visibility = 'PUBLIC';
          must.push({ term: { 'indexData.visibility.keyword': 'PUBLIC' } });
          break;
        case 'ALL_PUBLIC_WITH_DEVELOPER_PRIVATE':
          if (developerId) {
            // where.OR = [{ visibility: 'PUBLIC' }, { createdBy: developerId, visibility: 'PRIVATE' }];
            must.push({
              bool: {
                should: [
                  { term: { 'indexData.visibility.keyword': 'PUBLIC' } },
                  {
                    bool: {
                      must: [
                        { term: { 'indexData.createdBy.keyword': developerId } },
                        { term: { 'indexData.visibility.keyword': 'PRIVATE' } },
                      ],
                    },
                  },
                ],
              },
            });
          } else {
            // where.visibility = 'PUBLIC';
            must.push({ term: { 'indexData.visibility.keyword': 'PUBLIC' } });
          }
          break;
        case 'DEVELOPER_STAR': {
          // where.favorites = {
          //   some: {
          //     createdBy: developerId,
          //   },
          // };
          const favorites = await db.prisma.favorite.findMany({
            where: {
              createdBy: developerId,
            },
          });
          if (favorites.length > 0) {
            must.push({ terms: { id: favorites.map((f) => f.packageId) } });
          }
          break;
        }
        case 'DEVELOPER_ALL':
          // where.createdBy = developerId;
          must.push({ term: { 'indexData.createdBy.keyword': developerId } });
          break;
        case 'DEVELOPER_PUBLIC':
          // where.visibility = 'PUBLIC';
          // where.createdBy = developerId;
          must.push({ term: { 'indexData.visibility.keyword': 'PUBLIC' } });
          must.push({ term: { 'indexData.createdBy.keyword': developerId } });
          break;
        case 'DEVELOPER_PRIVATE':
          // where.visibility = 'PRIVATE';
          // where.createdBy = developerId;
          must.push({ term: { 'indexData.visibility.keyword': 'PRIVATE' } });
          must.push({ term: { 'indexData.createdBy.keyword': developerId } });
          break;
        default:
          throw new Error(`Unsupported scope ${scope}`);
      }

      const keyword = query?.trim();
      // no keyword search
      if (!keyword || keyword.length === 0) {
        // return where;
        return { bool: { must } };
      }

      // keyword search
      must.push({
        bool: {
          should: [
            { match: { 'indexData.name': keyword } },
            { match: { 'indexData.description': keyword } },
            { match: { 'indexData.toolSummary': keyword } },
          ],
        },
      });

      // const or: Prisma.PackageWhereInput[] = [
      //   { name: { string_contains: keyword } },
      //   { key: { contains: keyword } },
      //   ...i18n.locales.map((lang) => ({ name: { path: [lang], string_contains: keyword } })),
      // ];
      // return { AND: [where, { OR: or }] };
      return { bool: { must } };
    };
    const dslQuery = await buildDslQuery();

    const { rows: items, total } = await db.search.advancedSearch('PACKAGE', {
      query: dslQuery,
      sort: [{ 'indexData.stars': { order: 'desc' } }],
      size: pageSize,
      from: pageSize * (pageNo - 1),
    });
    const packageIds = items.map((item) => item.id);

    // query db
    const rows = await db.prisma.package.findMany({
      where: {
        id: { in: packageIds },
        isDeleted: false,
      },
      include: {
        _count: {
          select: {
            favorites: true,
          },
        },
      },
    });
    // const [rows, total] = await Promise.all([
    //   db.prisma.package.findMany({
    //     where,
    //     include: {
    //       _count: {
    //         select: {
    //           favorites: true,
    //         },
    //       },
    //     },
    //     skip: pageSize * (pageNo - 1),
    //     take: pageSize,
    //     orderBy: {
    //       // 星星越多越靠前
    //       favorites: {
    //         _count: 'desc',
    //       },
    //     },
    //   }),
    //   db.prisma.package.count({ where }),
    // ]);

    if (args?.category === 'featured') {
      rows.sort((a, b) => featured.indexOf(a.key) - featured.indexOf(b.key));
    }

    const packages = await Promise.all(
      rows.map(async (po, index) => {
        const pkg = new PackageSO(po, po._count.favorites);
        return { vo: await pkg.toSimpleVO(), index };
      }),
    );
    // sort by index
    const data = packages.sort((a, b) => a.index - b.index).map((item) => item.vo);
    return { pagination: { pageNo, pageSize, total }, data };
  }

  static async count() {
    return db.prisma.package.count();
  }

  static async listByDeveloper(developerId: string) {
    return this.page({ developerId, scope: 'DEVELOPER_ALL' });
  }

  static async search(args?: IPackageSearchDTO) {
    return this.page(args);
  }

  static async create(developerId: string, dto: PackageCreateDTO) {
    const model = await PackageDAO.create(developerId, dto);
    return new PackageSO(model);
  }

  static async upsert(developerId: string, dto: PackageCreateDTO) {
    const model = await PackageDAO.upsert(developerId, dto);
    return new PackageSO(model);
  }
}
