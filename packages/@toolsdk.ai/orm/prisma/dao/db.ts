import { getAppEnv } from 'sharelib/app-env';
import { MinioClient } from 'sharelib/minio-client';
import { Searcher } from 'sharelib/search';
import { PrismaClient } from '../prisma-client';
import { ToolSDKSearchIndex } from '../../search';

const prismClient =
  process.env.ENABLE_POSTGRES_LOGGING === 'true'
    ? new PrismaClient({
        log: ['query', 'info', 'warn', 'error'],
      })
    : new PrismaClient();

export class db {
  private static _minioClient: MinioClient;

  private static _searchClient: Searcher<ToolSDKSearchIndex>;

  private static lazyInitMinio() {
    if (!this._minioClient) {
      this._minioClient = new MinioClient();
    }
  }

  /**
   * 初始化 Minio bucket，在seed.ts中调用
   */
  static async initMinioBucket() {
    this.lazyInitMinio();
    await this._minioClient.checkAndInitBucket();
  }

  static get minioClient() {
    this.lazyInitMinio();
    return this._minioClient;
  }

  static get prisma() {
    return prismClient;
  }

  static get search() {
    if (this._searchClient) {
      return this._searchClient;
    }
    const appEnv = getAppEnv();
    const indexPrefix = `toolsdk-${appEnv}`.toLowerCase();
    this._searchClient = new Searcher<ToolSDKSearchIndex>(indexPrefix);
    return this._searchClient;
  }
}
