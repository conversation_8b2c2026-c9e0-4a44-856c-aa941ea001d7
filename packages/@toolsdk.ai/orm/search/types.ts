import { z } from 'zod';

export const ToolSDKSearchIndexTypes = [
  'PACKAGE', // 包含所有的包信息
] as const;

export const ToolSDKSearchIndexTypeSchema = z.enum(ToolSDKSearchIndexTypes);
export type ToolSDKSearchIndexType = z.infer<typeof ToolSDKSearchIndexTypeSchema>;

export const PackageSearchIndexSchema = z.object({
  type: z.literal(ToolSDKSearchIndexTypeSchema.enum.PACKAGE),
  id: z.string(),
  packageKey: z.string(),
  packageVersion: z.string().optional(),
  name: z.string(),
  description: z.string().optional(),
  categoryId: z.string().optional(),
  visibility: z.string().optional(),
  validated: z.boolean().optional(),
  stars: z.number().optional(),
  // 多个工具的简要描述，name + description。不用对象结构，多个直接拼接，减少索引字段数量
  toolSummary: z.string().optional(),
  createdBy: z.string(),
});
export type PackageSearchIndex = z.infer<typeof PackageSearchIndexSchema>;

export const ToolSDKSearchIndexSchema = PackageSearchIndexSchema;
// export const ToolSDKSearchIndexSchema = z.union([PackageSearchIndexSchema, xxx]);
export type ToolSDKSearchIndex = z.infer<typeof ToolSDKSearchIndexSchema>;
