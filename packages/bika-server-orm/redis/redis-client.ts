/* eslint-disable @typescript-eslint/no-explicit-any */
import type { RedisJSON } from '@redis/json/dist/commands';
import { createClient, type RedisClientType } from 'redis';
import { redisLock } from './lock';
import Redlock from './redlock';
import Redis from 'ioredis';

/**
 * 传入对象，组合成一个key，支持字面量
 */
export type RedisKeyBuilder = string | Record<string, string | number>;

/**
 * 真是的key和value
 */
export type RedisResultKeyValue = { key: string; value: string | null | undefined };

/**
 * 统一封装，进行RedisJSON
 *
 * 这个get和set，都是JSON字段，原生redis，无需序列化
 *
 *
 * 这个RedisJSON支持Date类型，但转换成string了，因此还是建议使用zod schema的z.string().date()来校验哦
 *
 */
export class RedisClient {
  private _redis: RedisClientType;

  private _redlock: Redlock;

  private _connected = false;

  private _connectPromise: Promise<void | RedisClientType>;

  public get connection() {
    return this._connectPromise;
  }

  public get connected() {
    return this._connected;
  }

  private parseKeyToString(theKey: RedisKeyBuilder, prefix: string = '') {
    if (typeof theKey === 'string') {
      if (prefix === undefined || prefix === '') return theKey;
      return `${prefix}:${theKey}`;
    }

    const strBuilder = [];
    if (prefix) strBuilder.push(prefix);

    for (const k of Object.keys(theKey)) {
      strBuilder.push(k);
      strBuilder.push(theKey[k].toString());
    }

    return strBuilder.join(':');
  }

  public static get redisUrl(): string {
    return process.env.REDIS_URL || 'redis://default:@redis-master:6379';
  }

  constructor() {
    this._redis = createClient({
      url: RedisClient.redisUrl,
    })
      .on('error', (err) => {
        console.log('Redis Client Error', err);
      })
      .on('end', () => {
        console.log('Redis Client Disconnected');
      })
      .on('connect', () => {
        console.log('Redis Client prepared to connect');
      })
      .on('ready', () => {
        console.log('Redis Client Connected');
      }) as RedisClientType;
    this._connectPromise = this._redis.connect();

    this._connectPromise.then(() => {
      this._connected = true;
    });

    // TODO: 后续 io-redis 和 node-redis 仅保留一个
    this._redlock = new Redlock([new Redis(RedisClient.redisUrl)], {
      // The expected clock drift; for more details see:
      // http://redis.io/topics/distlock
      driftFactor: 0.01, // multiplied by lock ttl to determine drift time

      // The max number of times Redlock will attempt to lock a resource
      // before erroring.
      retryCount: 10,

      // the time in ms between attempts
      retryDelay: 200, // time in ms

      // the max time in ms randomly added to retries
      // to improve performance under high contention
      // see https://www.awsarchitectureblog.com/2015/03/backoff.html
      retryJitter: 200, // time in ms

      // The minimum remaining time on a lock before an extension is automatically
      // attempted with the `using` API.
      automaticExtensionThreshold: 500, // time in ms
    });
  }

  public async get(key: RedisKeyBuilder, prefix: string = 'str'): Promise<any | null> {
    await this.connection;
    const realKey = this.parseKeyToString(key, prefix);
    const val = await this._redis.get(realKey);
    if (!val) return null;

    return JSON.parse(val);
  }

  public async setAdd<T>(
    key: RedisKeyBuilder,
    value: T,
    expiredsSeconds: number = 60,
    prefix: string = 'set',
  ): Promise<RedisResultKeyValue> {
    await this.connection;
    const realKey = this.parseKeyToString(key, prefix);
    const newVal = JSON.stringify(value);
    await this._redis.sAdd(realKey, newVal);
    await this._redis.expire(realKey, expiredsSeconds);
    return {
      key: realKey,
      value: newVal,
    };
  }

  public async queueCount(key: RedisKeyBuilder, prefix: string = 'queue'): Promise<number> {
    await this.connection;
    const realKey = this.parseKeyToString(key, prefix);
    const count = this._redis.lLen(realKey);
    return count;
  }

  public async queuePop<T>(key: RedisKeyBuilder): Promise<T | null> {
    await this.connection;
    const realKey = this.parseKeyToString(key, 'queue');
    const val = await this._redis.rPop(realKey);

    return val ? (JSON.parse(val) as T) : null;
  }

  public async queuePush<T>(
    key: RedisKeyBuilder,
    value: T,
    expiredsSeconds: number = 60 * 15, // 队列有15分钟的处理时间默认
  ): Promise<RedisResultKeyValue> {
    await this.connection;
    const realKey = this.parseKeyToString(key, 'queue');
    const newVal = JSON.stringify(value);
    await this._redis.lPush(realKey, newVal);
    await this._redis.expire(realKey, expiredsSeconds);
    return {
      key: realKey,
      value: newVal,
    };
  }

  /**
   * 获取keys
   *
   * @param key
   * @param prefix 字段前面有个“类型”，set、str、queue等，可不传
   * @returns
   */
  public async keys(key: RedisKeyBuilder, prefix?: string): Promise<string[]> {
    await this.connection;
    const realKey = this.parseKeyToString(key, prefix || '');
    let cursor: number = 0;
    const keys: string[] = [];
    do {
      const reply = await this._redis.scan(cursor, {
        COUNT: 100,
        MATCH: `*${realKey}`,
      });
      cursor = reply.cursor;
      keys.push(...reply.keys);
    } while (cursor !== 0);
    return keys;
  }

  public async scanKeys(
    key: RedisKeyBuilder,
    param?: {
      prefix?: string;
      cursor?: number;
      count?: number;
    },
  ): Promise<string[]> {
    await this.connection;
    const realKey = this.parseKeyToString(key, param?.prefix || '');
    const count = param?.count || 5;
    const cursor = param?.cursor || 0;
    const reply = await this._redis.scan(cursor, {
      COUNT: count,
      MATCH: `*${realKey}`,
    });
    return reply.keys;
  }

  public async setGet<T>(key: RedisKeyBuilder, prefix: string = 'set'): Promise<T[] | null> {
    await this.connection;
    const realKey = this.parseKeyToString(key, prefix);

    const members = await this._redis.sMembers(realKey);
    if (members) {
      const vals: T[] = [];
      for (const member of members) {
        vals.push(JSON.parse(member));
      }
      return vals;
    }
    return null;
  }

  public async del(key: RedisKeyBuilder, prefix: string = 'str'): Promise<number> {
    const realKey = this.parseKeyToString(key, prefix);
    return this._redis.del(realKey);
  }

  /**
   * 注意，value会被JSON.stringify，部分字段可能不支持哦，如date会变成string
   *
   * @param key
   * @param value
   * @returns
   */
  public async set(
    key: RedisKeyBuilder,
    value: any | undefined,
    expiresSeconds: number = 60,
    prefix: string = 'str',
  ): Promise<RedisResultKeyValue> {
    await this.connection;
    const realKey = this.parseKeyToString(key, prefix);
    if (value === undefined) {
      await this._redis.del(realKey);
      return {
        key: realKey,
        value: undefined,
      };
    }
    const newVal = await this._redis.set(realKey, JSON.stringify(value));
    await this._redis.expire(realKey, expiresSeconds);
    return {
      key: realKey,
      value: newVal,
    };
  }

  /**
   * RedisJSON原生字段，与序列化不同，这个可以做“复杂JSON查询”
   *
   * @param key
   * @returns
   */
  public async getJSON(key: RedisKeyBuilder): Promise<RedisJSON | null> {
    await this.connection;
    const realKey = this.parseKeyToString(key, 'json');
    const val = await this._redis.json.get(realKey);

    return val;
  }

  public async setJSON(key: RedisKeyBuilder, value: RedisJSON | undefined, expiresSeconds = 60) {
    await this.connection;
    const realKey = this.parseKeyToString(key, 'json');
    if (value === undefined) {
      await this._redis.del(realKey);
      return 'OK';
    }
    const newSet = await this._redis.json.set(realKey, '$', value);
    await this._redis.expire(realKey, expiresSeconds);
    return newSet;
  }

  // 直接拿到ioredis
  get redisOriginClient() {
    return this._redis;
  }

  get lock() {
    return redisLock(this._redis);
  }

  get redlock() {
    return this._redlock;
  }

  async setNX(key: string, value: number, expiresSeconds: number) {
    return this._redis.set(key, value, {
      EX: expiresSeconds,
      NX: true,
    });
  }
}
