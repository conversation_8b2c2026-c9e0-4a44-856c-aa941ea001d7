'use client';

import type { ButtonProps } from '@mui/joy/Button';
import MUIModal from '@mui/joy/Modal';
import ModalClose from '@mui/joy/ModalClose';
import ModalDialog from '@mui/joy/ModalDialog';
import Sheet from '@mui/joy/Sheet';
import Stack from '@mui/joy/Stack';
import type { SxProps } from '@mui/joy/styles/types';
import Typography from '@mui/joy/Typography';
import React from 'react';
import { createRoot } from 'react-dom/client';
import { Button } from './button-component';
import DangerCircleColorFilled from './icons/doc_hide_components/danger_circle_color_filled';
import InfoCircleColorFilled from './icons/doc_hide_components/info_circle_color_filled';
import WarnCircleColorFilled from './icons/doc_hide_components/warn_circle_color_filled';
import { Box } from './layout-components';
import { useSnackBar, SnackbarProvider } from './snackbar/snackbar-component';
import { EllipsisText } from './text';

export interface ModalComponentProps {
  className?: string;
  modalClassName?: string;
  children: React.ReactNode;
  disableEscapeKeyDown?: boolean;
  title?: React.ReactNode;
  onClose?: (event: React.MouseEvent<HTMLButtonElement>, reason: string) => void;
  closable?: boolean;
  width?: number | string;
  height?: number | string;
  minHeight?: number | string;
  minWidth?: number | string;
  zIndex?: number;
  sx?: SxProps;
  slotProps?: React.ComponentProps<typeof MUIModal>['slotProps'];
}

export function ModalComponent(props: ModalComponentProps) {
  const isClosable = props.closable ?? true; // if undefined, default to true
  const { title, zIndex, modalClassName, slotProps } = props;
  const header = () => {
    if (title) {
      return (
        <Stack position="relative" direction="row" justifyContent="center" alignItems="center" height="32px" mb="12px">
          <EllipsisText>
            <Typography textColor="var(--text-primary)" level="h6" flex={1} textAlign="center">
              {title}
            </Typography>
          </EllipsisText>

          {isClosable && <ModalClose sx={{ position: 'static' }} />}
        </Stack>
      );
    }
    if (!title && isClosable) return <ModalClose />;

    return null;
  };

  return (
    <MUIModal
      slotProps={slotProps}
      className={modalClassName}
      open
      onClose={props.onClose}
      disableEscapeKeyDown={props.disableEscapeKeyDown}
      sx={{
        zIndex,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <Sheet
        className={props.className}
        sx={{
          width: props.width || 1000,
          height: props.height || 'auto',
          minHeight: props.minHeight,
          minWidth: props.minWidth,
          overflow: 'auto',
          borderRadius: 'md',
          maxHeight: 'calc(100vh - 32px)',
          p: 2,
          boxShadow: 'lg',
          '--List-radius': '0px',
          ...props.sx,
        }}
        variant="outlined"
      >
        {header()}
        {props.children}
      </Sheet>
    </MUIModal>
  );
}

interface IShowProps {
  title: string;
  content: React.ReactNode;
  okText?: string;
  cancelText?: string;
  onOk?: () => void;
  type: 'info' | 'warning' | 'error';
  width?: number | string;
  zIndex?: number;
}

interface IModalParams {
  title: string;
  content: React.ReactNode;
  okText?: string;
  cancelText?: string;
  onOk?: () => void;
  onClose: () => void;
  type: 'info' | 'warning' | 'error';
  width?: number | string;
  zIndex?: number;
}

const MountModal = (props: IModalParams) => {
  const { title, content, okText = 'OK', cancelText = 'Cancel', onOk, onClose, type, width, zIndex } = props;
  const [loading, setLoading] = React.useState(false);

  const { toast } = useSnackBar();

  const _onOk = async () => {
    setLoading(true);
    try {
      await onOk?.();
      onClose();
    } catch (e) {
      if (e instanceof Error) {
        toast(e.message, {
          variant: 'error',
        });
      } else {
        toast(String(e), {
          variant: 'error',
        });
      }
    } finally {
      setLoading(false);
    }
  };

  let color: ButtonProps['color'] = 'primary';
  if (type === 'error') {
    color = 'danger';
  } else if (type === 'warning') {
    color = 'warning';
  }

  return (
    <MUIModal open onClose={onClose} disableEscapeKeyDown={true} sx={{ zIndex }}>
      <ModalDialog sx={{ width: width || 400, overflow: 'auto' }}>
        <ModalClose />
        <div className={'flex items-center space-x-2'}>
          {type === 'error' && <DangerCircleColorFilled color={'var(--status-danger)'} size={20} />}
          {type === 'warning' && <WarnCircleColorFilled color={'var(--status-warn)'} size={20} />}
          {type === 'info' && <InfoCircleColorFilled color={'var(--brand)'} size={20} />}
          <div className={'text-h6'}>{title}</div>
        </div>

        <Box className={'overflow-x-auto'}>
          <div className={'pl-[28px]'}>{content}</div>
        </Box>
        <div className={'flex space-x-2 mt-6 justify-end'}>
          <Button variant="plain" color="neutral" onClick={onClose} disabled={loading}>
            {cancelText}
          </Button>
          <Button color={color} onClick={_onOk} loading={loading}>
            {okText}
          </Button>
        </div>
      </ModalDialog>
    </MUIModal>
  );
};

/**
 *
 * 这是一个quick modal，简单小窗，不带context (如useLocale是不能用的)
 *
 * 复杂的Modal，请使用SpaceModal或GlobalModal，支持URL路由
 *
 * @param param0
 */
ModalComponent.show = ({
  title,
  okText = 'OK',
  cancelText = 'Cancel',
  content,
  onOk,
  type,
  width,
  zIndex,
}: IShowProps) => {
  const div = document.createElement('div');
  document.body.appendChild(div);
  const root = createRoot(div);

  const onClose = () => {
    root.unmount();
    const body = document.body;
    if (body.contains(div)) {
      document.body.removeChild(div);
    }
  };

  root.render(
    <SnackbarProvider>
      <MountModal
        title={title}
        content={content}
        okText={okText}
        cancelText={cancelText}
        onOk={onOk}
        onClose={onClose}
        type={type}
        width={width}
        zIndex={zIndex}
      />
    </SnackbarProvider>,
  );
};

export const Modal = ModalComponent;

export { MUIModal, ModalClose, ModalDialog };
