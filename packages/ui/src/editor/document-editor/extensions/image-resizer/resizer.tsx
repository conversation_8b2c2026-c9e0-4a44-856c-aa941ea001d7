import { NodeViewWrapper } from '@tiptap/react';
import classNames from 'classnames';
import { omit } from 'lodash';
import type React from 'react';
import TextLeftFilled from '@bika/ui/icons/doc_hide_components/text_left_filled';
import TextMiddleFilled from '@bika/ui/icons/doc_hide_components/text_middle_filled';
import TextRightFilled from '@bika/ui/icons/doc_hide_components/text_right_filled';
import { triggerPreviewAttachment } from '@bika/ui/preview-attachment/preivew-attachment';

enum Direction {
  TopLeft = 0,
  TopRight = 1,
  BottomLeft = 2,
  BottomRight = 3,
}

export const Resizer = (props: any) => {
  const handler = (direction: Direction) => (mouseDownEvent: React.MouseEvent<HTMLImageElement>) => {
    const proseMirrorWidth = document.querySelector('.ProseMirror')?.clientWidth ?? 0;
    const parent = (mouseDownEvent.target as HTMLElement).closest('.image-resizer');
    const image = parent?.querySelector('img.primaryImage') ?? null;
    if (image === null) return;
    const startSize = { x: image.clientWidth, y: image.clientHeight };
    const startPosition = { x: mouseDownEvent.pageX, y: mouseDownEvent.pageY };

    function onMouseMove(mouseMoveEvent: MouseEvent) {
      let width = startSize.x;
      let height = startSize.y;
      const xOffset = startPosition.x - mouseMoveEvent.pageX;
      const yOffset = startPosition.y - mouseMoveEvent.pageY;
      switch (direction) {
        case Direction.TopLeft:
          if (startSize.x + xOffset > 0) {
            width += xOffset;
          }
          if (startSize.y + yOffset > 0) {
            height += yOffset;
          }
          break;
        case Direction.TopRight:
          if (startSize.x - xOffset > 0) {
            width -= xOffset;
          }
          if (startSize.y + yOffset > 0) {
            height += yOffset;
          }
          break;
        case Direction.BottomLeft:
          if (startSize.x + xOffset > 0) {
            width += xOffset;
          }
          if (startSize.y - yOffset > 0) {
            height -= yOffset;
          }
          break;
        case Direction.BottomRight:
          if (startSize.x - xOffset > 0) {
            width -= xOffset;
          }
          if (startSize.y - yOffset > 0) {
            height -= yOffset;
          }
          break;
        default:
      }
      width = Math.min(width, proseMirrorWidth);
      props.updateAttributes({ width, height });
    }
    function onMouseUp() {
      document.body.removeEventListener('mousemove', onMouseMove);
    }

    document.body.addEventListener('mousemove', onMouseMove);
    document.body.addEventListener('mouseup', onMouseUp, { once: true });
  };

  const align = props.node.attrs.textAlign;
  const style: React.CSSProperties = {};
  if (align === 'center') {
    style.margin = '0 auto';
  } else if (align === 'right') {
    style.margin = '0 0 0 auto';
  }

  return (
    <NodeViewWrapper
      className={classNames('image-resizer', {
        'image-selected': props.editor.isEditable ? props.selected : false,
      })}
      style={style}
    >
      <img
        {...omit(props.node.attrs, ['alt', 'textAlign'])}
        className="primaryImage"
        alt={props.node.attrs.alt || ''}
        onDoubleClick={() => {
          triggerPreviewAttachment({
            index: 0,
            attachments: [
              {
                name: props.node.attrs.alt || `image-${Date.now()}.png`,
                contentType: 'image/*',
                url: props.node.attrs.src,
                variant: 'kkfile' as const,
              },
            ],
            t: props.editor.t,
          });
        }}
        onClick={() => {
          if (props.editor.isEditable && props.selected) {
            // expandWorkdocImage({
            //   file: {
            //     name: props.node.attrs.alt || `image-${Date.now()}.png`,
            //     token: props.node.attrs.src.replace(/\?.*$/, '') || props.node.attrs.token,
            //     width: props.node.attrs.width,
            //     height: props.node.attrs.height,
            //     mimeType: 'image/*',
            //   },
            //   onDelete: () => {
            //     props.editor.chain().focus().deleteSelection().run();
            //   },
            // });
          }
        }}
      />
      {props.selected && <div id="workdocImage" />}
      <div className="resize-trigger">
        <div className="resize-trigger-top-left" onMouseDown={handler(Direction.TopLeft)} />
        <div className="resize-trigger-top-right" onMouseDown={handler(Direction.TopRight)} />
        <div className="resize-trigger-bottom-left" onMouseDown={handler(Direction.BottomLeft)} />
        <div className="resize-trigger-bottom-right" onMouseDown={handler(Direction.BottomRight)} />
      </div>
      <div className="image-align">
        {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
        <div
          className={props.editor.isActive({ textAlign: 'left' }) ? 'active' : ''}
          onClick={() => {
            props.editor.chain().focus().setTextAlign('left').run();
          }}
        >
          <TextLeftFilled size={16} />
        </div>
        {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
        <div
          className={props.editor.isActive({ textAlign: 'center' }) ? 'active' : ''}
          onClick={() => {
            props.editor.chain().focus().setTextAlign('center').run();
          }}
        >
          <TextMiddleFilled size={16} />
        </div>
        {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
        <div
          className={props.editor.isActive({ textAlign: 'right' }) ? 'active' : ''}
          onClick={() => {
            props.editor.chain().focus().setTextAlign('right').run();
          }}
        >
          <TextRightFilled size={16} />
        </div>
      </div>
    </NodeViewWrapper>
  );
};
