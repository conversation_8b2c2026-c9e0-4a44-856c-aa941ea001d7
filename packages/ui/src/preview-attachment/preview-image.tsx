// import { cellValueToImageSrc, isWebp } from '@apitable/core';
import accept from 'attr-accept';
import { useRef } from 'react';
import * as React from 'react';
import { cn } from '@bika/ui/utils';
import { MIN_SCALE } from './const';
import { usePreviewImageContext } from './context/preview-image-context';
import { useEvents } from './hooks/use-events';
// import { IAttachmentPreviewProps } from './interface';
import { AttachmentVOExtends } from './interface';
import { getAttachmentDisplayPath } from './utils/get-attachment-display-path';
import { stopPropagation, isSupportImage } from './utils/index';

export function isWebp(file: { name: string; type: string }) {
  return accept(file, 'image/webp');
}

// TODO 重构 Merge AttachmentVOExtends | ISimpleAttachment
export interface ISimpleAttachment {
  name?: string;
  contentType?: string;
  url: string;
  variant: 'kkfile';
}

export interface IAttachmentPreviewProps {
  file: ISimpleAttachment;
}

export const PreviewImage: React.FC<React.PropsWithChildren<IAttachmentPreviewProps>> = (props) => {
  const { file } = props;
  const { transformInfo, setTransformInfo } = usePreviewImageContext();
  const { rotate, scale, translatePosition } = transformInfo;

  const imgRef = useRef<HTMLImageElement>(null);
  const wrapperRef = useRef<HTMLImageElement>(null);

  const handleImageLoad = (event: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const target = event.target as HTMLImageElement;
    const { naturalWidth: width, naturalHeight: height } = target;

    const { offsetWidth, offsetHeight } = wrapperRef.current as HTMLDivElement;

    let initActualScale = 1;
    let newScale = 1;

    let newTranslatePosition = {
      x: 0,
      y: 0,
    };

    if (width >= offsetWidth) {
      initActualScale = offsetWidth / width;
    }

    if (height >= offsetHeight) {
      initActualScale = offsetHeight / height;
    }

    // Initial rendering scaling exceeds lower limit
    if (initActualScale < MIN_SCALE) {
      if (width > height) {
        newScale = MIN_SCALE / initActualScale;
      } else if (target.width < 520) {
        newScale = Math.min(520, offsetWidth) / target.width;
        const offset = (newScale * target.height - target.height) / 2;
        newTranslatePosition = {
          x: 0,
          y: offset,
        };
      } else {
        newScale = MIN_SCALE / initActualScale;
      }
    }

    setTransformInfo({
      ...transformInfo,
      initActualScale,
      scale: newScale,
      translatePosition: newTranslatePosition,
    });
  };

  const isRotated = rotate % 180 !== 0;

  const { overflow } = useEvents({
    scale,
    isRotated,
    imageEle: imgRef.current!,
    wrapperEle: wrapperRef.current!,
    transformInfo,
    setTransformInfo,
  });

  if (!file || !isSupportImage(file.contentType ?? '')) {
    return null;
  }

  const src = getAttachmentDisplayPath(
    {
      id: '',
      name: file.name ?? '',
      mimeType: file.contentType ?? '',
      path: '',
      bucket: '',
      size: 0,
      url: file.url,
    } as AttachmentVOExtends,
    false,
  );

  return (
    <div
      ref={wrapperRef}
      className={'flex items-center justify-center w-full h-full ransition-transform duration-300 ease-in-out'}
      style={{
        transform: `translate3d(${translatePosition.x}px, ${translatePosition.y}px, 0)`,
      }}
      draggable={false}
    >
      <img
        onMouseDown={stopPropagation}
        onLoad={handleImageLoad}
        ref={imgRef}
        src={src as string}
        alt={file?.name || ''}
        draggable={false}
        className={cn(
          'max-w-full max-h-full object-contain transition transform duration-300 ease-in-out',
          overflow && 'cursor-grab active:cursor-grabbing',
        )}
        style={{
          transform: `rotate(${rotate}deg) scale3d(${scale}, ${scale}, 1)`,
        }}
        onClick={(e) => e.stopPropagation()}
      />
    </div>
  );
};
