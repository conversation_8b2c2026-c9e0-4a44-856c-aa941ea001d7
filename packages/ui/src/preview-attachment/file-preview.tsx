import React from 'react';
import { Skeleton } from '@bika/ui/skeleton';
import { useFilePreview } from './hooks/use-file-preview';

interface FilePreviewProps {
  url: string;
  fileName?: string;
  className?: string;
  onClose?: () => void;
}

export const FilePreview: React.FC<FilePreviewProps> = ({ url, fileName, onClose, className = 'w-full h-full' }) => {
  const { buildPreviewUrl } = useFilePreview();
  const [isLoading, setIsLoading] = React.useState(true);
  const [hasError, setHasError] = React.useState(false);

  const previewUrl = buildPreviewUrl(url);

  // Determine if we should use iframe or direct display based on content type

  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  const handleIframeError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  if (hasError) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-50">
        <div className="text-center">
          <div className="text-gray-500 text-lg mb-2">Unable to preview file</div>
          <div className="text-gray-400 text-sm">{fileName || 'Unknown file'}</div>
          <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="mt-4 inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Download File
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="relative h-full w-full" onClick={onClose}>
      {isLoading && <Skeleton pos="ANY" />}
      <iframe
        src={previewUrl}
        title="File Preview"
        className={className}
        sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        style={{ display: isLoading ? 'none' : 'block' }}
      />
    </div>
  );
};
