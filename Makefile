.DEFAULT_GOAL := help
SHELL := /bin/bash
UNAME_S := $(shell uname -s)
SEMVER3 := $(shell grep -o '"version": *"[^"]*"' apps/web/package.json | cut -d '"' -f 4)
define ANNOUNCE_BODY

 ______    _   __
|_   _ \  (_) [  |  _
  | |_) | __   | | / ]  ,--.
  |  __'.[  |  | '' <  `'_\ :
 _| |__) || |  | |`\ \ // | |,
|_______/[___][__|  \_]\'-;__/

Bika.ai Makefile $(SEMVER3)
================================================================

endef
export ANNOUNCE_BODY

ENV_FILE := $(shell pwd)/apps/web/.env.local
export ENV_FILE

_check_env: ## check if .env files exists
# check if env file exists
	@FILE=$$ENV_FILE ;\
	if [ ! -f "$$FILE" ]; then \
			echo "$$FILE does not exist. Please 'make init' first" ;\
			exit 1 ;\
	fi

init: env-ci ## init .env files and stuffs
	@echo '初始化 .env 文件，使用dev私有云数据库'

env-dev: ## copy .env.ci to .env.local (暂时不使用有云服务器数据库，都用 local dbs) 
	cp apps/web/.env.ci apps/web/.env.local
	cp apps/toolsdk.ai/.env.ci apps/toolsdk.ai/.env.local

env-ci: ## copy .env.ci to .env.local (CI/CD 纯local服务器)
	cp apps/web/.env.ci apps/web/.env.local
	cp apps/toolsdk.ai/.env.ci apps/toolsdk.ai/.env.local

env-develop: ## copy .env.develop to .env.local (CI/CD 纯develop构建配置)
	cp apps/web/.env.develop apps/web/.env.local
	cp apps/toolsdk.ai/.env.develop apps/toolsdk.ai/.env.local

env-staging: ## copy .env.staging to .env.local (CI/CD 纯staging构建配置)
	cp apps/web/.env.staging apps/web/.env.local

env-prod: ## copy .env.prod to .env.local (CI/CD 纯production构建配置)
	cp apps/web/.env.prod  apps/web/.env.local
	cp apps/toolsdk.ai/.env.prod apps/toolsdk.ai/.env.local

env-selfhost: ## copy .env.selfhost to .env.local (CI/CD 纯selfhost构建配置)
	cp apps/web/.env.selfhost apps/web/.env.local

install:
	export PUPPETEER_SKIP_DOWNLOAD=true && export PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true && pnpm install

install-ci:
	export PUPPETEER_SKIP_DOWNLOAD=true && export PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true && pnpm install --frozen-lockfile


link-next-public: ## 自动将web工程的public，ln到@bika/editor等其它next工程
ifeq ($(wildcard apps/ui-storybook/public),)
	ln -s $$PWD/apps/web/public $$PWD/apps/ui-storybook/public
endif
ifeq ($(wildcard apps/desktop/public),)
	ln -s $$PWD/apps/web/public $$PWD/apps/desktop/public
endif
ifeq ($(wildcard apps/toolsdk.ai/public),)
	ln -s $$PWD/apps/web/public $$PWD/apps/toolsdk.ai/public
endif

dev-desktop: link-next-public  ## start tauri desktop server (@bika/desktop)
	cd apps/desktop && pnpm run run

dev:  ## start servers for dev (@bika/web + doc-server-websocket)
	pnpm run dev

dev-toolsdk: link-next-public
	cd apps/toolsdk.ai && pnpm run dev

dev-web: ## start web server (@bika/web)
	cd apps/web && pnpm run dev

dev-turbo: ## start web server (@bika/web)
	cd apps/web && pnpm run dev:turbo

dev-vite: dev-editor-vite
	@echo 'Editor vite starting...'

dev-editor: dev-editor-vite
	@echo 'Editor vite starting...'

dev-cron: ## 模拟定时自动计划cron函数们，消费automation和olap数据迁移
	curl http://localhost:3000/api/cron/sync

# Tauri要求next.config.js中的output必须是'export'，而不是'standalone'
# _edit_next_config_export:
# ifeq ($(UNAME_S),Darwin)
# 	cd apps/web && \
# 	sed -i "s/'standalone'/'export'/g" next.config.js
# else
# 	cd apps/web && \
# 	sed -i '' "s/'standalone'/'export'/g" next.config.js
# endif

# _edit_next_config_standalone:
# ifeq ($(UNAME_S),Darwin)
# 	cd apps/web && \
# 	sed -i '' "s/'export'/'standalone'/g" next.config.js
# else
# 	cd apps/web && \
# 	sed -i "s/'export'/'standalone'/g" next.config.js
# endif

storybook: link-next-public ## dev storybook
	cd apps/ui-storybook && pnpm run dev

dev-server: ## run api server (@bika/server), with nodejs
	cd apps/server && pnpm run dev
dev-server-bun: ## run api server (@bika/server), with bun
	cd apps/server && pnpm run dev:bun
dev-server-doc:
	cd apps/server && pnpm run dev:doc

dev-edge: ## run edge functions locally (dev)
	cd apps/edge && pnpm run start

test-bika: ## test all bika packages (except toolsdk, since the standalone Prisma schema migration)
	cd packages/bika-server-orm && pnpm run db:gen
	pnpm run test:bika

test-toolsdk: ## test all toolsdk packages
	cd packages/@toolsdk.ai/orm && pnpm run db:gen
	cd packages/@toolsdk.ai/domains && pnpm run test

test-ab: ## stress test http localhost:3000
	pnpm run test:autocannon

test-postman: ## test postman
	newman run scripts/autotest/postman/api-test.collection.json -e scripts/autotest/postman/bika-dev.environment.json

colima: ## Run Colima Docker Container with File System Privileges
	colima start --vm-type=vz --mount-type=virtiofs

test-puppeteer: ## test postman
	cd ./scripts/autotest && npx tsx run-puppeteer.ts

test-domains: ## test domains
	cd domains && pnpm run test

test-ui: ## test ui
	cd packages/ui && pnpm run test

# color
RED := \033[31m
GREEN := \033[32m
YELLOW := \033[33m
BLUE := \033[34m
RESET := \033[0m

ai-benchmark: ##  gaia benchmark test with bun runtime
	@printf '$(GREEN)✅ Welcome to gaia benchmark test!$(RESET)\n'
	@printf '$(YELLOW)📋 Please select your options:$(RESET)\n'
	@printf '\n'
	@bash -c '\
	read -p "Enter AI model (default: gpt-4.1): " MODEL; \
	MODEL=$${MODEL:-gpt-4.1}; \
	read -p "Enter test level (1, 2, 3, or all, default: 1): " LEVEL; \
	LEVEL=$${LEVEL:-1}; \
	read -p "Are you sure to continue? (Y/n): " ANSWER; \
	ANSWER=$${ANSWER:-yes}; \
	if [ "$$ANSWER" = "yes" ] || [ "$$ANSWER" = "Y" ] || [ "$$ANSWER" = "y" ]; then \
		printf "🚀 Starting Gaia benchmark test with model: %s, level: %s...\n" "$$MODEL" "$$LEVEL"; \
		printf "⚡ Using bun runtime for faster execution...\n"; \
		if ! command -v bun &> /dev/null; then \
			printf "$(RED)❌ Error: bun is not installed. Please install bun first: https://bun.sh/$(RESET)\n"; \
			exit 1; \
		fi; \
		cd scripts/ai-benchmark && npx dotenv -e ../../apps/web/.env.local -- bun run gaia-benchmark.ts --model="$$MODEL" --level="$$LEVEL"; \
	else \
		printf "❌ Test cancelled by user\n"; \
	fi'



build: ## build all packages
	pnpm run build

build-export: ## build next js with output: 'export'
	cd apps/web && BUILD_EXPORT=true npx next build

build-storybook:
	cd apps/ui-storybook && NODE_OPTIONS=--max-old-space-size=8192 pnpm run build

build-server: ## Build API Server
	cd apps/server && pnpm run build

build-ios:
	cd apps/mobile && pnpm install && pnpm build:production:local:ios:non-interaction

build-web:
	cd packages/bika-server-orm && pnpm run db:gen
	cd apps/web && pnpm build
	cd apps/web && pnpm build:rsc

build-desktop:
	cd apps/desktop && pnpm build

build-toolsdk:
	cd packages/@toolsdk.ai/orm && pnpm run db:gen
	cd apps/toolsdk.ai && pnpm build

build-toolsdk-sdk-ts: ## Build toolsdk sdk-ts package for npm publishing
	cd projects/toolsdk/packages/sdk-ts && pnpm build

build-init-container: ## bika-init container for db:seed and db:migrate
	docker build -f scripts/Dockerfile.init -t bika-init:latest .

RELEASE_DATA := $(shell date -u +'%Y-%m-%dT%H:%M:%SZ')
build-docker-bika:
	docker build --progress=plain -f scripts/Dockerfile.bika -t bika:latest --build-arg APP_BUILD_DATE=$(APP_BUILD_DATE) .

# build-docker-web:
# 	docker build --progress=plain -f scripts/Dockerfile.web -t bika:latest .

build-docker-toolsdk:
	docker build --progress=plain -f scripts/Dockerfile.toolsdk -t toolsdk:latest .

build-docker-server-doc:
	docker build --progress=plain -f scripts/Dockerfile.server-doc -t bika-server-doc:latest .

build-docker-server-bun: build-server
	docker build --progress=plain -f scripts/Dockerfile.server-bun -t bika-server-bun:latest .

build-docker-server-bun-exe: build-server
	docker build --progress=plain -f scripts/Dockerfile.server-bun-exe -t bika-server-bun:latest .

build-docker-server-node: build-server
	docker build --progress=plain -f scripts/Dockerfile.server-node -t bika-server-node:latest .

start: ## NextJS production env
	cd apps/web && pnpm run start

db: _check_env ## start postgresql and mongodb local
	mkdir -p .data/elasticsearch
	docker-compose --profile oltp up -d --wait --wait-timeout 60


db-stop: ## stop postgresql and mongodb local
	docker-compose --profile oltp stop

db-kill: ## kill and remove postgresql and mongodb local
	docker-compose --profile oltp down --volumes

db-kill-all: ## kill and remove all oltp + olap containers
	docker-compose down --volumes

db-gen: ## generate db schema
	cd packages/bika-server-orm && pnpm run db:gen
	cd packages/@toolsdk.ai/orm && pnpm run db:gen

db-migrate: ## migrate db schema
	cd packages/bika-server-orm && pnpm run db:migrate
	cd packages/@toolsdk.ai/orm && pnpm run db:migrate

db-seed: ## db seed data
	cd packages/bika-server-orm && pnpm run db:seed
	cd packages/@toolsdk.ai/orm && pnpm run db:seed

db-reset: ## db reset = clean + migrate + seed (DANGER!)
	cd packages/bika-server-orm && pnpm run db:reset
	cd packages/@toolsdk.ai/orm && pnpm run db:reset

db-reset-force:
	cd packages/bika-server-orm && pnpm run db:reset:force
	cd packages/@toolsdk.ai/orm && pnpm run db:reset:force

db-olap: ## start clickhouse and elastic search local
	docker-compose --profile olap up -d --wait --wait-timeout 60

db-olap-stop: ## stop clickhosue and elastic searchlocal
	docker-compose --profile olap stop

db-olap-kill: ## kill and remove clickhosue and elastic search local
	docker-compose  --profile olap down --volumes


.PHONY: projects
projects:
	git subtree split -P projects/bika -b projects/bika
# git filter-repo --subdirectory-filter projects/bika-sdk


subtree-init: #  初始化所有 subtree
	git subtree add --prefix=projects/bika **************:bika-ai/bika-mcp-server.git main --squash

subtree-pull: # 更新所有 subtree
	git subtree pull --prefix=projects/bika **************:bika-ai/bika-mcp-server.git main

subtree-push: # 更新所有 subtree 到 develop 分支
	git subtree push --prefix=projects/bika **************:bika-ai/bika-mcp-server.git main

subtree-merge: # 更新所有 subtree 到 develop 分支
	git subtree merge --squash --prefix=projects/bika **************:bika-ai/bika-mcp-server.git main

fonts:
	@echo '在Linux下安装所需字体，为AIGC准备'
	wget https://puhuiti.oss-cn-hangzhou.aliyuncs.com/AlibabaPuHuiTi-3.zip -P /tmp
	cd /tmp && unzip AlibabaPuHuiTi-3.zip && cd AlibabaPuHuiTi-3 && find . -type f -name "*.ttf" | xargs sudo cp -t /usr/share/fonts/truetype/



airbyte-install: ## install airbyte on your local docker machine
	@echo 'Default username airbyte, default password password'
ifeq ($(wildcard airbyte),)
	git clone --depth=1 https://github.com/airbytehq/airbyte.git
else
	cd airbyte && git pull
endif
# download docker-compose.yaml only
	cd airbyte && bash run-ab-platform.sh -d

zapier-link:
	cd packages/@bika.ai/bika-zapier && zapier link

zapier-test:
	cd packages/@bika.ai/bika-zapier && zapier test

zapier-push:
	cd packages/@bika.ai/bika-zapier && zapier push

airbyte-start: ## start airbyte on your local docker machine
	cd airbyte && docker-compose up -d

airbyte-stop: ## start airbyte on your local docker machine
	cd airbyte && docker-compose down

airbyte-migrate: ## create connectors and airbyte to your local airbyte with terraform
	cd scripts/airbyte && terraform init && terraform apply

## 现在不需要预设数据了, 只要配置上Stripe API调用密钥, 程序自动创建
stripe:
	@echo '该命令会改变SKU, 通常只能让Kelly执行哦~'
	cd packages/bika-server-orm && pnpm run stripe

stripe-listen: ## 本地测试stripe webhook
	stripe listen --forward-to localhost:3000/api/stripe/webhooks

template-release:
	cd scripts/template-release && bun ./template-release.ts

aigc: template-aigc
	@echo 'Welcome to Bika AIGC'

template-aigc:
	@cd scripts/template-release && npx dotenv -e ../../apps/web/.env.local -- bun ./template-aigc/main.ts

AIGC_REPO= bika-content
AIGC_BLOG_DIR=contents/docs/blog-aigc
# 检查仓库下载了没，独立的bika-aigc内容生成库
template-aigc-clone:
ifeq ($(wildcard $(AIGC_REPO)),)
	@echo "$(AIGC_REPO) 目录不存在，开始clone： https://github.com/vikadata/bika-content.git"
	@echo "bika-content目录，是用于存放动态AIGC生成内容，避免主工程过于臃肿，并通过seed函数塞到数据库"
	@echo "静态AI内容,静态图片等，放在bika主工程"
	@echo "如果克隆失败，请去https://github.com/vikadata/bika-content ，手工克隆到根目录bika-content"
	@echo "----------------------------------------------------------------------------"
	git clone https://github.com/vikadata/bika-content.git
else
	@echo "$(AIGC_REPO) 目录存在，正确"
endif
# ifeq ($(wildcard $(AIGC_BLOG_DIR)),)
# 	@echo "$(AIGC_BLOG_DIR) 目录不存在，软链接它"
# 	ln -s $$PWD/$(AIGC_REPO)/blog-aigc $$PWD/$(AIGC_BLOG_DIR)
# else
# 	@echo "$(AIGC_BLOG_DIR) 目录存在，正确"
# endif

# git pull --force
# git push --force

buildpush-self-hosted: ## build and push self-hosted packages
	cd scripts/self-hosted && bash build.sh $${MODE:-docker}

app-icons: ## 生成各个尺寸的app图标（基于app-icon.png)
	cd apps/web/src-tauri && npx tauri icon

sync-icons: ## sync icons
	cd scripts && python3 sync-icons.py


sync-colors: ## design tokens
	python3 ./scripts/sync_colors.py

clean-git: ## git clean all, includes node_modules and stuffs
	@read -p "Are you sure to clean and delete? (Y/n) " ANSWER;\
	ANSWER=$${ANSWER:-yes};\
	if [ "$$ANSWER" = "yes" ] || [ "$$ANSWER" = "Y" ]; then git clean -fxd; fi

clean: ## delete .next cache
	rm -rf apps/web/.next

check: ## depcheck & syncpack check
	npx syncpack list
	npx depcheck

lint: ## Lint all TypeScript
	pnpm run lint



CUR_BRANCH="$(shell git rev-parse --abbrev-ref HEAD)"

release: ## deploy staging environment, only in develop or release/** branch
	@echo '😃 Hi! Hi! 当前是$(CUR_BRANCH)分支'
	@sleep 1
	@echo '😄 现在开始发版流程!!!'
	@sleep 1
	@echo '🥹 首先，我们要创建release分支，我会先确认版本号，请你确认一下：'
	@sleep 1
ifeq ($(CUR_BRANCH),"develop")
# 是develop分支，沿用版本号，alpha改beta
	@echo '🥹 你目前在$(CUR_BRANCH)分支，我会直接使用你的三位版本号做beta'
	@npx lerna version prerelease --preid beta --no-git-tag-version
	@sleep 1
	@make _release_create_branch
# 是release 分支,沿用版本后, beta改release
else ifeq ($(findstring release, $(CUR_BRANCH)), release)
	@echo '🥹 你目前在$(CUR_BRANCH)分支，我会直接使用你的三位版本号做release'
	@npx lerna version prerelease --preid release --no-git-tag-version
	@sleep 1
	@make _release_branch_to_main
else
# 不是develop分支，可理解为想发patch版本，即改第三位版本号
	@echo '🥹 你目前在$(CUR_BRANCH)分支，我会帮你把patch版本号(第三位)提升下'
	@sleep 1
	@npx lerna version prepatch --preid beta --no-git-tag-version
	@sleep 1
	@make _release_create_branch
endif


NEW_VERSION=$(shell node -pe "require('./apps/web/package.json').version.split('-')[0]")
_release_create_branch:
	@echo '😃 好了，现在我要创建一个分支 release/$(NEW_VERSION)：'
	@sleep 1
	git checkout -b release/$(NEW_VERSION)
	@sleep 1
	@echo '😄 提交版本号变更情况!...$(NEW_VERSION)'
	git commit -a -m "release: $(NEW_VERSION)"
	@sleep 1
	@echo '😃 我现在要push这个分支上去了!......$(NEW_VERSION)'
	git push --set-upstream origin release/$(NEW_VERSION)
	@sleep 1
	@echo '🌟 分支release/$(NEW_VERSION) 传上去了，已经开始开始部署staging环境了！'
	@sleep 1
	@echo '.........................'
	@echo '.........................'
	git checkout -b develop-pr/release/$(NEW_VERSION)
	@echo '😃 由于新建了release分支，你develop分支也要进行版本号提升了，我会创建一个临时分支，提升你的develop版本号'
	@sleep 1
	@echo '😃 你要提升大版本or小版本？直接回车默认patch。 '
	@read -p "😃 输入minor或patch: " BUMP;\
	if [ "$$BUMP" = "minor" ]; then npx lerna version preminor --preid alpha --no-git-tag-version --yes ; \
	elif [ "$$BUMP" = "patch" ]; then npx lerna version prepatch --preid alpha --no-git-tag-version --yes ; \
	else npx lerna version prepatch --preid alpha --no-git-tag-version --yes ; fi ;
	@echo '😃 我现在要push这个新版本号变化上去了，会有个新的PR给你!......'
	git commit -a -m "release: $(shell node -pe "require('./apps/web/package.json').version.split('-')[0]")"
	git push --set-upstream origin develop-pr/release/$(NEW_VERSION)
	@echo '😃 一切准备就绪了，我会回到新的release分支'
	git switch release/$(NEW_VERSION)
	@sleep 1
	@echo '😃 我把Pull Request页面打开，你看看有什么新PR处理'
	@sleep 1
	@open https://github.com/vikadata/bika/pulls

# @echo '接下来，你要记得自己创建一个PR，把新创建的release分支合回去develop哦'
# @sleep 1
# @open https://github.com/vikadata/bika/compare/develop...release/$(NEW_VERSION)

# _release_up_develop:


# @echo '你想发大版本？还是小版本？大版本输入minor，小版本输入patch:'
# @read -p "What do you want?>> " command; \
# if [ "$$command" = "patch" ]; then \
# 	echo '你选择了小版本'; \
# fi; \
# if [ "$$command" = "minor" ]; then \
# 	echo '你选择了大版本'; \
# fi;

# npx lerna version prerelease --preid beta --no-git-tag-version

# npx lerna version minor --no-git-tag-version
# npx lerna version patch  --no-git-tag-version


_release_branch_to_main:
	@echo '😃 好了，现在我要创建一个分支 pr-main/$(CUR_BRANCH)：'
	@sleep 1
	git checkout -b pr-main/$(CUR_BRANCH)
	@sleep 1
	@echo '😄 提交版本号变更情况!...$(CUR_BRANCH)'
	git commit -a -m "release: $(CUR_BRANCH) to main"
	@sleep 1
	@echo '😃 我现在要push这个分支上去了!......pr-main/$(CUR_BRANCH)'
	git push --set-upstream origin pr-main/$(CUR_BRANCH)
	@sleep 5
	@echo '😃 我把Pull Request页面打开，你看看要建什么新PR，注意！！设置合并到main！'
	@open https://github.com/vikadata/bika/pulls


tidy-i18n: ## tidy i18n
	bun ./scripts/tidy-i18n.ts

pdf-openapi-gen: ## Generate Bika OpenAPI PDF file
	@echo '生成Bika OpenAPI的PDF文档'
	ravagepdf -s https://bika.ai/api/openapi/bika/openapi.json -o bika-openapi.pdf

bench:
	cd domains && pnpm run benchmark

version: ## bump version number
	npx lerna version --no-git-tag-version

_bump-ios-build-number:
	cd apps/mobile && jq '.expo.ios.buildNumber |= ((tonumber + 1) | tostring)' app.json > updated_app.json && mv updated_app.json app.json

count: ## count lines of code
	npx cloc --include-lang=TypeScript --exclude-dir=node_modules,docs,.next .

# deploy: ## deploy to bika-dev.vercel.app, preview environment
# 	@npx vercel

deploy-edge: ## deploy edge server to bika-edge.vercel.app (dev)
	cd apps/edge && npm run deploy

venv-toolsdk: ## create python virtual environment for toolsdk
	python3 -m venv .venv
	source .venv/bin/activate
	pip3 install --no-cache-dir -r apps/edge/requirements.txt

cookbook-init: ## init cookbook cargos
	cargo install mdbook mdbook-pdf

cookbook-dev:
ifeq ($(wildcard scripts/cookbook-pdf/src/help),)
	ln -s $$PWD/contents/docs/help $$PWD/scripts/cookbook-pdf/src/help
endif
	cd scripts/cookbook-pdf && mdbook serve

cookbook-build: ## build cookbook pdf + html
	cd scripts/cookbook-pdf && mdbook build

### help
.PHONY: search
search:
	@echo " "
	echo  && grep -E "$$s.*?## .*" $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}';
	@echo ' ';
	@read -p "What do you want?>>" command; \
	make $$command;

opensource-bika: ## 开源Bika项目
	@echo 'Here start to open source Bika project... I will checkout bika current open source project, create a new branch, and rsync to'
	@if [ -d ".data/bika" ]; then \
		echo "Removing existing .data/bika directory..."; \
		rm -rf .data/bika; \
	fi
# <NAME_EMAIL>:bika-ai/bika.ai.git .data/bika
	mkdir .data/bika
# 	rsync，将 projects/bika 整个目录，放到这个 新建的bika 目录里
	rsync -av --exclude='node_modules' projects/bika/ .data/bika/
	rsync -av --exclude='node_modules' projects/basenext/ .data/bika/packages/basenext/
	rsync -av --exclude='node_modules' projects/toolsdk/packages/sdk-ts/ .data/bika/packages/toolsdk/
# 将.data/bika里的 _package.json 改名 pacakge.json，将_pnpm-workspace.yaml 改名为 pnpm-workspace.yaml
	@if [ -f ".data/bika/_package.json" ]; then \
		mv .data/bika/_package.json .data/bika/package.json; \
	fi
	@if [ -f ".data/bika/_pnpm-workspace.yaml" ]; then \
		mv .data/bika/_pnpm-workspace.yaml .data/bika/pnpm-workspace.yaml; \
	fi
	cd .data/bika && pnpm install

opensource-toolsdk: ## 开源ToolSDK项目
	@echo 'Here start to open source ToolSDK project... I will checkout bika current open source project, create a new branch, and rsync to'
	@if [ -d ".data/toolsdk" ]; then \
		echo "Removing existing .data/toolsdk directory..."; \
		rm -rf .data/toolsdk; \
	fi
# <NAME_EMAIL>:bika-ai/bika.ai.git .data/bika
	mkdir .data/toolsdk
# 	rsync，将 projects/bika 整个目录，放到这个 新建的bika 目录里
	rsync -av --exclude='node_modules' projects/toolsdk/ .data/toolsdk/
	rsync -av --exclude='node_modules' projects/basenext/ .data/toolsdk/packages/basenext/
# 将.data/toolsdk里的 _package.json 改名 pacakge.json，将_pnpm-workspace.yaml 改名为 pnpm-workspace.yaml
	@if [ -f ".data/toolsdk/_package.json" ]; then \
		mv .data/toolsdk/_package.json .data/toolsdk/package.json; \
	fi
	@if [ -f ".data/toolsdk/_pnpm-workspace.yaml" ]; then \
		mv .data/toolsdk/_pnpm-workspace.yaml .data/toolsdk/pnpm-workspace.yaml; \
	fi
	cd .data/toolsdk && pnpm install

opensource-basenext: ## 开源basenext项目
	@echo 'Here start to open source basenext project... I will checkout bika current open source project, create a new branch, and rsync to'
	@if [ -d ".data/basenext" ]; then \
		echo "Removing existing .data/basenext directory..."; \
		rm -rf .data/basenext; \
	fi

	<NAME_EMAIL>:toolsdk-ai/basenext.git .data/basenext
# 	mkdir .data/basenext
# 	rsync，将 projects/bika 整个目录，放到这个 新建的bika 目录里
	rsync -av --exclude='node_modules' projects/basenext/ .data/basenext/

	@if [ -f ".data/basenext/_pnpm-workspace.yaml" ]; then \
		mv .data/basenext/_pnpm-workspace.yaml .data/basenext/pnpm-workspace.yaml; \
	fi

	cd .data/basenext && pnpm install && git add . && git status 
	@read -p "Enter commit message: " COMMIT_MSG; \
	cd .data/basenext && git commit -m "$$COMMIT_MSG" && git push --set-upstream origin main

.PHONY: help
help:
	@echo "$$ANNOUNCE_BODY"
	@echo ' ';
	@grep -E '^[0-9a-zA-Z-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}';
	@echo '  '
	@read -p "What do you want?>> " command; \
	make $$command;


