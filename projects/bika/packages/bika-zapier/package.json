{"name": "@bika.ai/bika-zapier", "version": "1.9.1-alpha.51", "description": "Bika.ai's Zapier integration", "main": "index.js", "scripts": {"build": "node ./build.mjs", "push": "zapier push", "validate": "npm run build && zapier validate"}, "dependencies": {"axios": "^1.9.0", "zapier-platform-core": "15.18.1"}, "devDependencies": {"@bika/types": "file:../bika-types", "@types/bun": "latest", "@types/jest": "^29.4.0", "@types/node": "^18", "bika.ai": "file:../bika-sdk-js", "jest": "^29.4.3", "nock": "^13.3.0", "prettier": "2.8.4", "rimraf": "^4.1.2", "ts-jest": "^29.0.5", "typescript": "^5.7.2"}, "private": true, "jest": {"verbose": true, "moduleFileExtensions": ["js", "json", "ts"], "rootDir": "./src", "testRegex": [".test.ts$", ".test.js$"], "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "transformIgnorePatterns": ["^.+\\.js$"], "moduleDirectories": ["node_modules", "src"], "testEnvironment": "node"}}