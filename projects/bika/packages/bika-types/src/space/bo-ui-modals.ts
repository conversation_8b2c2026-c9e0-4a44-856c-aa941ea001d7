import { z } from 'zod';
import { SpaceSettingUITabSchema } from './bo-space-setting-tab';
import { AIIntentParamsSchema } from '../ai/bo-intent-params';
import { IntegrationTypeSchema } from '../integration/bo';
import { IntegrationVOSchema } from '../integration/vo';
import { NodeResourceTypeSchema } from '../node/base';
import { NodeMenuVOSchema } from '../node/vo-node';
import { AccessPrivilegeSchema } from '../permission/bo';
import { TemplateMetadataSchema } from '../template/bo-template-base';
// // Lowercase letters are used here because this will be displayed in the url's ?modal, lowercase looks better and matches
// export enum SpaceUIModalType {
//   AI_WIZARD = 'ai-wizard',
//   AI_COMMANDER = 'ai-commander',
//   CREATE_ANYTHING = 'create-anything',
//   MISSION_DETAIL = 'mission',
//   TEMPLATE_DETAIL = 'template', // template detail
//   REPORT_DETAIL = 'report',
//   INVITE_MEMBERS = 'invite-members',
//   SPACE_SETTINGS = 'space-settings',
//   USER_SETTINGS = 'user-settings',
//   NOTIFICATION_CENTER = 'notification-center',
//   TEMPLATE_EDITOR = 'template-editor',
// }

// Lowercase letters are used here because this will be displayed in the url's ?modal, lowercase looks better and matches
// export const SpaceUIModalTypeSchema = z.union([
//   z.literal('ai-wizard'),
//   z.literal('ai-commander'),
//   z.literal('create-anything'),
//   z.literal('mission'),
//   z.literal('template'),
//   z.literal('create-record'), // create record, will close after creation
//   z.literal('record-detail'), // full-featured record details
//   z.literal('request-record-mission'), // request data task
//   z.literal('report'),
//   z.literal('invite-members'),
//   z.literal('space-settings'),
//   z.literal('user-settings'),
//   z.literal('user-email-setting'),
//   z.literal('notification-center'),
//   z.literal('template-editor'),
//   z.literal('pricing'),
// ]);
// export type SpaceUIModalType = z.infer<typeof SpaceUIModalTypeSchema>;

const BaseSpaceUIModalSchema = z.object({
  type: z.string(),
});

export const AIWizardSpaceUIModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('ai-wizard'),
  initIntent: AIIntentParamsSchema.optional(),
  initWizardId: z.string().optional(),
  initUserMessage: z.string().optional(),
  // initIntent?: AIIntentParams, _initWizardId?: string
});

export const AICommanderSpaceUIModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('ai-commander'),
});
export const CreateAnythingModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('create-anything'),
});
export const MissionModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('mission'),
  // if undefined, new mission
  missionId: z.string().optional(),
});

export const TemplateModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('template'),
  templateId: z.string(),
});

export const CreateRecordModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('create-record'),
  databaseId: z.string(),
  viewId: z.string().optional(),
  mirrorId: z.string().optional(),
  disabledBackdropClick: z.boolean(),
});

export const EnterViewModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('enter-view'),
  databaseId: z.string(),
  viewId: z.string(),
});

export const UpdateRecordModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('update-record'),
  databaseId: z.string(),
  viewId: z.string().optional(),
  recordId: z.string(),
});
export const RecordDetailModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('record-detail'),
  databaseId: z.string(),
  recordId: z.string(),
  mirrorId: z.string().optional(),
  viewId: z.string().optional(),
  closeAfterUpdated: z.boolean().optional(),
  commentExpanded: z.string().optional(),
});

export const InviteMembersSpaceUIModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('invite-members'),
  teamId: z.string().optional(),
  roleId: z.string().optional(),
});
export const ReportSpaceUIModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('report'),
  reportId: z.string().optional(),
});

export const RequestRecordMissionSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('request-record-mission'),
  databaseId: z.string(),
  mirrorId: z.string().optional(),
  viewId: z.string().optional(),
});

export const SpaceSettingsUIModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('space-settings'),
  first: z
    .union([z.literal('USER'), z.literal('SPACE')])
    .default('SPACE')
    .optional(),
  tab: SpaceSettingUITabSchema.optional(),
});
export type SpaceSettingsUIModal = z.infer<typeof SpaceSettingsUIModalSchema>;

// export const UserSettingsSpaceUIModalSchema = BaseSpaceUIModalSchema.extend({
//   type: z.literal('user-settings'),
// });
export const NotificationCenterSpaceUIModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('notification-center'),
});

export const TemplateEditorModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('template-editor'),
  editObject: z
    .union([
      z.literal('TEMPLATE_FOLDER'),
      z.literal('NODE'),
      z.literal('FOLDER'),
      z.literal('DATABASE_FIELD'),
      z.literal('DATABASE_VIEW'),
    ])
    .optional(),
  editObjectId: z.string().optional(),
});

export const UserEmailSettingModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('user-email-setting'),
});

export const IntegrationDetailModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('integration-detail'),
  integration: z.object({
    data: IntegrationVOSchema.optional(),
    type: IntegrationTypeSchema,
  }),
});

export const MemberNameSettingModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('member-name-setting'),
  name: z.string().optional(),
});

export const NodeShareAndPermissionModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('node-share-and-permission'),
  nodeId: z.string(),
  option: z
    .object({
      showDetailList: z.boolean().optional(),
    })
    .optional(),
});

export const AIShareModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('ai-share'),
  wizardId: z.string(),
  option: z
    .object({
      shareType: z.enum(['PUBLIC', 'PRIVATE', 'TEAM']).optional(),
      allowComments: z.boolean().optional(),
      expiresAt: z.string().optional(),
    })
    .optional(),
});

export const DashboardWidgetModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('dashboard-widget'),
  widgetId: z.string(),
  dashboardId: z.string(),
  privilege: AccessPrivilegeSchema.optional(),
  isModal: z.boolean().optional(),
});

export const ImportExcelModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('import-excel'),
  // If databaseId exists, it means incremental import, otherwise it is a new import
  databaseId: z.string().optional(),
  folderId: z.string().optional(),
  importEnabled: z.boolean().optional(),
});

export const ImportBikaFileModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('import-bika-file'),
  folderId: z.string().optional(),
  importEnabled: z.boolean().optional(),
});

export const ImportVikaModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('import-vika'),
  folderId: z.string().optional(),
});

export const RequestOperationPermissionModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('request-operation-permission'),
});

export const ExportExcelModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('export-excel'),
  nodeId: z.string().describe('ID of the database to export'),
});

export const ExportAttachmentsModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('export-attachments'),
  nodeId: z.string().describe('ID of the database to export'),
});

export const ExportBikaFileModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('export-bika-file'),
  nodeId: z.string().describe('ID of the Folder to export'),
  exportType: z.enum(['template', 'resource']),
  includeData: z.boolean().optional(),
});

export const AlertReachLimitModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('alert-reach-limit'),
  text: z.string().optional(),
});

export const NodePropertiesModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('node-info'),
  resourceType: NodeResourceTypeSchema,
  nodeId: z.string(),
});

export const DeleteSpaceConfirmModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('delete-space'),
  resourceType: NodeResourceTypeSchema,
  nodeId: z.string(),
});

export const PublishTemplateModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('publish-template'),
  // publish: z.boolean(),
  republish: z.boolean(),
  templateId: z.string().optional(),
  templateMetadata: TemplateMetadataSchema.and(z.object({ version: z.string() })).optional(),
  nodeId: z.string().optional(),
});

// createNode
export const CreateNodeModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('create-node'),
  props: z.object({
    defaultNodeType: z.string().optional(),
    parentId: z.string(),
    isRedirect: z.boolean().optional(),
  }),
});

export const MoveToModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('move-to'),
  node: NodeMenuVOSchema,
});

export const PricingModalSchema = BaseSpaceUIModalSchema.extend({
  type: z.literal('pricing'),
});

export const SpaceUIModalConfigSchema = z.object({
  disabledBackdropClick: z.boolean().optional(),
});

export const AICreditLimitModalSchema = z.object({
  type: z.literal('ai-credit-limit'),
});

export const SpaceUIModalSchema = z
  .discriminatedUnion('type', [
    AIWizardSpaceUIModalSchema,
    AICommanderSpaceUIModalSchema,
    RequestRecordMissionSchema,
    DashboardWidgetModalSchema,
    CreateAnythingModalSchema,
    MissionModalSchema,
    InviteMembersSpaceUIModalSchema,
    RecordDetailModalSchema,
    UpdateRecordModalSchema,
    CreateRecordModalSchema,
    SpaceSettingsUIModalSchema,
    ReportSpaceUIModalSchema,
    EnterViewModalSchema,
    // UserSettingsSpaceUIModalSchema,
    NotificationCenterSpaceUIModalSchema,
    TemplateModalSchema,
    TemplateEditorModalSchema,
    UserEmailSettingModalSchema,
    IntegrationDetailModalSchema,
    MemberNameSettingModalSchema,
    NodeShareAndPermissionModalSchema,
    AIShareModalSchema,
    ImportExcelModalSchema,
    RequestOperationPermissionModalSchema,
    ExportExcelModalSchema,
    ExportAttachmentsModalSchema,
    ImportBikaFileModalSchema,
    AlertReachLimitModalSchema,
    ExportBikaFileModalSchema,
    PublishTemplateModalSchema,
    NodePropertiesModalSchema,
    CreateNodeModalSchema,
    MoveToModalSchema,
    PricingModalSchema,
    ImportVikaModalSchema,
    DeleteSpaceConfirmModalSchema,
    AICreditLimitModalSchema,
  ])
  .and(SpaceUIModalConfigSchema);

export type SpaceUIModalConfig = z.infer<typeof SpaceUIModalConfigSchema>;

export type SpaceUIModal = z.infer<typeof SpaceUIModalSchema>;
export type SpaceUIModalType = SpaceUIModal['type'];
