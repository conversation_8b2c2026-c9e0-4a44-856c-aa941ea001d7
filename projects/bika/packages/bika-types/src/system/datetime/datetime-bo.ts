import { z } from 'zod';

// Examples
// console.log(isValidTimeZone('Asia/Shanghai')); // true
// console.log(isValidTimeZone('Invalid/Timezone')); // false
// console.log(isValidTimeZone('Etc/GMT-8')); // true
// Reference: https://www.ibm.com/docs/en/cloudpakw3700/2.3.0.0?topic=SS6PD2_2.3.0/doc/psapsys_restapi/time_zone_list.htm
export function isValidTimeZone(timeZone: string) {
  try {
    // Internation API
    // eslint-disable-next-line no-new
    new Intl.DateTimeFormat(undefined, { timeZone });
    return true;
  } catch (_) {
    return false;
  }
}
export const IntlTimeZoneSchema = z.custom<string>((val) => isValidTimeZone(val as string));

// Time Formatter
export const TimeFormats = [
  /** 'HH:mm', 00 - 24 hour */
  'HH:mm',
  /** 'hh:mm', 00 - 12 hour */
  'hh:mm',
] as const;
export const TimeFormatSchema = z.enum(TimeFormats);
export type TimeFormat = z.infer<typeof TimeFormatSchema>;
export const TimeFormatOptions = TimeFormatSchema.options;

export const DateFormatSchema = z.enum([
  /** year month day */
  'YYYY/MM/DD',
  /** year month day */
  'YYYY-MM-DD',
  'MM-DD-YYYY',
  /** day/month/year */
  'DD/MM/YYYY',
  'MM/DD/YYYY',
  /** year-month */
  'YYYY-MM',
  /** Month Day */
  'MM-DD',
  /** year */
  'YYYY',
  /** month */
  'MM',
  /** day */
  'DD',
]);
export type DateFormat = z.infer<typeof DateFormatSchema>;
export const DateFormatOptions = DateFormatSchema.options;

export const DateTimeFieldPropertySchema = z.object({
  // specify field default timezone
  // e.g: Asia/Shanghai
  timeZone: z.union([IntlTimeZoneSchema, z.literal('AUTO')]).optional(),
  // date format
  dateFormat: DateFormatSchema,
  /** Time format */
  timeFormat: TimeFormatSchema.optional(),
  /** Whether to include time */
  includeTime: z.boolean().default(false),

  showTimezone: z.boolean().optional(),
  // Whether to automatically fill in the creation time when adding a new record
  autofill: z.boolean().optional(),
  // includeTimeZone?: boolean;
});
export type DateTimeFieldProperty = z.infer<typeof DateTimeFieldPropertySchema>;
