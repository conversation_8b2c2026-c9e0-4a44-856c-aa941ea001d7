import { z } from 'zod';
import {
  ViewFieldSchema,
  DatabaseViewTypeSchema,
  ViewFilterSchema,
  ViewSortArraySchema,
  ViewGroupArraySchema,
  ViewExtraSchema,
} from './bo-view';
import { FieldVOSchema } from './vo-field';
import { RecordPaginationVOSchema } from './vo-record';
import { RenderOptionSchema } from '../system/render-option';

export const ViewFieldVOSchema = FieldVOSchema.and(ViewFieldSchema);

export type ViewFieldVO = z.infer<typeof ViewFieldVOSchema>;

export const ViewPropertySchema = z.object({
  autoSave: z.boolean().optional(),
  hidden: z.boolean().optional(),
  frozenColumnCount: z.number().optional(),
  rowHeightLevel: z.number().optional(),
  autoHeadHeight: z.boolean().optional(),
});

export type ViewProperty = z.infer<typeof ViewPropertySchema>;

export const ViewVOSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  type: DatabaseViewTypeSchema,
  templateId: z.string().optional(),
  databaseId: z.string(),
  preViewId: z.string().nullish(),
  filters: ViewFilterSchema.optional(),
  sorts: ViewSortArraySchema.optional(),
  groups: ViewGroupArraySchema.optional(),
  property: ViewPropertySchema.optional(),
  columns: z.array(ViewFieldVOSchema),
  extra: ViewExtraSchema.optional(),
});
export type ViewVO = z.infer<typeof ViewVOSchema>;

export const ViewSimpleVOSchema = ViewVOSchema.pick({
  id: true,
  name: true,
  description: true,
  type: true,
});

export type ViewSimpleVO = z.infer<typeof ViewSimpleVOSchema>;

export const ViewRenderOptsSchema = RenderOptionSchema.extend({
  // initData: z.boolean().optional(),
});

export type ViewRenderOpts = z.infer<typeof ViewRenderOptsSchema>;

export const ViewDetailVOSchema = ViewVOSchema.extend({
  records: RecordPaginationVOSchema.optional(),
});

export type ViewDetailVO = z.infer<typeof ViewDetailVOSchema>;
