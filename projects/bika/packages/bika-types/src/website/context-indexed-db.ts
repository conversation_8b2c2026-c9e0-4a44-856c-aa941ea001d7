/**
 * IndexedDB 全局数据库管理模块
 *
 * 统一管理数据库存储的方案：
 *
 * 1. 存储名称管理
 * - 使用 STORE_NAMES 常量统一定义所有存储名称
 * - 避免硬编码字符串，减少拼写错误
 *
 * 2. 存储配置管理
 * - 使用 STORE_CONFIGS 配置数组定义存储结构
 * - 包含存储名称、键路径和索引配置
 * - 用于验证配置完整性，防止遗漏
 *
 * 3. 自动化清理和验证
 * - cleanupOldStores 方法会根据配置自动清理所有存储
 * - validateStoreConfigs 方法会检查配置完整性
 * - 添加新存储时的步骤：
 *   1. 在 STORE_NAMES 中添加名称常量
 *   2. 在 STORE_CONFIGS 中添加配置
 *   3. 在 GlobalIndexedDBSchema 中添加类型定义
 *   4. 创建对应的 createStore 方法
 *   5. 在 createAllStores 中调用创建方法
 *
 * 4. 类型安全
 * - 所有存储名称都有类型约束
 * - 配置和实际使用保持一致性
 */

import { openDB, DBSchema, type IDBPDatabase } from 'idb';
import React from 'react';
import type { SkillsetSelectBO } from '@bika/types/skill/bo';
import { useGlobalContext } from './context-global-context';
import type { AIChatContextVO } from '../ai/vo-ai-chat-context';
import type { AIChatOption } from '../ai/vo-ai-sdk';
import type { NodeMenuVO } from '../node/vo-node';
import { TalkDetailVO } from '../space/vo-talk';

export type IDataRecently = NodeMenuVO & {
  spaceId: string;
  indexedTime: Date; // indexed time
};

export type IDataSpaceTalk = TalkDetailVO & {
  spaceId: string;
};

export type IDataAIChatSession = {
  id: string; // selector key: ${storageKey}:${chatId}
  chatId?: string;
  option?: AIChatOption;
  contexts?: AIChatContextVO[];
  skillsets?: SkillsetSelectBO[];
  input?: string;
  updatedAt: Date;
};

// 数据库存储名称常量
export const STORE_NAMES = {
  RECENTLY_NODE_DETAILS: 'RECENTLY_NODE_DETAILS',
  SPACE_TALKS: 'SPACE_TALKS',
  AI_CHAT_SESSIONS: 'AI_CHAT_SESSIONS',
} as const;

// 存储配置类型
type StoreConfig = {
  name: keyof typeof STORE_NAMES;
  keyPath: string;
  indexes: Array<{
    name: string;
    keyPath: string | string[];
  }>;
};

// 存储配置对象
const STORE_CONFIGS: StoreConfig[] = [
  {
    name: 'RECENTLY_NODE_DETAILS',
    keyPath: 'id',
    indexes: [
      { name: 'id', keyPath: 'id' },
      { name: 'spaceId', keyPath: 'spaceId' },
      { name: 'indexedTime', keyPath: 'indexedTime' },
    ],
  },
  {
    name: 'SPACE_TALKS',
    keyPath: 'id',
    indexes: [
      { name: 'id', keyPath: 'id' },
      { name: 'spaceId', keyPath: 'spaceId' },
      { name: 'createdAt', keyPath: 'createdAt' },
      { name: 'updatedAt', keyPath: 'updatedAt' },
    ],
  },
  {
    name: 'AI_CHAT_SESSIONS',
    keyPath: 'id',
    indexes: [
      { name: 'id', keyPath: 'id' },
      { name: 'chatId', keyPath: 'chatId' },
      { name: 'updatedAt', keyPath: 'updatedAt' },
    ],
  },
];

// Type definitions, refer to idb typescript part: https://www.npmjs.com/package/idb#examples
export interface GlobalIndexedDBSchema extends DBSchema {
  /**
   * Recently accessed NodeDetailVO details
   * @deprecated 改用 Feed
   */
  [STORE_NAMES.RECENTLY_NODE_DETAILS]: {
    key: string; // id
    value: IDataRecently;
    // indexes
    indexes: {
      id: string;
      spaceId: string;
      indexedTime: Date;
    };
  };

  /**
   * Chat Feeds
   */
  [STORE_NAMES.SPACE_TALKS]: {
    key: string; // id
    value: IDataSpaceTalk;
    // indexes
    indexes: {
      id: string;
      spaceId: string;
      createdAt: Date;
      updatedAt: Date;
    };
  };

  /**
   * AI Chat Contexts
   */
  [STORE_NAMES.AI_CHAT_SESSIONS]: {
    key: string; // composite key: ${storageKey}:${chatId}
    value: IDataAIChatSession;
    // indexes
    indexes: {
      id: string;
      storageKey: string;
      chatId: string;
      updatedAt: Date;
    };
  };
}

const DB_NAME_PREFIX = 'bika.ai-idb';

class GlobalIndexedDB {
  static async doInit(hostname: string) {
    return openDB<GlobalIndexedDBSchema>(`${DB_NAME_PREFIX}-${hostname}`, 1, {
      upgrade(db, oldVersion, newVersion) {
        if (oldVersion !== newVersion) {
          // 清理旧的对象存储
          GlobalIndexedDB.cleanupOldStores(db);
        }

        // 创建所有需要的对象存储
        GlobalIndexedDB.createAllStores(db);
      },
    });
  }

  /**
   * 清理旧的对象存储
   */
  private static cleanupOldStores(db: IDBPDatabase<GlobalIndexedDBSchema>) {
    // 遍历所有配置的存储名称进行清理
    STORE_CONFIGS.forEach((config) => {
      const storeName = STORE_NAMES[config.name];
      if (db.objectStoreNames.contains(storeName)) {
        console.log(`delete idb ${storeName}`);
        db.deleteObjectStore(storeName);
      }
    });
  }

  /**
   * 创建所有对象存储
   */
  private static createAllStores(db: IDBPDatabase<GlobalIndexedDBSchema>) {
    // 验证配置完整性
    GlobalIndexedDB.validateStoreConfigs();

    // 调用专用的创建方法
    GlobalIndexedDB.createRecentlyNodeDetailsStore(db);
    GlobalIndexedDB.createSpaceTalksStore(db);
    GlobalIndexedDB.createAIChatContextsStore(db);
  }

  /**
   * 验证存储配置的完整性
   * 确保所有存储名称都在配置中定义
   */
  private static validateStoreConfigs() {
    const configuredStores = new Set(STORE_CONFIGS.map((config) => config.name));
    const allStoreNames = Object.keys(STORE_NAMES) as Array<keyof typeof STORE_NAMES>;

    allStoreNames.forEach((storeName) => {
      if (!configuredStores.has(storeName)) {
        console.warn(`存储 ${storeName} 没有在配置中定义，可能会导致创建或清理时遗漏`);
      }
    });
  }

  /**
   * 创建最近访问节点详情存储
   */
  private static createRecentlyNodeDetailsStore(db: IDBPDatabase<GlobalIndexedDBSchema>) {
    const recentlyNodeDetailsStore = db.createObjectStore(STORE_NAMES.RECENTLY_NODE_DETAILS, {
      keyPath: 'id',
    });
    recentlyNodeDetailsStore.createIndex('id', 'id');
    recentlyNodeDetailsStore.createIndex('spaceId', 'spaceId');
    recentlyNodeDetailsStore.createIndex('indexedTime', 'indexedTime');
  }

  /**
   * 创建空间对话存储
   */
  private static createSpaceTalksStore(db: IDBPDatabase<GlobalIndexedDBSchema>) {
    const feedsStore = db.createObjectStore(STORE_NAMES.SPACE_TALKS, {
      keyPath: 'id',
    });
    feedsStore.createIndex('id', 'id');
    feedsStore.createIndex('spaceId', 'spaceId');
    feedsStore.createIndex('createdAt', 'createdAt');
    feedsStore.createIndex('updatedAt', 'updatedAt');
  }

  /**
   * 创建AI聊天上下文存储
   */
  private static createAIChatContextsStore(db: IDBPDatabase<GlobalIndexedDBSchema>) {
    const aiChatContextsStore = db.createObjectStore(STORE_NAMES.AI_CHAT_SESSIONS, {
      keyPath: 'id',
    });
    aiChatContextsStore.createIndex('id', 'id');
    aiChatContextsStore.createIndex('storageKey', 'storageKey');
    aiChatContextsStore.createIndex('chatId', 'chatId');
    aiChatContextsStore.createIndex('updatedAt', 'updatedAt');
  }
}

// 全局 IndexedDB 实例引用
let dbRef: IDBPDatabase<GlobalIndexedDBSchema> | null = null;

/**
 * 获取全局 IndexedDB 实例（推荐使用）
 */
export function useGlobalIndexedDB() {
  const globalContext = useGlobalContext();
  React.useEffect(() => {
    if (dbRef !== null) return;
    (async () => {
      const newDB = await GlobalIndexedDB.doInit(globalContext.hostname);
      dbRef = newDB;
    })();
  }, []);

  return {
    db: dbRef,
    isLoading: dbRef === null,
  };
}
