import { z } from '@hono/zod-openapi';
import { RecordDataSchema } from '../database/bo-record';
import { RecordBulkUpdatesSchema, RecordCreateDTOSchema, RecordUpdateDTOSchema } from '../database/dto-record';
import { i18n, isValidTimeZone } from '../system';

/**
 * @deprecated use `ApiCreateRecordsReqSchema` instead
 */
export const ApiCreateRecordReqSchema = RecordCreateDTOSchema.omit({
  databaseId: true,
  mirrorId: true,
  formId: true,
  sharing: true,
});
/**
 * @deprecated use `ApiCreateRecordsReq` instead
 */
export type ApiCreateRecordReq = z.infer<typeof ApiCreateRecordReqSchema>;

export const ApiCreateRecordsReqSchema = z
  .object({
    cells: z.array(RecordDataSchema),
  })
  .openapi({
    description: 'Array of record cells',
    example: {
      cells: [
        {
          single_text: 'text 1',
          number: 123,
          checkbox: true,
        },
        {
          single_text: 'text 2',
          number: 456,
          checkbox: false,
        },
        {
          single_text: 'text 3',
          number: 789,
          checkbox: true,
        },
      ],
    },
  });
export type ApiCreateRecordsReq = z.infer<typeof ApiCreateRecordsReqSchema>;

/**
 * @deprecated use `ApiUpdateRecordDTOSchema` instead
 */
export const ApiRecordUpdateReqSchema = RecordUpdateDTOSchema.omit({ databaseId: true, mirrorId: true });
/**
 * @deprecated use `ApiUpdateRecordDTO` instead
 */
export type ApiRecordUpdateReq = z.infer<typeof ApiRecordUpdateReqSchema>;

/**
 * @deprecated use `ApiUpdateRecordReqV2Schema` instead
 */
export const ApiUpdateRecordReqSchema = z
  .object({
    cells: RecordDataSchema,
  })
  .openapi({
    description: 'cells to update',
    example: {
      cells: {
        single_text: 'single text',
      },
    },
  });
/**
 * @deprecated use `ApiUpdateRecordReqV2` instead
 */
export type ApiUpdateRecordReq = z.infer<typeof ApiUpdateRecordReqSchema>;

/**
 * @deprecated use `ApiUpdateRecordsReqV2Schema` instead
 */
export const ApiUpdateRecordsReqSchema = z.object({
  records: RecordBulkUpdatesSchema,
});
/**
 * @deprecated use `ApiUpdateRecordsReqV2` instead
 */
export type ApiUpdateRecordsReq = z.infer<typeof ApiUpdateRecordsReqSchema>;

export const RecordSortSchema = z.object({
  field: z.string().openapi({ description: 'The field to sort by' }),
  order: z
    .enum(['asc', 'desc'])
    .openapi({ description: 'The order to sort by, either ascending (asc) or descending (desc)' }),
});
export type RecordSort = z.infer<typeof RecordSortSchema>;

export const RecordSortArraySchema = RecordSortSchema.array();
export type RecordSorts = z.infer<typeof RecordSortArraySchema>;

/**
 * @deprecated this schema is deprecated, use `ApiGetRecordsReqSchema` instead
 */
export const ApiRecordsQueryDTOSchema = z.object({
  viewId: z.string().optional(),
  pageNum: z.string().min(1).optional().default('1'),
  pageSize: z.string().min(1).max(1000).optional().default('100'),
  sort: RecordSortArraySchema.optional(),
  recordIds: z.string().optional(),
  fields: z.string().optional(),
  filter: z.string().optional(),
  maxRecords: z.string().min(1).optional(),
  cellFormat: z.literal('json').or(z.literal('string')).optional(),
  fieldKey: z.literal('name').or(z.literal('id')).optional().default('name'),
});

/**
 * Bika OpenAPI - Standardized Request Schema for Getting Records
 * @deprecated use `ApiGetRecordsReqV2Schema` instead
 */
export const ApiGetRecordsReqSchema = z.object({
  viewId: z
    .string()
    .optional()
    .openapi({
      type: 'string',
      example: 'viw',
      description: `
(Optional) View ID. defaults to the first view in the datasheet.<br/>
The request will return the results of the view after filtering/sorting in the view,<br/>
you can use the fields parameter to filter the data of unwanted fields.
  `,
    }),
  pageNum: z
    .number()
    .min(1, { message: 'Page number must be at least 1' })
    .optional()
    .default(1)
    .openapi({
      type: 'number',
      description: `
(Optional) Specifies the total number of records returned per page,<br/>
the default value is 100. this parameter only accepts integers from 1 to 1000.
  `,
    }),
  pageSize: z
    .number()
    .min(1, { message: 'Page size must be at least 1' })
    .max(100, { message: 'Page size can be at most 100' })
    .optional()
    .default(100)
    .openapi({
      type: 'number',
      example: 100,
      description: `
(Optional) Specifies the page number of the pagination, default is 1,<br/>
used in conjunction with the parameter pageSize.
  `,
    }),
  recordIds: z
    .array(z.string())
    .optional()
    .openapi({
      type: 'array',
      example: ['recordId1', 'recordId2'],
      description: `
(Optional) An array of record IDs. If this parameter is attached,<br/>
an array of records with the specified IDs is returned.<br/>
The returned values are sorted in the order of the incoming array.<br/>
Ignore filtering and sorting at this point. No paging, up to 1000 records per query.
  `,
    }),
  fields: z
    .array(z.string())
    .optional()
    .openapi({
      type: 'array',
      example: ['field1', 'field2'],
      description: `
      (Optional) Specify which fields to return in the records.
      If this parameter is included, only the specified fields will be returned in the records.`,
    }),
  filter: z
    .string()
    .optional()
    .openapi({
      type: 'string',
      example: 'field1="value1";field2>10',
      description: `
      (Optional) Use the grammar as a filter condition to return matching records,
      access https://bika.ai/help understanding how the grammar is used.
      `,
    }),
  sort: RecordSortArraySchema.optional().openapi({
    type: 'array',
    example: [{ field: 'field1', order: 'asc' }],
    description: `
      (Optional) Sorts the records in the specified datasheet.
      An array of multiple sort objects. Supports order: 'asc' and reverse order: 'desc'.
      Note: The sort condition specified by this parameter will override the sort condition in the view.
    `,
  }),
  maxRecords: z
    .number()
    .min(1, { message: 'Max records must be at least 1' })
    .optional()
    .openapi({
      type: 'number',
      example: 100,
      description: `
      (Optional) Limits the total number of records returned.
      If the value is less than the actual total number of records in the database,
      the database number of records returned will be limited to that value.
    `,
    }),
  cellFormat: z
    .union([z.literal('json'), z.literal('string')])
    .optional()
    .default('json')
    .openapi({
      type: 'string',
      example: 'json',
      description: `
      The cellFormat parameter defines how cell values are returned:
        json: Returns structured data based on the field type (default).
        string: Returns user-friendly text; requires both timeZone and userLocale.
      Note: The string format may change and should not be relied on for consistency.
    `,
    }),
  fieldKey: z
    .enum(['name', 'id'])
    .optional()
    .default('name')
    .openapi({
      type: 'string',
      example: 'name',
      description: `
      The fieldKey parameter specifies that let you return field objects where the key is:
        name: Uses the field name as the key (default).
        id: Uses the field ID as the key.
      `,
    }),
});

/**
 * @deprecated use `ApiGetRecordsReqV2` instead
 */
export type ApiGetRecordsReq = z.infer<typeof ApiGetRecordsReqSchema>;

// ============= below are the new schemas for database OpenAPI V2 version =============

export const CellFormatSchema = z.union([z.literal('json'), z.literal('string')]);
export type CellFormat = z.infer<typeof CellFormatSchema>;

/**
 * Bika OpenAPI v2 - Standardized Request Schema for Getting Records
 * features:
 * 1. `offset` is replaced with `pageNum` for clarity.
 * 2. `recordIds` is removed
 * 3. add `timeZone` and `userLocale` for `cellFormat=string` format cell value.
 */
export const ApiGetRecordsReqV2Schema = z.object({
  timeZone: z
    .string()
    .refine(isValidTimeZone, (val) => ({ message: `${val} is not a valid time zone` }))
    .optional()
    .openapi({
      example: 'Asia/Shanghai',
      description:
        '(Optional) The time zone that should be used to format dates when using <strong>cellFormat=string.</strong><br> ' +
        'This parameter is required when using <strong>cellFormat=string.</strong><br>' +
        'this parameter must be encoded before passing it as a value. <br>' +
        'For example, <strong>Asia/Shanghai</strong> should be encoded as <strong>Asia%2FShanghai</strong>.',
    }),
  userLocale: z
    .enum(i18n.locales)
    .optional()
    .openapi({
      example: 'en',
      description:
        '(Optional) The user locale that should be used to format dates when using <strong>cellFormat=string.</strong> <br>' +
        'This parameter is required when using <strong>cellFormat=string.</strong>',
    }),
  // open in the future
  // viewId: z
  //   .string()
  //   .optional()
  //   .openapi({
  //     type: 'string',
  //     example: 'viw',
  //     description: `
  //   (Optional) The id of view in the database, if set, only the records in this view will be returned.
  //   The records will be filtered and sorted according to the view configuration,
  //   pass the sort parameter in the request. which will override the view's sorting.
  //   Fields hidden in this view will be returned in the results,
  //   you can use the fields parameter to filter the data of unwanted fields.
  //   `,
  //   }),
  offset: z
    .string()
    .optional()
    .openapi({
      type: 'string',
      description:
        '(Optional) Use the offset from the previous request to fetch the next page of records. <br>' +
        'If not set, the first page of records will be returned.',
    }),
  pageSize: z.coerce
    .number()
    .min(1, { message: 'Page size must be at least 1' })
    .max(100, { message: 'Page size can be at most 100' })
    .default(100)
    .optional()
    .openapi({
      type: 'number',
      example: 100,
      description:
        '(Optional) Specifies the page number of the pagination, default is 100,<br>' +
        'used in conjunction with the parameter <strong>pageSize</strong>.',
    }),
  maxRecords: z.coerce
    .number()
    .refine((val) => val > 0, { message: 'Max records must be at least 1' })
    .optional()
    .openapi({
      type: 'number',
      example: 300,
      description:
        '(Optional) Limits the total number of records returned.<br>' +
        'If the value is less than the actual total number of records in the database,<br>' +
        'the database number of records returned will be limited to that value.',
    }),
  fields: z
    .preprocess((val) => {
      if (Array.isArray(val)) return val;
      if (typeof val === 'string') return val ? [val] : [];
      return undefined;
    }, z.array(z.string()))
    .optional()
    .openapi({
      param: { name: 'fields', in: 'query', required: false, style: 'form', explode: true },
      example: ['field1', 'field2'],
      description:
        '(Optional) Specify which fields to return in the records.<br>' +
        'If this parameter is included, only the specified fields will be returned in the records.',
    }),
  filter: z
    .string()
    .optional()
    .openapi({
      type: 'string',
      example: 'field1=="value1";field2>10',
      description:
        '(Optional) Use the grammar as a filter condition to return matching records,<br>' +
        'the filter must be encoded first before passing it as a value.<br>' +
        'For example, <strong>field1=="value1";field2>10</strong> should be encoded as <strong>field1%3D%3D%22value1%22%3Bfield2%3E10</strong>.<br>' +
        'please access help document <a target="_blank" href="https://bika.ai/help/guide/developer/filter-query-language">Filter Query Language</a> understanding how the grammar is used.',
    }),
  sort: RecordSortArraySchema.optional().openapi({
    param: {
      style: 'deepObject',
      explode: true,
    },
    type: 'array',
    items: {
      type: 'object',
      properties: {
        field: { type: 'string', description: 'The field to sort by' },
        order: {
          type: 'string',
          enum: ['asc', 'desc'],
          description:
            'The order to sort by, either ascending (<strong>asc</strong>) or descending (<strong>desc</strong>)',
        },
      },
    },
    description:
      '(Optional) Sorts the records in the specified database.An array of multiple sort objects.<br>' +
      'Supports order: "asc" and reverse order: "desc". <br>' +
      'Note: The sort condition specified by this parameter will override the sort condition in the view.<br>' +
      'eg: <strong>sort[0][field]=field1&sort[0][order]=asc&sort[1][field]=field2&sort[1][order]=desc</strong>',
  }),
  cellFormat: CellFormatSchema.default('json')
    .optional()
    .openapi({
      type: 'string',
      example: 'json',
      description:
        '(Optional) The cellFormat parameter defines how cell values are returned:<br>' +
        '<strong>json</strong>: Returns structured data based on the field type (default).<br>' +
        '<strong>string</strong>: Returns user-friendly text; requires both timeZone and userLocale.<br>' +
        'Note: The string format may change and should not be relied on for consistency.<br>' +
        'More details about cellFormat can be found in the <a target="_blank" href="https://bika.ai/help/guide/developer/cell-value">Cell Value</a>.',
    }),
  fieldKey: z
    .enum(['name', 'id'])
    .default('name')
    .optional()
    .openapi({
      type: 'string',
      example: 'name',
      description:
        'The fieldKey parameter specifies that let you return field objects where the key is:<br>' +
        '<strong>name</strong>: Uses the field name as the key (default).<br>' +
        '<strong>id</strong>: Uses the field ID as the key.',
    }),
});
export type ApiGetRecordsReqV2 = z.infer<typeof ApiGetRecordsReqV2Schema>;

/**
 * Bika OpenAPI v2 - Standardized Request Schema for Getting a Single Record
 */
export const ApiGetRecordReqV2Schema = ApiGetRecordsReqV2Schema.pick({
  timeZone: true,
  userLocale: true,
  cellFormat: true,
  fieldKey: true,
});
export type ApiGetRecordReqV2 = z.infer<typeof ApiGetRecordReqV2Schema>;

const AttachmentCellValueSchema = z.object({
  id: z.string().openapi({
    description: 'Attachment Unique ID',
  }),
  name: z.string().optional().openapi({
    description: 'Attachment name, if not provided, the name will be the same as the original file name',
  }),
});

export const FieldCellValueInputSchema = z.union([
  z.null(), // for set empty cell
  z.boolean(), // for checkbox
  z.number(), // for number/currency/percent/rating
  z.string(), // for single text, long text, date(ISO 8601 format), email, phone, url, single select
  z.string().array(), // for multi select, member, link record id
  AttachmentCellValueSchema.array(), // for attachment
]);
export type FieldCellValueInput = z.infer<typeof FieldCellValueInputSchema>;

export const RecordFieldSchema = z.object({
  fields: z.record(FieldCellValueInputSchema).openapi({
    description: 'The fields of the record, structured as key-value pairs.',
    example: {
      single_text: 'text 1',
      number: 123,
      checkbox: true,
      date: '2021-03-29T10:05:45-08:00',
    },
  }),
});
export type RecordField = z.infer<typeof RecordFieldSchema>;
const RecordFieldExample: RecordField = {
  fields: {
    single_text: 'text 1',
    number: 123,
    currency: 99.99,
    percent: 50,
    checkbox: true,
    single_select: 'Done',
    multi_select: ['Monday', 'Tuesday'],
    date: '2021-03-29T10:05:00.000Z',
    date_range: '2021-03-29T10:05:00.000Z/2021-04-05T10:05:00.000Z',
    attachment: [
      {
        id: 'att123',
        name: 'touch.jpg',
      },
      {
        id: 'att456',
        name: 'document.pdf',
      },
    ],
    assigners: ['member_id', 'team_id', 'role_id'],
    link_record_ids: ['rec123', 'rec456'],
  },
};

/**
 * Bika OpenAPI v2 - Create Records Request Schema
 */
export const ApiCreateRecordsReqV2Schema = ApiGetRecordsReqV2Schema.pick({ fieldKey: true })
  .partial()
  .extend({
    records: z
      .array(RecordFieldSchema)
      .max(10, { message: 'Up to 10 records can be created in one request' })
      .optional()
      .openapi({
        description: 'Array of record objects to be created.',
        example: [RecordFieldExample],
      }),
  });
export type ApiCreateRecordsReqV2 = z.infer<typeof ApiCreateRecordsReqV2Schema>;

/**
 * Bika OpenAPI v2 - Update Record Request Schema
 */
export const ApiUpdateRecordReqV2Schema = ApiGetRecordsReqV2Schema.pick({ fieldKey: true })
  .partial()
  .merge(RecordFieldSchema)
  .openapi({
    description: 'Fields to update in the record.',
    example: {
      fieldKey: 'name',
      ...RecordFieldExample,
    },
  });
export type ApiUpdateRecordReqV2 = z.infer<typeof ApiUpdateRecordReqV2Schema>;

/**
 * Bika OpenAPI v2 - Update Multiple Records Request Schema
 */
export const ApiUpdateRecordsReqV2Schema = ApiGetRecordsReqV2Schema.pick({ fieldKey: true })
  .partial()
  .extend({
    records: RecordFieldSchema.extend({
      id: z.string().openapi({
        example: 'rec111',
        description: 'Record ID',
      }),
    })
      .array()
      .max(10, { message: 'Up to 10 records can be updated in one request' })
      .openapi({
        description: 'Array of record objects to be updated.',
        example: [
          {
            id: 'rec1',
            ...RecordFieldExample,
          },
          {
            id: 'rec2',
            ...RecordFieldExample,
          },
        ],
      }),
  });
export type ApiUpdateRecordsReqV2 = z.infer<typeof ApiUpdateRecordsReqV2Schema>;

/**
 * Bika OpenAPI v2 - Delete Record Request Schema
 */
export const ApiDeleteRecordReqV2Schema = z.object({
  records: z
    .preprocess(
      (val) => {
        if (Array.isArray(val)) return val;
        if (typeof val === 'string') return val ? [val] : [];
        return [];
      },
      z.array(z.string()).min(1).max(10, { message: 'Up to 10 records can be deleted in one request' }),
    )
    .openapi({
      param: { name: 'records', in: 'query', required: true },
      example: ['rec1***', 'rec2***'],
      description: 'Array of record IDs to delete, max 10 records',
    }),
});
export type ApiDeleteRecordReqV2 = z.infer<typeof ApiDeleteRecordReqV2Schema>;
