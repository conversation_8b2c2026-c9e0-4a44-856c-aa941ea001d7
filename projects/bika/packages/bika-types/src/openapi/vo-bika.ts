import { z } from '@hono/zod-openapi';
import { AttachmentCellDataSchema, DocCellDataSchema, RecordDataSchema } from '../database/bo-record';
import { UnitTypeSchema } from '../unit/vo-base';

export const SystemMetaVOSchema = z.object({
  version: z.string(),
  hostname: z.string(),
  headers: z.record(z.string()),
  isFromCN: z.boolean().optional(),
});

export type SystemMetaVO = z.infer<typeof SystemMetaVOSchema>;

export const EmbedLinkVOSchema = z.object({
  id: z.string(),
  objectType: z.string(),
  objectId: z.string(),
  // Link embeddable in iframe
  url: z.string(),
});

export type EmbedLinkVO = z.infer<typeof EmbedLinkVOSchema>;

/**
 * @deprecated use `ApiRecordVOSchema` instead
 */
export const BikaRecordVOSchema = z.object({
  recordId: z.string(),
  fields: RecordDataSchema,
});
/**
 * @deprecated use `ApiRecordVO` instead
 */
export type BikaRecordVO = z.infer<typeof BikaRecordVOSchema>;

/**
 * Get Records Response
 * @deprecated use `GetRecordsResponseVOSchema` instead
 */
export const GetRecordsResVOSchema = z.object({
  total: z.number(),
  pageNum: z.number(),
  pageSize: z.number(),
  records: z.array(
    BikaRecordVOSchema.and(
      z.object({
        createdAt: z.number(),
        updatedAt: z.number(),
      }),
    ),
  ),
});
/**
 * @deprecated use `ApiGetRecordsResVO` instead
 */
export type GetRecordsResVO = z.infer<typeof GetRecordsResVOSchema>;

// ============= below are the new schemas for database OpenAPI V2 version =============

// Cell Format = json
const OpenAPIAttachmentCellValueSchema = AttachmentCellDataSchema.pick({
  id: true,
  name: true,
  size: true,
  mimeType: true,
}).extend({
  url: z.string().optional().openapi({ description: 'The URL of the attachment' }),
  thumbnailUrl: z.string().nullish(),
});
export type OpenAPIAttachmentCellValue = z.infer<typeof OpenAPIAttachmentCellValueSchema>;
const OpenAPIMemberCellValueSchema = z.object({
  id: z.string().openapi({
    description: 'Contact Unique ID',
  }),
  type: UnitTypeSchema.openapi({
    description: 'Contact Type, eg: MEMBER',
  }),
  name: z.string(),
  deleted: z.boolean().optional(),
});
export type OpenAPIMemberCellValue = z.infer<typeof OpenAPIMemberCellValueSchema>;
const OpenAPIUserCellValueSchema = OpenAPIMemberCellValueSchema.pick({ id: true, name: true, deleted: true });
export type OpenAPIUserCellValue = z.infer<typeof OpenAPIUserCellValueSchema>;
const OpenAPIFieldCellValueSchema = z.union([
  z.null(),
  z.boolean(),
  z.number(),
  z.number().array(),
  z.string(),
  z.string().array(),
  OpenAPIUserCellValueSchema,
  OpenAPIUserCellValueSchema.array(),
  OpenAPIMemberCellValueSchema.array(),
  OpenAPIAttachmentCellValueSchema.array(),
  DocCellDataSchema,
  DocCellDataSchema.array(),
]);
export type OpenAPIFieldCellValue = z.infer<typeof OpenAPIFieldCellValueSchema>;
const ApiRecordFieldsSchema = z.record(OpenAPIFieldCellValueSchema);
export type ApiRecordFields = z.infer<typeof ApiRecordFieldsSchema>;

export const RecordFieldsExample: ApiRecordFields = {
  single_text: 'text 1',
  number: 123,
  checkbox: true,
  single_select: 'Done',
  multi_select: ['Monday', 'Tuesday'],
  date: '2023-10-01T10:05:45.000Z',
  date_range: '2023-10-01T10:05:45.000Z -> 2023-10-02T10:05:45.000Z',
  created_at: '2023-10-01T00:00:00.000Z',
  link_record: ['rec123', 'rec456'],
  attachment: [
    {
      id: 'att123',
      name: 'attachment.jpg',
      size: 1024,
      mimeType: 'image/jpeg',
      url: 'https://example.com/attachment.jpg',
      thumbnailUrl: 'https://example.com/thumbnail.jpg',
    },
  ],
  assigners: [
    {
      id: 'tem123',
      type: 'Team',
      name: 'HR Team',
    },
    {
      id: 'rol123',
      type: 'Role',
      name: 'Manager',
    },
    {
      id: 'mem123',
      type: 'Member',
      name: 'John Doe',
    },
  ],
  created_by: {
    id: 'user123',
    name: 'Jane Smith',
  },
  last_modified_by: {
    id: 'user123',
    name: 'Jane Smith',
  },
};

/**
 * Bika OpenAPI V2 - OpenAPI Record VO Schema
 */
export const ApiRecordVOSchema = z
  .object({
    id: z.string().openapi({
      description: 'Record ID',
    }),
    fields: ApiRecordFieldsSchema.openapi({
      description: 'The fields of the record, structured as key-value pairs.',
      example: RecordFieldsExample,
    }),
    createdAt: z.string().openapi({
      description: 'The creation timestamp of the record in the ISO format, eg: 2020-01-01T00:00:00.000Z',
      example: new Date().toISOString(),
    }),
    updatedAt: z.string().optional().openapi({
      description: 'The last update timestamp of the record in the ISO format, eg: 2020-01-01T00:00:00.000Z',
      example: new Date().toISOString(),
    }),
  })
  .openapi('RecordVO');

export type ApiRecordVO = z.infer<typeof ApiRecordVOSchema>;

/**
 * Bika OpenAPI V2 - OpenAPI Get Records Response Schema
 */
export const ApiGetRecordsResSchema = z.object({
  offset: z.string().optional().openapi({
    description: 'Return an offset if there are more records to fetch. use to fetch next page of records.',
  }),
  hasMore: z.boolean().openapi({
    description: 'Indicates if there are more records to fetch.',
  }),
  records: z.array(ApiRecordVOSchema).openapi({
    description: 'Array of record object',
  }),
});
export type ApiGetRecordsRes = z.infer<typeof ApiGetRecordsResSchema>;

/**
 * Bika OpenAPI V2 - OpenAPI Create Records Response Schema
 */
export const ApiCreateRecordsResSchema = z.object({
  // created records
  records: z.array(ApiRecordVOSchema).openapi({
    description: 'Array of record objects to be created.',
  }),
  // add more fields in the future if needed
});
export type ApiCreateRecordsRes = z.infer<typeof ApiCreateRecordsResSchema>;

/**
 * Bika OpenAPI V2 - OpenAPI Update Multiple Records Response Schema
 */
export const ApiUpdateRecordsResSchema = z.object({
  // updated records
  records: z.array(ApiRecordVOSchema).openapi({
    description: 'Array of record objects that were updated.',
  }),
  // add more fields in the future if needed
});
export type ApiUpdateRecordsRes = z.infer<typeof ApiUpdateRecordsResSchema>;

/**
 * Bika OpenAPI V2 - OpenAPI Delete Record Response Schema
 */
export const ApiDeleteRecordResSchema = z.object({
  id: z.string().openapi({
    description: 'The ID of the record that was deleted.',
  }),
  deleted: z.boolean().openapi({
    description: 'Indicates whether the record was successfully deleted.',
  }),
});
export type ApiDeleteRecordRes = z.infer<typeof ApiDeleteRecordResSchema>;
