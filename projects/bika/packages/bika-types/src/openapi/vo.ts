import { z } from '@hono/zod-openapi';

export * from './vo-bika';

export const createResponseVOSchema = (data: z.ZodType) =>
  z.object({
    success: z.boolean(),
    code: z.number().openapi({
      description: 'bika status code',
      example: 200,
    }),
    message: z.string().openapi({
      description: 'Response message',
      example: 'SUCCESS',
    }),
    data,
  });

export interface ResponseVO<T> {
  success: boolean;
  code: number;
  message: string;
  data: T;
}

export class ResponseVOBuilder {
  static success<T>(data: T): ResponseVO<T> {
    return {
      success: true,
      code: 200,
      message: 'SUCCESS',
      data,
    };
  }

  static error(message: string, code: number): ResponseVO<object> {
    return {
      success: false,
      code,
      message,
      data: {},
    };
  }
}
