import React from 'react';
import { useApiCaller } from '@bika/api-caller/context';
import { TalkDetailVO } from '@bika/types/space/vo';
import { useTalksDB } from '../talks-db';

/**
 *  空间站的 space feeds，采取本地 indexed db 缓存优先策略:
 *
 * 1. 首次全局获取( remoteListFeeds )，并存到 indexed DB
 * 2. SSE动态 服务端推送更新，个别存到 indexed DB
 * 3. 超时(localStorgage)，全局获取(remoteListFeeds)，更新缓存
 *
 *
 * @returns
 */
export function useSpaceTalks(spaceId: string) {
  const { trpc } = useApiCaller();

  const [localSpaceTalks, setLocalSpaceTalks] = React.useState<TalkDetailVO[]>([]);

  const talksDB = useTalksDB(spaceId);

  const isLoading = talksDB.isLoading;

  const loadLocalTalks = React.useCallback(async () => {
    const talks = await talksDB.getAll();
    setLocalSpaceTalks(talks || []);
  }, [talksDB]);

  const getRemoteTalks = React.useCallback(async () => {
    // 从网上获取所有 feeds
    const remoteFeeds: TalkDetailVO[] = await trpc.talk.searchTalks.query({
      mode: 'recently-chats',
      spaceId,
    });

    // 获取本地 IndexedDB 中的数据
    const localFeeds = (await talksDB.getAll()) || [];
    const remoteFeedIds = new Set(remoteFeeds.map((feed) => feed.id));

    // 更新或添加远程数据到 IndexedDB
    for (const remoteFeed of remoteFeeds) {
      await talksDB.update(remoteFeed);
    }

    // 删除本地多余的数据（远程不存在的数据）
    for (const localFeed of localFeeds) {
      if (!remoteFeedIds.has(localFeed.id)) {
        await talksDB.delete(localFeed.id);
      }
    }

    // 重新加载本地数据到状态
    await loadLocalTalks();
  }, [trpc.talk.searchTalks, spaceId, talksDB, loadLocalTalks]);

  React.useEffect(() => {
    const calcluate = (e: CustomEvent) => {
      if (e.detail?.key !== 'LAST_TIME_UPDATE_TALK') return;

      loadLocalTalks();
    };

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    window.addEventListener('localStorageChange', calcluate as any);

    return () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      window.removeEventListener('localStorageChange', calcluate as any);
    };
  }, [loadLocalTalks]);

  // 首次载本地数据
  React.useEffect(() => {
    if (!isLoading) {
      loadLocalTalks().then(() => {
        getRemoteTalks();
      });
    }
  }, [isLoading]);

  const updateTalk = async (data: TalkDetailVO) => {
    await talksDB.update(data);
    loadLocalTalks();
  };

  const deleteTalk = async (id: string) => {
    await talksDB.delete(id);
    loadLocalTalks();
  };

  const sortedSpaceTalks = localSpaceTalks.sort((a: TalkDetailVO, b: TalkDetailVO) => {
    // 首先按 isPinned 排序，置顶的在前面
    if (a.isPinned !== b.isPinned) {
      return (b.isPinned ? 1 : 0) - (a.isPinned ? 1 : 0);
    }
    // 如果 isPinned 相同，则按更新时间倒序排列
    return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
  });

  return {
    data: sortedSpaceTalks,
    update: updateTalk,
    delete: deleteTalk,
    isLoading: talksDB.isLoading,
  };
}
