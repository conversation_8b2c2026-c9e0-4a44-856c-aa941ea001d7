import assert from 'assert';
import { useEffect, useState } from 'react';
import type { NodeMenuVO } from '@bika/types/node/vo';
import { useSpaceContextForce } from '@bika/types/space/context';
import { useGlobalIndexedDB, type IDataRecently } from '@bika/types/website/context';

export function useRecently() {
  const spaceContext = useSpaceContextForce();
  const spaceId = spaceContext.data!.id;
  const { db: globalIndexedDB, isLoading } = useGlobalIndexedDB();

  const [recentlyNodeDetails, setRecentlyNodeDetails] = useState<IDataRecently[]>([]);

  useEffect(() => {
    if (!isLoading) {
      assert(globalIndexedDB, 'Global IndexedDB should be initialized');
      globalIndexedDB.getAllFromIndex('RECENTLY_NODE_DETAILS', 'spaceId', spaceId).then((result) => {
        setRecentlyNodeDetails(result);
      });
    }
  }, [spaceId, globalIndexedDB, isLoading]);

  return {
    isLoading,
    data: recentlyNodeDetails as IDataRecently[],
    update: (data: NodeMenuVO) => {
      assert(globalIndexedDB, 'Global IndexedDB should be initialized');
      globalIndexedDB.put('RECENTLY_NODE_DETAILS', { spaceId, indexedTime: new Date(), ...data });
    },
  };
}
