import assert from 'assert';
import { useCallback } from 'react';
import { TalkDetailVO } from '@bika/types/space/vo';
import { useGlobalIndexedDB } from '@bika/types/website/context';

export const useTalksDB = (spaceId: string) => {
  const { db: globalDB, isLoading } = useGlobalIndexedDB();
  assert(spaceId, 'spaceId should not be null in useTalksDB');

  const update = useCallback(
    async (data: TalkDetailVO) => {
      assert(globalDB, 'Global IndexedDB should be initialized');
      await globalDB.put('SPACE_TALKS', { spaceId, ...data });
    },
    [spaceId, globalDB],
  );

  const deleteItem = useCallback(
    async (id: string) => {
      assert(globalDB, 'Global IndexedDB should be initialized');
      await globalDB.delete('SPACE_TALKS', id);
    },
    [globalDB],
  );

  const getAll = useCallback(async () => {
    assert(globalDB, 'Global IndexedDB should be initialized');
    const result = await globalDB?.getAllFromIndex('SPACE_TALKS', 'spaceId', spaceId);
    return result;
  }, [spaceId, globalDB]);

  return {
    spaceId,
    update,
    delete: deleteItem,
    getAll,
    isLoading,
  };
};
