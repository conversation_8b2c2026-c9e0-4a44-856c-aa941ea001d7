import Image from 'next/image';
import { useSpaceContext } from '@bika/types/space/context';
import { Button } from '@bika/ui/button-component';
import { StandalonePageView } from '@bika/ui/components/standalone/index';
import StarFilled from '@bika/ui/icons/components/star_filled';

interface AlertReachLimitProps {
  text?: string;
}

export const AlertReachLimit = ({ text }: AlertReachLimitProps) => {
  const spaceContext = useSpaceContext();
  return (
    <StandalonePageView>
      <div className="flex flex-col items-center justify-center space-y-5 w-[340px]">
        <div
          className={`w-20 h-20 rounded-full border border-solid bg-transparent relative mx-auto flex items-center justify-center border-[--borderOnbrandLight]`}
          style={{ borderColor: `rgba(123, 103, 238, 0.3)` }}
        >
          <div className="w-[64px] h-[64px] rounded-full border-4 border-[var(--fc8)] flex items-center justify-center">
            <Image
              src={'/assets/pricing/avatar.png'}
              alt=""
              width={64}
              height={64}
              className="w-16 h-16 rounded-full"
            />
          </div>
          <div className="w-3.5 h-3.5 bg-white rounded-full absolute -bottom-1 left-1/2 transform -translate-x-1/2 flex items-center justify-center">
            <StarFilled color={'var(--rainbow-orange5)'} size="12px" />
          </div>
        </div>
        <div>
          <div className="text-b1 text-center">{text}</div>
          <div className="mt-[80px] flex flex-col items-center justify-center space-y-4">
            <Button
              sx={{ width: '100%', height: '40px' }}
              onClick={() => {
                spaceContext?.showUIModal({ type: 'space-settings', tab: { type: 'SPACE_UPGRADE' } });
              }}
            >
              🌟 立即升级
            </Button>
          </div>
        </div>
      </div>
    </StandalonePageView>
  );
};
