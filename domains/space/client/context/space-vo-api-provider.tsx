'use client';

import assert from 'assert';
import React, { useEffect, useState, useCallback } from 'react';
import { Helmet } from 'react-helmet-async';
import { useApiCaller } from '@bika/api-caller/context';
import { GuideIntentUI } from '@bika/domains/ai/client/wizard/intents-ui/guide-intent-ui/index'; // 新手引导 常驻
import { SpaceNodeResourceApiProvider } from '@bika/domains/node/client/context/space-node-resource-api-provider';
import { FabComponent } from '@bika/domains/shared/client/components/fab-component';
import PageSkeleton from '@bika/domains/website/client/layout/page-skeleton/index';
import type { RecordRenderVO } from '@bika/types/database/vo';
import type { NodeResourceScope } from '@bika/types/node/bo';
import { NodeTreeVOSchema, type NodeTreeVO } from '@bika/types/node/vo';
import type { SpaceAdminAbilities } from '@bika/types/permission/vo';
import {
  type SpaceUIDrawer,
  SpaceUIDrawerSchema,
  type SpaceUIModal,
  SpaceUIModalSchema,
  SpaceUICopilotSchema,
} from '@bika/types/space/bo';
import { SpaceContext, type ISpaceContext } from '@bika/types/space/context';
import { useShareContext } from '@bika/types/space/context';
import type { SpaceMyRedDotsVO, SpaceRenderVO } from '@bika/types/space/vo';
import type { MemberVO, TeamVO } from '@bika/types/unit/vo';
import type { ISSEEventOnlineUser } from '@bika/types/user/bo';
import { useGlobalContext, useGlobalState } from '@bika/types/website/context';
import type { GridApi } from '@bika/ui/database/types';
import { useUIFrameworkContext } from '@bika/ui/framework';
import { useSnackBar } from '@bika/ui/snackbar';
import { findNode, updateItemOnRootNodeTree } from '../sidebar/utils/match-template-util';
import { useTalksDB } from '../talks-db';

export const CONST_TOAST_DURATION = 3000;

interface ISpaceVOProvider {
  children: React.ReactNode;
  space: SpaceRenderVO;
  isLoading: boolean;
  refetch: () => void;
  hasFab?: boolean;
}

/**
 * 加载space后，再开始加载其它元素
 *
 * @param props
 * @returns
 */
export function SpaceVOApiProvider(props: ISpaceVOProvider) {
  const globalContext = useGlobalContext();
  const { toast } = useSnackBar();

  const { trpcQuery, trpc } = useApiCaller();
  const utils = trpcQuery.useUtils();

  const { mutateAsync: updateSpace } = trpcQuery.space.update.useMutation();

  const [api] = useGlobalState<GridApi<RecordRenderVO>>('AG_GRID_API');
  const spaceId = props.space.id;
  const space = props.space;
  const refetchSpace = props.refetch;
  const isLoadingSpace = props.isLoading;
  const [rootNode, setRootNode] = React.useState<NodeTreeVO | undefined>(undefined);
  const [privateRootNode, setPrivateRootNode] = React.useState<NodeTreeVO | undefined>(undefined);

  const { sharing } = useShareContext();
  const talksDB = useTalksDB(spaceId);

  // fetch my info in space
  const [me, setMe] = useState<MemberVO | null>(null);
  const {
    data: myInfo,
    isLoading: isLoadingMyInfo,
    refetch: refetchMyInfo,
  } = trpcQuery.my.info.useQuery(
    {
      spaceId,
    },
    {
      retry: false,
      refetchOnWindowFocus: false,
      enabled: !sharing,
    },
  );
  useEffect(() => {
    if (myInfo) {
      setMe(myInfo);
    }
  }, [myInfo]);

  // useEffect(() => {
  //   trpc.unit.outgoingContactList
  //     .query({
  //       spaceId,
  //       type: 'Role',
  //       pageSize: 50,
  //     })
  //     .then((res) => {
  //       if (res?.data?.length > 0) {
  //         setHasOutgoingList(true);
  //       }
  //     })
  //     .catch((error) => {
  //       if (error?.message !== 'Outgoing webhook not found') {
  //         setHasOutgoingList(true);
  //       }
  //     });
  // }, []);

  const {
    data: queryGuestRootNode,
    isLoading: isLoadingQueryGuestRootNode,
    refetch: refetchQueryGuestRootNode,
  } = trpcQuery.guest.nodes.useQuery(
    {
      spaceId,
    },
    {
      retry: false,
      refetchOnWindowFocus: false,
      enabled: !sharing && myInfo !== undefined && myInfo?.isGuest,
    },
  );

  // fetch space root node, (depth: 2)
  const rootNodeEabled = !sharing && myInfo !== undefined && !myInfo?.isGuest;
  const {
    data: nodeData = {
      space: null,
      private: null,
    },
    isLoading: _isLoadingRootNode,
    refetch: refetchRootNode,
    isError: isRootNodeErrored,
  } = trpcQuery.node.root.useQuery(
    {
      spaceId,
    },
    {
      retry: false,
      refetchOnWindowFocus: false,
      enabled: rootNodeEabled,
    },
  );
  const isLoadingRootNode = rootNodeEabled && !isRootNodeErrored ? _isLoadingRootNode : false;

  useEffect(() => {
    if (queryGuestRootNode) {
      const guestRootNode: NodeTreeVO = {
        id: 'root',
        name: 'root',
        type: 'ROOT',
        sharing: false,
        hasShareLock: false,
        hasPermissions: false,
        children: queryGuestRootNode,
      };
      setRootNode(guestRootNode);
    }
  }, [queryGuestRootNode]);

  const { space: querySpaceRootNode, private: queryPrivateRootNode } = nodeData;

  useEffect(() => {
    if (querySpaceRootNode) {
      setRootNode(querySpaceRootNode);
    } else {
      setRootNode({
        id: 'root',
        name: 'root',
        type: 'ROOT',
        sharing: false,
        hasShareLock: false,
        hasPermissions: false,
        children: [],
      });
    }
  }, [querySpaceRootNode]);

  useEffect(() => {
    if (queryPrivateRootNode) {
      setPrivateRootNode(queryPrivateRootNode);
    }
  }, [queryPrivateRootNode]);

  // fetch my role permission in space
  const [permission, setPermission] = useState<SpaceAdminAbilities | null>(null);
  const {
    data: remotePermission,
    isLoading: isLoadingPermission,
    refetch: _refetchPermission,
  } = trpcQuery.my.permission.useQuery(
    { spaceId },
    {
      refetchOnWindowFocus: false,
      enabled: !sharing,
    },
  );

  useEffect(() => {
    if (remotePermission) {
      setPermission(remotePermission);
    }
  }, [remotePermission]);

  // fetch root team of space
  const [rootTeam, setRootTeam] = useState<TeamVO | null>(null);
  const {
    data: remoteRootTeamData,
    isLoading: isLoadingRootTeam,
    refetch: _refetchRootTeam,
  } = trpcQuery.team.root.useQuery(
    { spaceId },
    {
      refetchOnWindowFocus: false,
      enabled: !sharing,
    },
  );

  const {
    data: shortcutNode,
    isLoading: isLoadingShortcutNode,
    refetch: refetchShortcutNode,
  } = trpcQuery.my.shortcutNode.useQuery(
    {
      spaceId,
    },
    {
      refetchOnWindowFocus: false,
      enabled: !sharing,
    },
  );

  useEffect(() => {
    if (remoteRootTeamData) {
      setRootTeam(remoteRootTeamData);
    }
  }, [remoteRootTeamData]);

  // fetch root team of space
  const [guestRootTeam, setGuestRootTeam] = useState<TeamVO | null>(null);
  const {
    data: remoteGuestRootTeamData,
    isLoading: isLoadingGuestRootTeam,
    refetch: _refetchGuestRootTeam,
  } = trpcQuery.team.root.useQuery(
    { spaceId, isGuest: true },
    {
      refetchOnWindowFocus: false,
      enabled: !sharing,
    },
  );

  useEffect(() => {
    if (remoteGuestRootTeamData) {
      setGuestRootTeam(remoteGuestRootTeamData);
    }
  }, [remoteGuestRootTeamData]);

  const { hasFab = false } = props;

  const changeSpaceName = async (name: string) => {
    await updateSpace({ id: spaceId, data: { name } });
    await refetchSpace();
  };

  const { searchParams } = useUIFrameworkContext();

  const getUIModal = () => {
    const modalStr = searchParams.get('modal');
    if (!modalStr) return null;
    const modal = JSON.parse(decodeURIComponent(atob(modalStr as string)));
    return SpaceUIModalSchema.parse(modal as unknown);
  };

  const getUIDrawer = () => {
    const drawerStr = searchParams.get('drawer');
    if (!drawerStr) return null;
    const modal = JSON.parse(decodeURIComponent(atob(drawerStr as string)));
    return SpaceUIDrawerSchema.parse(modal as unknown);
  };

  const showUIDrawer = (drawer: SpaceUIDrawer | null) => {
    // 部分情况下会失效 原因未知 换成window自带的API
    // searchParams.set('drawer', drawer ? btoa(encodeURIComponent(JSON.stringify(drawer))) : undefined);

    // 获取当前 URL 的搜索参数
    const urlParams = new URLSearchParams(window.location.search);

    // 设置 drawer 参数
    if (drawer) {
      urlParams.set('drawer', btoa(encodeURIComponent(JSON.stringify(drawer))));
    } else {
      urlParams.delete('drawer'); // 如果 drawer 为空，删除参数
    }

    // 更新 URL（不刷新页面）
    const newUrl = `${window.location.pathname}?${urlParams.toString()}`;
    window.history.pushState({}, '', newUrl);
  };

  /**
   * router，设置query string，控制UIModal的隐显
   * @param key
   * @param value
   */
  const showUIModal = useCallback(
    (modal: SpaceUIModal | null) => {
      searchParams.set('modal', modal ? btoa(encodeURIComponent(JSON.stringify(modal))) : undefined);
    },
    [searchParams],
  );

  // fetch my reddots
  const {
    data: remoteMyReddots,
    isLoading: isLoadingMyReddots,
    refetch: refetchReddots,
  } = trpcQuery.my.reddots.useQuery(
    { spaceId },
    {
      onSuccess: (successRedDots: SpaceMyRedDotsVO) => {
        if (
          successRedDots.forcePopupMissions &&
          successRedDots.forcePopupMissions.length > 0 &&
          space?.settings?.onboardingStage === 'DONE'
        ) {
          // 发现有强制mission，立即弹出modal
          showUIModal({
            type: 'mission',
            missionId: successRedDots.forcePopupMissions[0].id,
          });
        }
      },
      enabled: !sharing,
    },
  );

  // 更新目录树
  const setSectionNodeTree = (nodeId: string) => {
    trpc.node.position.query({ id: nodeId }).then((sectionNodeTree) => {
      if (sectionNodeTree.scope === 'PRIVATE' && privateRootNode) {
        const newRoot = updateItemOnRootNodeTree(sectionNodeTree, privateRootNode);
        setPrivateRootNode(newRoot);
      }
      if (sectionNodeTree.scope === 'SPACE' && rootNode) {
        const newRoot = updateItemOnRootNodeTree(sectionNodeTree, rootNode);
        setRootNode(newRoot);
      }
    });
  };

  const loading =
    !sharing &&
    (isLoadingSpace ||
      isLoadingMyInfo ||
      isLoadingRootNode ||
      isLoadingMyReddots ||
      isLoadingPermission ||
      isLoadingRootTeam ||
      isLoadingGuestRootTeam);

  const refetch = useCallback(async () => {
    await Promise.all([refetchSpace(), refetchRootNode()]);
  }, [refetchRootNode, refetchSpace]);

  const {
    authContext: { onlineSessions },
  } = globalContext;

  useEffect(() => {
    if (!talksDB.isLoading) {
      const auth = globalContext.authContext;
      auth.registerSseHandler('talk', (data) => {
        assert(data.name === 'talk');
        // 只有当 memberId 匹配当前空间的 member 时才更新数据
        if (data.memberId && me && data.memberId === me.id) {
          talksDB.update(data.talk);
          // localStorage.setItem('LAST_TIME_UPDATE_TALK', Date.now().toString());
          // 触发自定义事件，因为 storage 事件在同一标签页内不会触发
          window.dispatchEvent(
            new CustomEvent('localStorageChange', {
              detail: { key: 'LAST_TIME_UPDATE_TALK', value: Date.now().toString() },
            }),
          );
        }
      });
    }
  }, [talksDB, globalContext.authContext, me]);

  useEffect(() => {
    const auth = globalContext.authContext;
    auth.registerSseHandler('snackbar', (data) => {
      if (data.name !== 'snackbar') {
        return;
      }
      const isCloseable = Boolean((data as { closeable?: boolean }).closeable ?? true);
      const variant = !isCloseable ? 'persist' : 'info';
      if (variant === 'persist') {
        toast(data.text, {
          variant,
          id: 'refresh-update',
          autoHideDuration: isCloseable ? CONST_TOAST_DURATION : undefined,
          action: {
            text: data.action?.text,
            onClick: () => {
              let gridApi = api;
              if (!api) {
                console.warn('api not found, retrieve it from window');
                gridApi = window.AG_GRID_API as GridApi<RecordRenderVO>;
              }

              gridApi?.refreshServerSide({
                purge: true,
              });
            },
          },
        });
      } else {
        const onClick = () => {
          if (data.action?.showModal) {
            return showUIModal(data.action.showModal);
          }
          return null;
        };
        toast(data.text, {
          variant,
          autoHideDuration: isCloseable ? CONST_TOAST_DURATION : undefined,
          action: {
            text: data.action?.text,
            onClick,
          },
        });
      }
    });
    auth.registerSseHandler('space-sidebar', async (data) => {
      if (data.name !== 'space-sidebar') {
        return;
      }
      // 当前space Id更新事件符合，否则忽略
      if (data.spaceId === spaceId) {
        if (data.refreshInfos.includes('REDDOTS')) {
          refetchReddots();
        }
      }
    });

    auth.registerSseHandler('node', async (data) => {
      console.log('SSE node', data);
      if (data.name !== 'node') {
        return;
      }
      if (data.spaceId === spaceId && data.nodeId) {
        const detail = await utils.node.detail.fetch({ id: data.nodeId });
        if (detail) {
          console.log('SSE node detail', detail);
          const { success, data: treeVo, error } = NodeTreeVOSchema.safeParse(detail);
          if (success) {
            if (detail.scope === 'SPACE' && rootNode) {
              if (treeVo.id === rootNode.id) {
                refetchRootNode();
              } else {
                const newTree = updateItemOnRootNodeTree(treeVo, rootNode);
                console.log('SSE node public newTree', newTree);
                if (newTree.children?.length === 0) {
                  return;
                }

                setRootNode(newTree);
              }
            }
            if (detail.scope === 'PRIVATE' && privateRootNode) {
              const newTree = updateItemOnRootNodeTree(treeVo, privateRootNode);
              console.log('SSE node private newTree', newTree);
              setPrivateRootNode(newTree);
            }
          } else {
            console.log('SSE node public error', error);
          }
          utils.node.talk.invalidate();
        }
        utils.node.detail.invalidate({ id: data.nodeId });
        utils.node.folderDetail.invalidate({ id: data.nodeId });
      }
    });

    auth.registerSseHandler('online-user', (data) => {
      if (data.name !== 'online-user') {
        return;
      }
      const onlineUserData = data as ISSEEventOnlineUser;
      auth.setOnlineSessions([
        ...onlineSessions,
        {
          onlineSessionId: onlineUserData.onlineSessionId,
          user: onlineUserData.user,
        },
      ]);

      // eslint-disable-next-line no-console
      if (globalContext.appEnv !== 'PRODUCTION')
        return () => {
          auth.unregisterSseHandler('online-user');
          auth.unregisterSseHandler('node');
          auth.unregisterSseHandler('space-sidebar');
          auth.unregisterSseHandler('snackbar');
        };
    });
  }, [
    globalContext.authContext,
    refetchRootNode,
    rootNode,
    onlineSessions,
    refetch,
    spaceId,
    toast,
    showUIModal,
    api,
    me,
  ]);

  const context: ISpaceContext = {
    data: space!,
    myInfo: me!,
    refetchMyInfo,
    rootTeam: rootTeam!,
    guestRootTeam: guestRootTeam!,
    permission,
    redDots: remoteMyReddots!,
    useShortcutsNode: () => ({
      shortcutNode,
      setShortcutNode: () => {
        // TODO: 排序使用
      },
      refetch: refetchShortcutNode,
      isLoading: isLoadingShortcutNode,
    }),
    useRootNode: (scope?: NodeResourceScope) => {
      if (scope === 'PRIVATE') {
        return {
          rootNode: privateRootNode!,
          setRootNode: setPrivateRootNode,
          refetch: refetchRootNode, // 基本用不到
          isLoading: isLoadingRootNode,
          findNode: (nodeId: string) => findNode(nodeId, privateRootNode!),
          setSectionNodeTree,
        };
      }
      return {
        rootNode: rootNode!, // 存在帧间隙，rootNode还没来得及赋值
        setRootNode,
        refetch: refetchRootNode,
        isLoading: isLoadingRootNode,
        findNode: (nodeId: string) => findNode(nodeId, rootNode!),
        setSectionNodeTree,
      };
    },
    // rootNode: rootNode!,
    // setRootNode,
    // refetchRootNode,
    getUIModal,
    showUIModal,
    getUIDrawer,
    showUIDrawer,
    showAICopilot: (copilot) => {
      searchParams.set('copilot', copilot ? btoa(encodeURIComponent(JSON.stringify(copilot))) : undefined);
    },
    getAICopilot: () => {
      const drawerStr = searchParams.get('copilot');
      if (!drawerStr) return null;
      const modal = JSON.parse(decodeURIComponent(atob(drawerStr as string)));
      return SpaceUICopilotSchema.parse(modal as unknown);
    },

    refetch,
    changeSpaceName,
  };

  // 放到全局state，供跨模块使用
  const [_, setGlobalSpaceContext] = useGlobalState<ISpaceContext>('SPACE', context);
  useEffect(() => {
    setGlobalSpaceContext(context);
  }, [props.space]);

  if (loading) {
    return <PageSkeleton loading={loading} />;
  }

  return (
    <SpaceContext.Provider value={context}>
      <SpaceNodeResourceApiProvider>
        <Helmet>
          {/* <title>{nodeDetail.name}</title> */}
          <title>{space.name || space.id}</title>
        </Helmet>
        <meta name="description" content={(space as any).description} />
        <GuideIntentUI
          onClick={async (id: string) => {
            if (id === 'USER_WIZARD_GUIDE') {
              try {
                await trpc.space.update.mutate({
                  id: space.id,
                  data: {
                    settings: { onboardingStage: 'DONE' },
                  },
                });
              } catch (error) {
                console.error('Error updating space settings:', error);
              }
              console.log('GuideIntentUI clicked');
            }
          }}
        />
        {props.children}
        {hasFab && (
          <FabComponent
            onClick={() => {
              showUIModal({ type: 'ai-commander' });
            }}
          />
        )}
      </SpaceNodeResourceApiProvider>
    </SpaceContext.Provider>
  );
}
