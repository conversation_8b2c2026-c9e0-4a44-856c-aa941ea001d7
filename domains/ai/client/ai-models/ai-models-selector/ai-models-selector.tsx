import React from 'react';
import {
  AILanguageModelClientConfig,
  getAILanguageModelClientConfigs,
} from '@bika/contents/config/client/ai/ai-model-client-config';
import { useLocale } from '@bika/contents/i18n';
import type { PresetLanguageAIModelDef, IAIModelSelectBO } from '@bika/types/ai/bo';
import { useGlobalContext } from '@bika/types/website/context';
import { SelectInput } from '@bika/ui/shared/types-form/select-input';
import { OPENAI_MODELS } from './ai-model-custom-manual-openai-select-options';
import { AIModelConfigCustom } from './ai-model-selector-custom';

interface Props {
  value: IAIModelSelectBO;
  onChange: (value: IAIModelSelectBO) => void;
  disableCustom?: boolean;
}

export function AIModelsSelector(props: Props) {
  const locale = useLocale();
  const { i } = locale;

  const globalContext = useGlobalContext();

  // 一种 preset 优先的表单策略
  const [configType, setConfigType] = React.useState<PresetLanguageAIModelDef | 'custom' | 'auto'>('auto');

  const presetModel = props.value.kind === 'preset' ? props.value.model : undefined;

  React.useEffect(() => {
    if (props.value.kind === 'custom') {
      setConfigType('custom');
    } else if (props.value.kind === 'preset' && presetModel) {
      setConfigType(presetModel);
    } else {
      setConfigType('auto');
    }
  }, [props.value.kind, presetModel]);

  const options = React.useMemo(() => {
    const opts = [];
    opts.push({ label: 'Auto', value: 'auto', icon: '/assets/ai/model/auto.png' });

    if (globalContext.appEnv !== 'PRODUCTION') {
      opts.push({ label: 'Mock', value: 'mock', icon: AILanguageModelClientConfig.mock.logo });
    }

    const configs = getAILanguageModelClientConfigs('agent');

    for (const { key, config } of configs) {
      opts.push({ label: i(config.name), value: key, icon: config.logo });
    }

    if (props.disableCustom !== true) {
      opts.push({ label: 'Custom', value: 'custom', icon: '/assets/ai/model/custom.png' });
    }

    return opts;
  }, [i]);

  return (
    <>
      <SelectInput
        label={'AI Model'}
        options={options}
        onChange={(newVal) => {
          if (!newVal) {
            throw new Error('AI Model is required');
          }

          if (newVal === 'custom') {
            setConfigType('custom');
            props.onChange({
              ...props.value,
              kind: 'custom',
              custom: {
                type: 'manual',
                provider: {
                  type: 'OPENAI',
                  apiKey: '',
                  baseUrl: undefined,
                },
                modelId: OPENAI_MODELS[0],
              },
            });
          } else if (newVal === 'auto') {
            setConfigType(newVal);
            props.onChange({
              ...props.value,
              kind: 'auto',
            });
          } else {
            setConfigType(newVal);
            props.onChange({
              ...props.value,
              kind: 'preset',
              model: newVal as PresetLanguageAIModelDef,
            });
          }
        }}
        value={configType}
      />

      {props.value.kind === 'custom' && (
        <AIModelConfigCustom
          value={props.value}
          onChange={(newVal) => {
            props.onChange(newVal);
          }}
        />
      )}
    </>
  );
}
