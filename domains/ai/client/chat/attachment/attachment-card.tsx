import { Typography } from '@mui/joy';
import type React from 'react';
import { useState } from 'react';
import { Box, Stack } from '@bika/ui/layouts';
import { Loading } from '@bika/ui/preview-attachment/loading';
import { EllipsisText } from '@bika/ui/text/ellipsis';

// Utility function to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / k ** i).toFixed(1))} ${sizes[i]}`;
};

interface AttachmentCardProps {
  id: string;
  name: string;
  contentType: string;
  fileSize?: number;
  imageSrc: string;
}

export const AttachmentCard: React.FC<AttachmentCardProps> = ({ id, name, contentType, fileSize, imageSrc }) => {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  const handleImageError = () => {
    setImageLoading(false);
    setImageError(true);
  };

  return (
    <Stack
      direction="row"
      sx={{
        flex: '1 1 auto',
        paddingLeft: '16px',
        paddingRight: '16px',
        overflowX: 'hidden',
        height: '100%',
        alignItems: 'center',
      }}
    >
      <Box
        sx={{
          width: '32px',
          height: '32px',
          flex: '0 0 32px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        {imageLoading && <Loading currentColor />}
        <img
          src={imageSrc}
          alt={name}
          className="w-[32px] h-[32px] object-cover"
          style={{
            display: imageLoading ? 'none' : 'block',
            opacity: imageError ? 0.5 : 1,
          }}
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
      </Box>
      <Box
        sx={{
          width: '100%',
          paddingLeft: '12px',
          padding: '8px',
        }}
      >
        <EllipsisText>
          <Typography
            level="b3"
            sx={{
              paddingRight: '24px',
              color: 'var(--text-primary)',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              marginBottom: '2px',
            }}
          >
            {name}
          </Typography>
        </EllipsisText>

        <Typography
          level="b4"
          sx={{
            color: 'var(--text-secondary)',
          }}
        >
          {fileSize ? formatFileSize(fileSize) : ''}
        </Typography>
      </Box>
    </Stack>
  );
};
