import type { Message } from '@ai-sdk/ui-utils';
import React from 'react';
import { useApiCaller } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import type { AIIntentParams } from '@bika/types/ai/bo';
import { toAISDKMessage } from '@bika/types/ai/bo';
import type { AIWizardVO } from '@bika/types/ai/vo';
import type { ISpaceContext } from '@bika/types/space/context';
import type { Locale } from '@bika/types/system';
import { useGlobalState } from '@bika/types/website/context';
import { Button } from '@bika/ui/button-component';
import AddOutlined from '@bika/ui/icons/components/add_outlined';
import { Skeleton } from '@bika/ui/skeleton';
import { snackbarShow } from '@bika/ui/snackbar';
import { type AIChatHandle, AIChatUIRenderer } from './ai-chat-ui';
import type { IAIChatInputState, AIChatInputConfig } from './hooks/use-ai-chat-cache';

export interface AIChatViewProps {
  inputState: IAIChatInputState;
  config?: AIChatInputConfig;

  // initUserMessage?: string;
  // use previous dialog, or new dialog?
  // initChatId?: string;
  // if new dialog, what's the intent type?
  initAIIntent?: AIIntentParams;

  //  如果设置了这个，human，一旦 wizard 初始化完成，马上发起 message
  // initStartMessage?: string;

  onClose?: () => void;

  // 是否内置modal component
  withModal?: boolean;
  // 是否强制语言？会在 useChat的 custom body 传入
  forceLocale?: Locale;
  // 是否侧边栏模式
  displayMode?: 'COPILOT' | 'MODAL' | 'VIEW';

  // context?: AIChatContextVO[];
  // allowContextMenu?: AIChatContextVO['type'][];
  setStage?: (stage: 'welcome' | 'chat') => void;
  skillsetIcons?: React.ReactNode;
}

/**
 * 使用Streaming AI Chat View 的Wizard2
 *
 * @returns
 */
export function AIChatView(props: AIChatViewProps) {
  const { setStage } = props;
  const { trpc } = useApiCaller();
  const { lang, t } = useLocale();
  const chatUIHandle = React.useRef<AIChatHandle>(null);
  const [globalSpaceContext] = useGlobalState<ISpaceContext>('SPACE');
  const [wizard, setWizard] = React.useState<AIWizardVO | null>(null);
  const [loading, setLoading] = React.useState<boolean>(true);

  const [initError, setInitError] = React.useState<string | null>(null);

  // init start message 是否已经被执行完毕
  const initStartMessageExecuted = React.useRef(false); // 这里有 ref，是避免 useEffect 开发环境下的副作用执行两次

  const initDialog = React.useCallback(
    async (wizardId?: string) => {
      setLoading(true);
      const data = wizardId
        ? await trpc.ai.fetchWizard.query({ wizardId })
        : await trpc.ai.newWizard.query({
            spaceId: globalSpaceContext?.data.id,
            intent: props.initAIIntent || {
              type: 'SEARCH', // default ai intent
            },
          });

      if (!wizardId) {
        snackbarShow({
          content: t.wizard.new_wizard_created,
          color: 'success',
        });
      }
      props.inputState.setChatId(data.id);
      setLoading(false);
      setWizard(data as AIWizardVO);
      setInitError(null);
    },
    [globalSpaceContext?.data.id],
  );

  // 初始化 Dialog
  React.useEffect(() => {
    const fetchWizard = async () => {
      try {
        await initDialog(props.inputState.chatId);
      } catch (error) {
        setLoading(false);
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes('not found') && setStage) {
          // 清除缓存的 chatId
          props.inputState.clear();
          setStage('welcome'); // 切换到欢迎界面
          return;
        }
        setInitError(errorMessage);
      }
    };
    fetchWizard();
  }, [props.inputState.chatId]);

  // 初始化完成后，马上发一条消息
  React.useEffect(() => {
    if (
      wizard &&
      initStartMessageExecuted.current === false &&
      (props.inputState.input || (props.inputState.contexts && props.inputState.contexts.length > 0))
    ) {
      //  如果有 initStartMessage，马上发一条消息
      chatUIHandle.current?.doAppendMessage(props.inputState.input ?? '');

      initStartMessageExecuted.current = true;
    }
    // 如果有队列 queue，也么上发送一条消息
  }, [initStartMessageExecuted, props.inputState.input, props.inputState.contexts, wizard]);

  // 初始化显示的消息
  const initialMessages = React.useMemo(() => {
    const msgs: Message[] = [];
    if (wizard) {
      let i = 0;
      for (const msg of wizard.messages) {
        const aiSDKMessage = toAISDKMessage(msg);

        msgs.push(aiSDKMessage);
        i++;
      }
    }
    return msgs;
  }, [wizard, lang, props.forceLocale]);

  if (initError) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <div className="text-red-500 mb-4">{initError}</div>
        <Button
          onClick={() => {
            initDialog();
          }}
          startDecorator={<AddOutlined color="var(--text-secondary)" />}
        >
          {t.ai.new_chat}
        </Button>
      </div>
    );
  }

  if (loading || !wizard) return <Skeleton pos="CHAT" />;

  return (
    <>
      <AIChatUIRenderer
        inputState={props.inputState}
        ref={chatUIHandle}
        // options={wizard.options}
        displayMode={props.displayMode}
        config={{ ...props.config, options: [...(props.config?.options || []), ...(wizard.options || [])] }}
        disabled={wizard.resolutionStatus === 'SUCCESS'}
        api={'/api/ai/chat'}
        initialMessages={initialMessages}
        skillsetIcons={props.skillsetIcons}
        onFinish={(_message) => {
          // trpc.ai.fetchWizard.query({ wizardId: wizard.id }).then((data) => setWizard(data as AIWizardVO));
        }}
        // textFilter={(text) => {
        //   if (text.startsWith('/resolve:')) {
        //     return `🖱️🖱️🖱️`; //  ${text}`;
        //   }
        //   return text;
        // }}
        chatId={wizard.id}
        customBody={
          wizard
            ? {
                forceLocale: props.forceLocale,
                // initUserMessage: props.initUserMessage,
                // use previous dialog, or new dialog?
                // if new dialog, what's the intent type?
                initAIIntent: props.initAIIntent,
              }
            : undefined
        }
      />
    </>
  );
}
