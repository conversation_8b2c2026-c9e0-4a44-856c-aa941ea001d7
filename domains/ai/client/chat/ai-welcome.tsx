import clxs from 'classnames';
import React from 'react';
import { type AIChatInputConfig, type IAIChatInputState } from './hooks/use-ai-chat-cache';
import { AIChatInput, type AIChatInputRefHandle } from './input/ai-chat-input';
import { PromptIntentUI } from '../wizard/intents-ui/prompt-intent-ui';

export interface AIWelcomeProps {
  inputState: IAIChatInputState;
  config?: AIChatInputConfig;
  /**
   * Welcome 页，有两种样式，取决于中间是否有内容显示
   *
   *  样式 1：输入框垂直居中，没有其它东西展示
   *  样式 2：输入框置底，中间显示其它东西
   */
  centerContent?: React.ReactNode;
  onSubmit: (state: IAIChatInputState) => void;

  initPrompts?: string[];

  title: string | React.ReactNode;
  description?: string | React.ReactNode;
  // Welcome 的 options 要手工传入，因为还没激活 wizard
  // options?: AIChatOption[];
  // allowContextMenu?: AIChatContextVO['type'][];
  customBottom?: React.ReactNode;
  avatars?: React.ReactNode;
  skillsetIcons?: React.ReactNode;
  fullscreen?: boolean;
  placeholder?: string;
}

/**
 *
 * @param props
 * @returns
 */
export function AIWelcome(props: AIWelcomeProps) {
  const { avatars = null, fullscreen = true } = props;
  const [input, setInput] = React.useState('');
  const inputRef = React.useRef<AIChatInputRefHandle>(null);
  // const inputState = useAIChatInputState();
  const inputState = props.inputState;

  return (
    <div
      className={clxs('flex flex-col items-center justify-center pt-[20px] pb-[40px] px-4', {
        'min-h-screen': fullscreen,
        'h-full': !fullscreen,
      })}
    >
      <div className="truncate max-w-[720px] text-[clamp(1.5rem,3vw,2rem)] font-[600] mb-4 leading-[1.2] text-center bg-gradient-to-r from-[#4FD2F6] via-[#886AFF] to-[#EE478F] text-transparent bg-clip-text">
        {props.title}
      </div>
      <div className="w-full max-w-[720px] font-[700] text-4xl text-center leading-[40px] text-[--text-primary] bg-gradient-to-b from-[--text-primary] to-[--text-secondary] bg-clip-text text-transparent line-clamp-2 break-words">
        {props.description}
      </div>
      {avatars}
      <div className="w-full max-w-[720px] mt-[16px]">
        <AIChatInput
          inputState={inputState}
          config={props.config}
          ref={inputRef}
          onChange={undefined}
          disabled={false}
          handleSubmit={() => {
            props.onSubmit(inputState);
          }}
          sx={
            {
              // paddingX: '0!important',
            }
          }
          placeholder={props.placeholder}
          skillsetIcons={props.skillsetIcons}
        />
        {props.initPrompts && (
          <PromptIntentUI
            onClick={(prompt) => {
              inputState.setInput(prompt);
              props.onSubmit(inputState);
            }}
            disabled={false}
            intentUI={{
              type: 'PROMPT',
              prompts: props.initPrompts,
            }}
          />
        )}
      </div>
      {props.customBottom}
    </div>
  );
}
