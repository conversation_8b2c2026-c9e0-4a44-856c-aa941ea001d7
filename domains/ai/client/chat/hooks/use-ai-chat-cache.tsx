import assert from 'assert';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import type { AIChatContextVO, AIChatOption, AIChatModelOption } from '@bika/types/ai/vo';
import type { SkillsetSelectBO } from '@bika/types/skill/bo';
import type { SkillsetVO } from '@bika/types/skill/vo';
import type { TalkBO } from '@bika/types/space/bo';
import { useSpaceId } from '@bika/types/space/context';
import { useGlobalIndexedDB, type IDataAIChatSession } from '@bika/types/website/context';

export type AIChatSelector =
  | { type: 'agent'; spaceId: string; agent: TalkBO }
  | { type: 'copilot'; spaceId: string; copilot: TalkBO }
  | { type: 'space'; spaceId: string };

export type IAIChatSessionContext = IDataAIChatSession & {
  setSkillsets: (skillsets: SkillsetSelectBO[]) => void;
  setContexts: (contexts: AIChatContextVO[] | ((prev: AIChatContextVO[] | undefined) => AIChatContextVO[])) => void;
  setInput: (inputText: string) => void;
  setChatId: (chatId: string | undefined) => void;
  setOption: (option: AIChatOption | undefined) => void;
  clear: () => void;
};
// 只有 state
export type IAIChatInputState = Omit<IAIChatSessionContext, 'id' | 'updatedAt'>;

export type AIChatInputConfig = {
  // 模型选项
  modelOptions?: AIChatModelOption[];

  //  固定化一些 consexts?
  contexts?: AIChatContextVO[];
  options?: AIChatOption[];
  skillsets?: SkillsetVO[];
  allowContextMenu?: AIChatContextVO['type'][];

  // 文件上传接受的文件类型
  uploadAccept?: string;
};
/**
 * Helper function to create a composite key for IndexedDB storage
 */
export function getSelectorKey(selector: AIChatSelector): string {
  if (selector.type === 'agent') {
    if (selector.agent.type === 'expert') {
      return `${selector.spaceId}:${selector.type}:expert:${selector.agent.expertKey}`;
    }
    if (selector.agent.type === 'node') {
      return `${selector.spaceId}:${selector.type}:node:${selector.agent.nodeId}`;
    }
  }
  if (selector.type === 'copilot') {
    if (selector.copilot.type === 'node') {
      return `${selector.spaceId}:${selector.type}:node:${selector.copilot.nodeId}`;
    }
  }
  if (selector.type === 'space') {
    return `${selector.spaceId}:${selector.type}`;
  }
  throw new Error(`Unsupported selector type: ${selector.type}`);
}

/**
 * Hook to manage AI chat contexts using IndexedDB
 */
export function useAIChatSession(selector: AIChatSelector): {
  session: IAIChatSessionContext;
  isLoading: boolean;
} {
  const spaceId = useSpaceId();
  const { db: globalIndexedDB, isLoading: isDBInitializing } = useGlobalIndexedDB();
  const fetchRef = React.useRef(true);

  const selectorKey = useMemo(() => (spaceId ? getSelectorKey(selector) : ''), [spaceId, JSON.stringify(selector)]);

  const [data, setData] = useState<IDataAIChatSession>(() => ({
    id: selectorKey,
    updatedAt: new Date(),
  }));

  useEffect(() => {
    setData((prev) => ({
      ...prev,
      id: selectorKey,
    }));
  }, [selectorKey]);

  useEffect(() => {
    if (!isDBInitializing) {
      fetchRef.current = true;
      // Fetch data from IndexedDB
      globalIndexedDB
        ?.get('AI_CHAT_SESSIONS', selectorKey)
        .then((cacheData) => {
          if (cacheData) {
            setData(cacheData);
          } else {
            // 切换 AI node 时，indexedDB 里没有数据，初始化一个空的 session
            setData({
              id: selectorKey,
              updatedAt: new Date(),
              input: '',
              skillsets: [],
              contexts: [],
              chatId: undefined,
              option: undefined,
            });
          }
          fetchRef.current = false;
        })
        .catch((error) => {
          console.error('Failed to fetch chat data from IndexedDB:', error);
          fetchRef.current = false;
        });
    }
  }, [spaceId, globalIndexedDB, isDBInitializing, selectorKey]);

  // Save data to IndexedDB when they change
  const saveToIndexedDB = useCallback(
    async (updates: Partial<IDataAIChatSession> = {}) => {
      try {
        assert(data);
        assert(isDBInitializing === false, 'useAIChatSession: isLoading should be false before saving');
        assert(globalIndexedDB, 'useAIChatSession: globalIndexedDB should be initialized');

        const newData: IDataAIChatSession = { ...data, ...updates };

        await globalIndexedDB.put('AI_CHAT_SESSIONS', newData);
      } catch (error) {
        console.error('Failed to save chat data to IndexedDB:', error);
      }
    },
    [data, globalIndexedDB, isDBInitializing],
  );

  return {
    session: {
      ...data,
      setOption: (option: AIChatOption | undefined) => {
        setData((prev) => {
          const newData: IDataAIChatSession = { ...prev, option };
          saveToIndexedDB(newData);
          return newData;
        });
      },
      setContexts: (contexts: AIChatContextVO[] | ((prev: AIChatContextVO[] | undefined) => AIChatContextVO[])) => {
        setData((prev) => {
          const newContexts = typeof contexts === 'function' ? contexts(prev.contexts ?? []) : contexts;
          const newData: IDataAIChatSession = {
            ...prev,
            contexts: newContexts,
          };
          saveToIndexedDB(newData);
          return newData;
        });
      },
      setSkillsets: (skillsets: SkillsetSelectBO[]) => {
        setData((prev) => {
          const newData: IDataAIChatSession = { ...prev, skillsets };
          saveToIndexedDB(newData);
          return newData;
        });
      },
      setChatId: (chatId: string | undefined) => {
        setData((prev) => {
          const newData: IDataAIChatSession = { ...prev, chatId };
          saveToIndexedDB(newData);
          return newData;
        });
      },
      setInput: (inputText: string) => {
        setData((prev) => {
          const newData: IDataAIChatSession = { ...prev, input: inputText };
          saveToIndexedDB(newData);
          return newData;
        });
      },
      clear: () => {
        setData({
          id: selectorKey,
          updatedAt: new Date(),
          input: '',
          skillsets: [],
          contexts: [],
          chatId: undefined,
          option: undefined,
        });
        saveToIndexedDB();
      },
    },
    isLoading: isDBInitializing || fetchRef.current,
  };
}

/**
 *
 * Session 移除 id，只有 state，内存 memory state 版，不持久化，临时在内存
 *
 * @returns
 */
export function useAIChatInputState(): IAIChatInputState {
  const [input, setInput] = React.useState('');
  const [option, setOption] = React.useState<AIChatOption | undefined>(undefined);
  const [skillsets, setSkillsets] = React.useState<SkillsetSelectBO[]>([]);
  const [contexts, setContexts] = React.useState<AIChatContextVO[]>([]);
  const [chatId, setChatId] = React.useState<string | undefined>(undefined);

  return {
    input,
    skillsets,
    contexts,
    chatId,
    option,
    setSkillsets,
    setContexts,
    setInput,
    setChatId,
    setOption,
    clear: () => {
      setInput('');
      setSkillsets([]);
      setContexts([]);
      setChatId(undefined);
      setOption(undefined);
    },
  };
}
