import React, { useState } from 'react';
import { ModalComponent, ModalComponentProps } from '@bika/ui/modal';
import { ArtifactContainer, ArtifactContainerProps } from './artifact-container';

export interface ArtifactContainerWithModalProps<T extends object | string> extends ArtifactContainerProps<T> {
  /**
   * Optional callback when modal opens/closes
   */
  onModalStateChange?: (isOpen: boolean) => void;

  /**
   * Props to pass to the ModalComponent when modal is opened
   */
  modalProps?: Partial<Pick<ModalComponentProps, 'title' | 'width' | 'height' | 'closable' | 'className' | 'sx'>>;
}

/**
 * A wrapper component that adds modal expansion functionality to ArtifactContainer.
 * Renders ArtifactContainer normally, but opens it in a modal when expanded.
 */
export const ArtifactContainerWithModal: React.FC<
  Omit<ArtifactContainerWithModalProps<object | string>, 'onExpand' | 'isModal'>
> = ({ onModalStateChange, modalProps, ...artifactContainerProps }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Override expandable to control modal opening
  const enhancedProps = {
    ...artifactContainerProps,
    expandable: true,
    // Pass a custom toolbar button that triggers modal
    toolbarButton: artifactContainerProps.toolbarButton,
  };

  return (
    <>
      {/* Inline view */}
      <ArtifactContainer
        {...enhancedProps}
        isModal={isModalOpen}
        onExpand={(expaend) => {
          setIsModalOpen(expaend);
        }}
      />

      {/* Modal view */}
      {isModalOpen && (
        <ModalComponent
          title={modalProps?.title || null}
          onClose={() => {
            setIsModalOpen(false);
            onModalStateChange?.(false);
          }}
          closable={modalProps?.closable ?? false}
          width={modalProps?.width ?? '90vw'}
          height={modalProps?.height ?? '90vh'}
          className={modalProps?.className}
          sx={{
            maxWidth: 'none',
            maxHeight: 'none',
            ...modalProps?.sx,
          }}
        >
          <ArtifactContainer
            {...artifactContainerProps}
            expandable={false}
            isModal={isModalOpen}
            onExpand={(expaend) => {
              setIsModalOpen(expaend);
            }}
          />
        </ModalComponent>
      )}
    </>
  );
};
