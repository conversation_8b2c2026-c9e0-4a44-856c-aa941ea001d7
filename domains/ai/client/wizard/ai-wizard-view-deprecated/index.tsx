import assert from 'assert';
import React from 'react';
import { useApiCaller } from '@bika/api-caller/context';
import { AIWizardVOProvider } from '@bika/types/ai/context';
import { AIIntentUIResolveDTO } from '@bika/types/ai/dto';
import { ISpaceContext } from '@bika/types/space/context';
import { useGlobalContext, useGlobalState } from '@bika/types/website/context';
import { Skeleton } from '@bika/ui/skeleton';
import { AIWizardRenderer } from './renderer';
import { AIChatViewProps } from '../../chat/ai-chat-view';

/**
 * 4种模式：
 * - 不带Modal的View，放在Global Context (正常使用方式)
 * - 带Modal的，用于Onboarding，放在Space Modals (新手引导)
 * - 新手引导的模式，UI-GUIDE，放在SpaceModals (新手引导)
 * - NPC侧边聊天模式 （比如某些地方，直接在右上角或者左下角悬空一个聊天框?暂时没用上）
 *
 * @param props
 * @returns
 */
export function AIWizardViewDepreciated(props: AIChatViewProps) {
  const [globalSpaceContext] = useGlobalState<ISpaceContext>('SPACE');
  const globalCtx = useGlobalContext();
  const { trpc } = useApiCaller();

  const closeIt = () => {
    globalCtx.showUIModal(null);
    if (props.onClose) {
      props.onClose();
    }
  };

  // 走网络抓取
  const initDialog = async () => {
    const data = !props.inputState.chatId
      ? await trpc.ai.newWizard.query({
          spaceId: globalSpaceContext?.data.id,
          intent: props.initAIIntent || {
            type: 'SEARCH', // default ai intent
          },
        })
      : await trpc.ai.fetchWizard.query({ wizardId: props.inputState.chatId! });
    // setWizard(data);
    props.inputState.setChatId(data.id);
    return data;
  };

  // UI操作是假打字机
  const sendUI = async (wizardId: string, uiResolve: AIIntentUIResolveDTO) => {
    const refreshDialog = await trpc.ai.resolveWizard.mutate({
      wizardId,
      resolve: {
        type: 'UI',
        uiResolve,
      },
    });
    assert(refreshDialog);
    const lastAIMessage = refreshDialog.messages[refreshDialog.messages.length - 1];

    const prevIntent = props.initAIIntent;
    const nextIntent = refreshDialog.intent;
    // console.log({ prevIntent, nextIntent });
    if (prevIntent && nextIntent.type !== prevIntent?.type) {
      // 如果意图变了，重新初始化对话
      globalCtx.showUIModal({ name: 'AI_WIZARD', initIntent: nextIntent });
      // aiContext.startAIWizard(nextIntent);
    } else if (
      refreshDialog.resolutionStatus === 'SUCCESS' &&
      lastAIMessage.ui &&
      lastAIMessage.ui.type === 'CONFIRM' &&
      lastAIMessage.ui.default &&
      props.onClose
    ) {
      props.onClose();
    }
    return refreshDialog;
  };

  // 发出聊天信息，真服务器流打字机
  const sendMessage = async (wizardId: string, humanSay: string) => {
    const resolveWizard = await trpc.ai.resolveWizard.mutate({
      wizardId,
      resolve: {
        type: 'MESSAGE',
        message: humanSay,
      },
    });
    // setAiMessage(iStr(refreshDialog.lastAiMessage!.text));
    assert(resolveWizard);

    if (resolveWizard.resolutionStatus === 'NOT_STARTED') {
      // 如果去到一个NOT_STARTED的AI，主动搭讪信息
      // sendMessage('好的呀');
    }
    return resolveWizard;
  };

  return (
    <AIWizardVOProvider
      skeleton={<Skeleton pos="AI_WIZARD" />}
      initDialog={initDialog}
      value={{
        initAIIntent: props.initAIIntent,
        initWizardId: props.inputState.chatId,
        // initUserMessage: props.initUserMessage,
      }}
      onSendUI={sendUI}
      onSendMessage={sendMessage}
      onSuccess={closeIt}
      defaultAutoPlay={true}
    >
      <AIWizardRenderer withModal={props.withModal} closeIt={closeIt} />
    </AIWizardVOProvider>
  );
}
