import React from 'react';
import { AI<PERSON>hatView, type AIChatViewProps } from '../chat/ai-chat-view';
import { AIWelcome, type AIWelcomeProps } from '../chat/ai-welcome';
import type { IAIChatInputState } from '../chat/hooks/use-ai-chat-cache';
import { useGlobalChatState } from '../chat/hooks/use-global-chat-state';

type Props = Pick<
  AIChatViewProps,
  // | 'selector'
  'inputState' | 'config' | 'forceLocale' | 'initAIIntent' | 'displayMode'
  // | 'context' | 'allowContextMenu'
> &
  Pick<
    AIWelcomeProps,
    | 'title'
    | 'description'
    | 'initPrompts'
    // | 'options'
    | 'customBottom'
    | 'avatars'
  > & {
    // redirect?: () => void;
    fullscreen?: boolean;
    skillsetIcons?: React.ReactNode;
  };

export interface IAIChatWithWelcomeHandle {
  stage: 'welcome' | 'chat';
  setStage: (stage: 'welcome' | 'chat') => void;
}
/**
 * AI Wizard Launchpad
 * Just a textbox, before start a wizard
 *
 * @returns
 */
function InternalAIWizardWithWelcome(props: Props, ref: React.Ref<IAIChatWithWelcomeHandle>) {
  // const { redirect } = props;
  const [stage, setStage] = React.useState<'welcome' | 'chat'>('welcome');
  // const [input, setInput] = React.useState<string>('');
  // const [inputState, setInputState] = React.useState<IAIChatInputState>({
  //   input: '',
  //   setInput: (value) => setInput(value),
  // });
  // 避免开发环境 useEffect 执行两次的问题
  const hasInit = React.useRef(false);

  const globalChatState = useGlobalChatState();

  React.useImperativeHandle(ref, () => ({
    stage,
    setStage,
  }));

  React.useEffect(() => {
    if (!hasInit.current) {
      if (globalChatState.autoChatQueue.length > 0) {
        // 是否队列里有自动消息，有的话马上激活 chat 搞一条
        // 取第一个自动消息，设置成 initStartMessage
        const firstChatInQueue = globalChatState.removeFromAutoChatQueue(0);
        props.inputState.setInput(firstChatInQueue.userPrompt);
        // setInput(firstChatInQueue.userPrompt);
        setStage('chat');
      } else if (props.inputState.chatId) {
        // 有强制初始化的 chat？ 马上开始
        setStage('chat');
      } else {
        setStage('welcome');
      }
      hasInit.current = true;
    }
  }, []);

  return (
    <>
      {stage === 'welcome' && (
        <AIWelcome
          inputState={props.inputState}
          config={props.config}
          initPrompts={props.initPrompts}
          // allowContextMenu={props.allowContextMenu}
          title={props.title} // t.ai_consultant.title}
          description={props.description} // t.ai_consultant.description}
          onSubmit={(_newInputState: IAIChatInputState) => {
            // setInputState(newInputState);
            //  正式开始，跳到 ai wizard
            setStage('chat');
          }}
          avatars={props.avatars}
          skillsetIcons={props.skillsetIcons}
          customBottom={props.customBottom}
          fullscreen={props.fullscreen}
        />
      )}
      {stage === 'chat' && (
        // 这个组件会发起 trpc 请求，创建 ai wizard
        <AIChatView
          inputState={props.inputState}
          config={props.config}
          displayMode={props.displayMode}
          forceLocale={props.forceLocale}
          // initStartMessage={props.inputState.input}
          initAIIntent={props.initAIIntent}
          // context={props.context}
          // allowContextMenu={props.allowContextMenu}
          setStage={setStage}
          skillsetIcons={props.skillsetIcons}
        />
      )}
    </>
  );
}

export const AIWizardWithWelcome = React.memo(React.forwardRef(InternalAIWizardWithWelcome));
