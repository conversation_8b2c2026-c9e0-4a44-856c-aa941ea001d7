import { UserSO } from '@bika/domains/user/server';
import { AIMessageBO } from '@bika/types/ai/bo';
import { AIMessageVO } from '@bika/types/ai/vo';

/**
 * AIMessageSO
 *
 * 用于渲染出MessageVO，返回客户端
 */
export class AIMessageSO {
  static async convertBOToVO(messages: AIMessageBO[]): Promise<AIMessageVO[]> {
    if (!messages?.length) {
      return [];
    }

    // 只处理 HUMAN 类型的消息，并收集有效的用户ID
    const humanMessages = messages.filter(
      (m): m is AIMessageBO & { createdBy: string } => m.role === 'user' && !!m.createdBy,
    );

    if (!humanMessages.length) {
      return messages;
    }

    // 获取所有用户信息
    const userIds = [...new Set(humanMessages.map((m) => m.createdBy))];
    const userMap = await UserSO.buildMapByIds(userIds);

    // 转换消息
    return messages.map((message: AIMessageBO) => {
      if (message.role === 'user' && message.createdBy) {
        const user = userMap[message.createdBy];
        if (user && message.annotations && !message.annotations.find((a) => a.type === 'creator')) {
          message.annotations.push({
            type: 'creator',
            creator: user.toVO(),
          });
        }
        return message;
      }
      return message;
    });
  }
}
