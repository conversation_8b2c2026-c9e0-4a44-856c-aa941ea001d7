import { test, expect } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { LauncherSO } from '@bika/domains/ai/server/launcher-so';
import { wait } from '@bika/domains/shared/server';
import { db } from '@bika/server-orm';

/**
 * AI 生成CustomTemplate！
 */
test('Launcher Search', async () => {
  const { user, space, rootFolder } = await MockContext.initUserContext();
  await space!.installTemplateById(user, 'diary');

  await wait(2000);

  // 检查索引
  const { rows: spaceNodes } = await db.search.advancedSearch('NODE_RESOURCE', {
    query: {
      bool: {
        should: [],
        must: [
          {
            term: {
              'spaceId.keyword': space.id,
            },
          },
        ],
      },
    },
  });
  console.log(spaceNodes);
  expect(spaceNodes.length).toBeGreaterThan(0);

  const commands1 = await LauncherSO.search(user.id, space.id, 'SMART');

  console.log(commands1);

  const commands2 = await LauncherSO.search(user.id, space.id, 'SMART', 'diary');
  console.log(JSON.stringify(commands2));
});
