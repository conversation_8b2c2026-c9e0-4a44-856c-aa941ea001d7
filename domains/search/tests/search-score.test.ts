import { expect, test } from 'vitest';
import { db } from '@bika/server-orm';

test('search with scores', async () => {
  await db.search.write({
    id: 'test-template',
    indexData: {
      type: 'TEMPLATE_REPO',
      templateId: 'test-template',
      content: 'Test Rempl Repo',
      visibility: 'PUBLIC',
    },
  });

  const { rows: result } = await db.search.advancedSearch('TEMPLATE_REPO', {
    q: 'test',
  });
  expect(result.length).toBeGreaterThanOrEqual(1);
  console.log('搜索test模板相似度得分', result[0].score);
  expect(result[0].score).toBeGreaterThan(0.001); // 相似度>2分
});
