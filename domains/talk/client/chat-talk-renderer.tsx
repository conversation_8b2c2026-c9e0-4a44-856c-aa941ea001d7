import dayjs from 'dayjs';
import React from 'react';
import { useLocale } from '@bika/contents/i18n';
import { useSpaceContextForce } from '@bika/types/space/context';
import type { TalkDetailVO } from '@bika/types/space/vo';
import { NodeIcon } from '@bika/ui/node/icon';
import { convertTalkTime } from './talk-time';
import { useTalkDisplayInfo } from './use-talk-display-info';
import { UnitItemRenderer } from '../../space/client/sidebar-tree/unit-tree-view/unit-item-renderer';

interface Props {
  value: TalkDetailVO;
  handleRemove: (id: string) => void;
  handlePin: (id: string, isPinned: boolean) => void;
  isLast?: boolean;
}

export function ChatTalkRenderer(props: Props) {
  const spaceContext = useSpaceContextForce();
  const locale = useLocale();
  const spaceId = spaceContext.data?.id;
  const timeStr = React.useMemo(() => convertTalkTime(dayjs(props.value.updatedAt)), [props.value.updatedAt]);

  const displayInfo = useTalkDisplayInfo(props.value);
  if (!displayInfo) {
    return null; // or handle the error as needed
  }

  const { url, name, description, nodeIconValue } = displayInfo;

  const isActive =
    url === `/space/${spaceId}` ? window.location.pathname === url : window.location.pathname.includes(url);

  return (
    <UnitItemRenderer
      kind={'talk'}
      id={props.value.id}
      time={locale.i(timeStr)}
      url={url}
      name={name}
      description={description}
      icon={<NodeIcon value={nodeIconValue} color="var(--static)" size={40} title={name} />}
      badge={props.value.type === 'node' ? undefined : Number(props.value.badge)}
      badgePosition={'bottom'}
      // contextMenu={[]}
      isLast={props.isLast}
      isActive={isActive}
      handlePin={props.handlePin}
      handleRemove={props.handleRemove}
      isPinned={props.value.isPinned}
    />
  );
}
