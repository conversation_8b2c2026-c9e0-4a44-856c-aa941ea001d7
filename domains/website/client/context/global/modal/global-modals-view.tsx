import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dynamic from 'next/dynamic';
import React from 'react';
import { useLocale } from '@bika/contents/i18n/context';
// import { AIWizardView } from '@bika/domains/ai/client/wizard/ai-wizard-view';
import { useAIChatInputState } from '@bika/domains/ai/client/chat/hooks/use-ai-chat-cache';
import { AuthWithStandaloneModal } from '@bika/domains/auth/client';
import { TemplateOperate } from '@bika/domains/template/client/template-operate';
import { HelpVideoTabsView } from '@bika/domains/website/client/help-video/help-video-tabs-view';
import { AIArtifactModal } from '@bika/domains/website-global-modals/ai-artifact-modal';
import type { GlobalModalConfig } from '@bika/types/website/bo';
import { Stack, Box } from '@bika/ui/layouts';
import { ModalComponent } from '@bika/ui/modal';
import { Skeleton } from '@bika/ui/skeleton';
import { getModalConfig } from './global-modals-config';
import { useModal } from '../hooks/useModal';

interface Props {
  config: GlobalModalConfig | null;
  onClose: () => void;
}

export function GlobalModalsView(props: Props) {
  const locale = useLocale();
  const { config: modalConfig, onClose } = props;

  const inputState = useAIChatInputState();

  React.useEffect(() => {
    if (modalConfig && 'initUserMessage' in modalConfig && modalConfig.initUserMessage) {
      inputState.setInput(modalConfig.initUserMessage);
    }
  }, [modalConfig, inputState]);

  const modalControl = useModal();

  const handleModalClose = () => {
    // 把当前地址栏的?contact=1去掉
    // if (name === 'CONTACT_SERVICE') {
    //   const url = new URL(window.location.href);
    //   url.searchParams.delete('contact');
    //   window.history.replaceState({}, '', url.toString());
    // }
    modalControl.setShowUIModal(null);
    onClose();
  };

  const renderModal = () => {
    if (!modalConfig) {
      return null;
    }
    const modalName = modalConfig.name;
    switch (modalName) {
      case 'AUTH': {
        return (
          <AuthWithStandaloneModal
            redirect={modalConfig.redirect}
            onLogin={async () => {
              modalControl.setShowUIModal(null);
            }}
          />
        );
      }
      case 'USER_PROFILE': {
        const UserProfileView = dynamic(() =>
          import('@bika/domains/user/client/user-settings-modal').then((res) => res.default),
        );
        return <UserProfileView onClose={handleModalClose} />;
      }
      case 'CONTACT_SERVICE':
      case 'COMING_SOON': {
        const ContactServiceView = dynamic(
          () => import('@bika/domains/website/client/contact-service/index').then((res) => res.ContactServiceTabsView),
          {
            loading: () => (
              <Box minWidth={346}>
                <Skeleton pos={'CONTACT_SERVICE'} />
              </Box>
            ),
          },
        );
        return <ContactServiceView onClose={handleModalClose} defaultTab={modalConfig.defaultTab} />;
      }
      case 'USER_EMAIL_SETTING': {
        const UserEmailSettingView = dynamic(() =>
          import('@bika/domains/user/client/user-email-setting-modal').then((res) => res.default),
        );
        return <UserEmailSettingView onClose={handleModalClose} />;
      }
      case 'INSTALL_TEMPLATE': {
        return <TemplateOperate modalConfig={modalConfig} onClose={handleModalClose} />;
      }
      case 'AI_WIZARD': {
        // assert(modalConfig)
        const AIWizardView = dynamic(
          () =>
            import('../../../../../ai/client/wizard/ai-wizard-view-deprecated').then(
              (res) => res.AIWizardViewDepreciated,
            ),
          {
            ssr: false,
          },
        );

        return (
          <AIWizardView
            inputState={inputState}
            // initUserMessage={modalConfig.initUserMessage}
            initAIIntent={modalConfig.initIntent}
            // initChatId={modalConfig.initWizardId}
            key={modalConfig.initIntent?.type}
            withModal={false}
          />
        );
      }

      case 'HELP_VIDEO': {
        return <HelpVideoTabsView />;
      }
      case 'AI_ARTIFACT': {
        return <AIArtifactModal onClickClose={handleModalClose} />;
        // return <AIChatArtifact message={undefined} tool={undefined} onClickClose={function (): void {
        //   throw new Error('Function not implemented.');
        // } } />;
      }
      default:
        return null;
    }
  };

  if (!modalConfig) {
    return null;
  }
  const config = getModalConfig(modalConfig.name, locale);

  if (config) {
    if (config.modal === false) {
      return renderModal();
    }

    const renderTitle = () => {
      if (typeof config.title === 'string') {
        return (
          <Stack pb={1} alignItems={config.center ? 'center' : 'flex-start'}>
            {config.title}
          </Stack>
        );
      }
      return config.title;
    };

    return (
      <ModalComponent
        closable={config.closable || false}
        className={config.className}
        width={config.width}
        height={config.height}
        // eslint-disable-next-line no-void
        onClose={handleModalClose}
        title={renderTitle()}
      >
        {/* <LocaleProvider {...localeProps}> */}
        <LocalizationProvider dateAdapter={AdapterDayjs}>{renderModal()}</LocalizationProvider>
        {/* </LocaleProvider> */}
      </ModalComponent>
    );
  }

  return null;
}
