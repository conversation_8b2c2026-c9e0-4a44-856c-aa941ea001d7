import assert from 'assert';
import { z } from 'zod';
import { AISO } from '@bika/domains/ai/server/ai-so';
import { ArtifactVO, SlidesArtifactSchema, SlidesArtifactVO } from '@bika/types/ai/vo';
import { IStreamResult } from '../../ai/server/types';
import type { UserSO } from '../../user/server/user-so';
import { type AIArtifactSO } from '../ai-artifact-so';
import { IArtifactHandler } from '../interface';

export class SlidesArtifactHandler implements IArtifactHandler {
  private _artifactSO: AIArtifactSO;

  public constructor(artifactSO: AIArtifactSO) {
    this._artifactSO = artifactSO;
  }

  async streamResult<T = z.infer<typeof SlidesArtifactSchema>>(user: UserSO): Promise<IStreamResult<T> | undefined> {
    assert(this._artifactSO.type === 'slides', 'SlidesArtifactHandler only support slides type');
    // const model = AISO.getSystemAIModel();
    // const model = 'claude-sonnet-4';
    const model = 'qwen/qwen-turbo';
    const streamResult = await AISO.streamObject(
      {
        ...this._artifactSO.prompt,
        schema: SlidesArtifactSchema,
        user,
      },
      {
        model,
      },
    );
    return {
      type: 'object',
      objectResult: streamResult,
    } as IStreamResult<T>;
  }

  /**
   * Get Artifact VO
   *
   * @param user
   * @param streamResult
   * @returns
   */
  async getValue(_user: UserSO, streamResult: IStreamResult | undefined): Promise<ArtifactVO> {
    assert(streamResult, 'streamResult should not be undefined');
    assert(streamResult.type === 'object', 'SlidesArtifactHandler only support object type');
    // const model = AISO.getSystemAIModel();
    // const model = 'claude-sonnet-4';
    const model = 'qwen/qwen-turbo';

    const vo: ArtifactVO = {
      type: 'slides',
      id: this._artifactSO.id,
      data: {
        slides: [],
      },
    };

    vo.data = (await streamResult.objectResult.object) as SlidesArtifactVO;
    vo.usage = await AISO.parseAICreditCost(model, await streamResult.objectResult.usage);
    return vo;
  }
}
