import type { ToolInvocation } from '@ai-sdk/ui-utils';
import React from 'react';
import { useLocale } from '@bika/contents/i18n';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { IconButton } from '@bika/ui/button-component';
import DownloadOutlined from '@bika/ui/icons/components/download_outlined';
import { directDownload } from '@bika/ui/preview-attachment/attachment-renderer';
import { FilePreview } from '@bika/ui/preview-attachment/file-preview';
import { ArtifactContainerWithModal } from '../../ai/client/chat/artifacts/components/artifact-container-with-modal';

interface FileArtifactProps {
  filePath?: string;
  content?: string;
  skillsets?: SkillsetSelectDTO[];
  tool?: ToolInvocation;
  expandable?: boolean;
}

export const FileArtifact = (props: FileArtifactProps) => {
  const { filePath, content, skillsets = [], tool, expandable = true } = props;
  const { t } = useLocale();

  const data = { filePath, content };

  const downloadButton = (
    <>
      <IconButton
        variant="plain"
        size="sm"
        color="neutral"
        onClick={async () => {
          if (filePath) {
            // Extract filename from filePath or use a default name
            const filename = filePath.split('/').pop() || 'download';
            await directDownload(filePath, filename);
          }
        }}
        sx={{
          '&:hover': {
            backgroundColor: 'var(--hover)',
          },
        }}
      >
        <DownloadOutlined color="var(--text-primary)" />
      </IconButton>
    </>
  );

  return (
    <ArtifactContainerWithModal
      data={data}
      skillsets={skillsets}
      tool={tool}
      modalProps={{
        width: '100vw',
        height: '100vh',
      }}
      expandable={expandable}
      rowDataType="json"
      toolbarButton={downloadButton}
      switchProps={{
        previewLabel: t.ai.artifact_preview,
      }}
    >
      {filePath && <FilePreview url={filePath} />}
    </ArtifactContainerWithModal>
  );
};
