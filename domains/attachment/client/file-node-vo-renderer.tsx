import Image from 'next/image';
import { useLocale } from '@bika/contents/i18n/context';
import { OnlineUsersView } from '@bika/domains/auth/client/online-users-view';
import { NodeVOAICopilotMenu } from '@bika/domains/editor/client/node-vo-menu/node-vo-ai-copilot-menu';
import { NodeVOMenu } from '@bika/domains/editor/client/node-vo-menu/node-vo-menu';
import { NodeHeaderTitle } from '@bika/domains/node/client/header/node-header-title-component';
import type { FileNodeVO } from '@bika/types/document/vo';
import type { NodeDetailVO } from '@bika/types/node/vo';
import { Box, Stack } from '@bika/ui/layouts';
import { AttachmentRenderer } from '@bika/ui/preview-attachment/attachment-renderer';
import { PreviewImageProvider } from '@bika/ui/preview-attachment/context/preview-image-context';
// import { useFrameSetState } from '@bika/ui/preview-attachment/hooks/use-frame-state';
import { useTransform } from '@bika/ui/preview-attachment/preivew-attachment';
import { ToolBar } from '@bika/ui/preview-attachment/tool_bar';
import { HeaderPageComponent } from '@bika/ui/web-layout';

interface Props {
  value: NodeDetailVO;
}

export function FileNodeVORenderer(props: Props) {
  const fileVO = props.value.resource as FileNodeVO;
  const { name, permission, description, id } = props.value;
  const { t } = useLocale();

  const { transformInfo, setTransformInfo, handleZoom, handleRotate } = useTransform();
  // const [transformInfo, setTransformInfo] = useFrameSetState<ITransFormInfo>(initTransformInfo);

  const getAttachment = () => ({
    ...fileVO.attachment,
    links: {
      previewUrl: fileVO.url,
      thumbnailUrl: fileVO.url,
      downloadUrl: fileVO.url,
    },
  });

  const attachment = getAttachment();
  const isImage = (attachment.mimeType || (attachment as any).type).startsWith('image/');

  return (
    <div className="w-full h-full">
      <HeaderPageComponent
        header={
          <NodeHeaderTitle
            nodeType="FILE"
            nodeId={id}
            icon={{ kind: 'node-resource', customIcon: props.value.icon ?? undefined, nodeType: 'FILE' }}
            // TODO i18n
            name={name || 'no title'}
            description={description}
            permission={permission?.privilege}
            button={
              <Stack direction="row" alignItems="center" spacing={2}>
                <OnlineUsersView />
                <NodeVOAICopilotMenu value={props.value} />
                <NodeVOMenu value={props.value} detail={props.value} />
              </Stack>
            }
          />
        }
      >
        <Box padding={'8px 32px'} bgcolor="var(--bg-page)" height="100%" position="relative">
          {isImage && (
            <div className="absolute left-0 top-0 right-0 w-full bg-black/50 backdrop-blur-sm shadow-lg z-10 px-10 py-2">
              <ToolBar
                transformInfo={transformInfo}
                setTransformInfo={setTransformInfo}
                fileInfo={{
                  name: fileVO.attachment.name || 'attachment',
                  contentType: fileVO.attachment.mimeType || 'application/octet-stream',
                  url: fileVO.url,
                  variant: 'kkfile',
                }}
                onZoom={handleZoom}
                onRotate={handleRotate}
                t={t}
              />
            </div>
          )}
          {fileVO.attachment ? (
            <PreviewImageProvider transformInfo={transformInfo} setTransformInfo={setTransformInfo}>
              <AttachmentRenderer attachment={getAttachment()} t={t} />
            </PreviewImageProvider>
          ) : (
            <div className="flex items-center justify-center mt-[32px] mb-[24px] w-full">
              <Image
                src={'/assets/attachment/data-manager-download.png'}
                width={160}
                height={160}
                alt="data-manager-download"
              />
            </div>
          )}
        </Box>
      </HeaderPageComponent>
    </div>
  );
}
