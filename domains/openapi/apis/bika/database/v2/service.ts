import { HTTPException } from 'hono/http-exception';
import { AttachmentSO } from '@bika/domains/attachment/server';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { FieldSO } from '@bika/domains/database/server/fields/field-so';
import { AstFilter } from '@bika/domains/database/server/views/filter/ast-filter';
import { FilterSO } from '@bika/domains/database/server/views/filter-so';
import { SortSO } from '@bika/domains/database/server/views/sort-so';
import { LockUtil } from '@bika/domains/shared/server';
import { UserSO } from '@bika/domains/user/server';
import { mongoose } from '@bika/server-orm';
import { CellValue, RecordData } from '@bika/types/database/bo';
import { RecordBulkUpdates } from '@bika/types/database/dto';
import {
  ApiCreateRecordsReqV2,
  ApiGetRecordReqV2,
  ApiGetRecordsReqV2,
  ApiUpdateRecordReqV2,
  ApiUpdateRecordsReqV2,
  FieldCellValueInput,
} from '@bika/types/openapi/dto';
import {
  ApiCreateRecordsRes,
  ApiDeleteRecordRes,
  ApiGetRecordsRes,
  ApiRecordVO,
  ApiUpdateRecordsRes,
} from '@bika/types/openapi/vo';

export class Service {
  private database: DatabaseSO;

  constructor(database: DatabaseSO) {
    this.database = database;
  }

  static async init(databaseId: string) {
    const database = await this.getDatabase(databaseId);
    return new Service(database);
  }

  async listRecords(user: UserSO, req: ApiGetRecordsReqV2): Promise<ApiGetRecordsRes> {
    const {
      offset,
      pageSize = 100,
      maxRecords,
      filter: filterAsRSql,
      sort,
      fields,
      cellFormat = 'json',
      fieldKey = 'name',
    } = req;

    if (cellFormat === 'string' && !req.timeZone?.length) {
      // cellFormat 为 string 时，必须指定时区
      throw new HTTPException(400, { message: 'Time zone is required when cellFormat is "string"' });
    }
    const locale = req.userLocale ? req.userLocale : user.locale;
    const timeZone = req.timeZone || user.timeZone;

    // 过滤展示
    const returnFieldIds = fields?.map((fieldId) => this.database.getFieldIdByFieldKey(fieldId));

    // 是否使用视图
    // const view = viewId ? await this.database.getView(viewId) : null;

    // 是否可溢出最大页的记录数
    let sizePerPage = pageSize;
    if (maxRecords !== undefined) {
      // 如果设置了 maxRecords，则取最小的值
      sizePerPage = Math.min(100, maxRecords);
    }

    // 过滤条件
    // console.log(`user timezone: ${user.timeZone}, timeZone: ${timeZone}`);
    const filter = filterAsRSql ? new AstFilter(user, this.database).toViewFilter(filterAsRSql, { timeZone }) : null;
    const filterQuery = await new FilterSO(this.database, filter).buildQuery();

    // 排序
    const sorts: SortSO[] = [];
    for (const { field, order } of sort ?? []) {
      const fieldId = this.database.getFieldIdByFieldKey(field);
      const sortSO = this.database.buildSort(fieldId, order === 'asc');
      if (sortSO) {
        sorts.push(sortSO);
      }
    }

    const lastId = offset ? this.validateOffset(offset) : undefined;
    const { hasMore, nextId, records } = await this.database.getRecordsAsCursor({
      lastId,
      pageSize: sizePerPage,
      filterQuery,
      sorts,
    });
    const recordVOs = await Promise.all(
      records.map((record) =>
        record.toApiRecordVO({
          locale,
          timeZone,
          returnFieldName: fieldKey === 'name',
          fieldIds: returnFieldIds,
          cellFormat,
        }),
      ),
    );
    return {
      hasMore,
      offset: nextId?.toString(),
      records: recordVOs,
    };
  }

  /**
   * 获取单个记录
   */
  async getRecord(user: UserSO, recordId: string, req: ApiGetRecordReqV2): Promise<ApiRecordVO> {
    const { fieldKey = 'name', cellFormat = 'json' } = req;
    if (cellFormat === 'string' && !req.timeZone?.length) {
      // cellFormat 为 string 时，必须指定timeZone和locale
      throw new HTTPException(400, { message: 'Time zone and user locale are required when cellFormat is "string"' });
    }
    const locale = req.userLocale ? req.userLocale : user.locale;
    const timeZone = req.timeZone || user.timeZone;
    // 获取单个记录的逻辑
    const record = await this.database.getRecord(recordId);
    return record.toApiRecordVO({
      locale,
      timeZone,
      returnFieldName: fieldKey === 'name',
      cellFormat,
    });
  }

  /**
   * 创建多个记录
   */
  async createRecords(user: UserSO, req: ApiCreateRecordsReqV2): Promise<ApiCreateRecordsRes> {
    const { fieldKey = 'name', records = [] } = req;
    // 转换格式
    const cells: RecordData[] = await Promise.all(
      records.map(async (record) => {
        const cellEntries: [string, CellValue | undefined][] = await Promise.all(
          Object.entries(record.fields).map(async ([fk, value]) => {
            const field = this.database.getFieldByFieldKey(fk);
            const cellValue = await this.transformToCellValue(field, value);
            return [field.id, cellValue];
          }),
        );
        return Object.fromEntries(cellEntries);
      }),
    );
    const member = await user.getMember(this.database.spaceId);

    // 用量检查
    await this.checkEntitlement(cells.length);

    // 执行创建多行记录
    const createdRecords = await LockUtil.databaseGlobalLock(this.database.id, async () =>
      this.database.createRecords(user, member, cells),
    );

    const recordVOs = await Promise.all(
      createdRecords.map((record) =>
        record.toApiRecordVO({
          locale: user.locale,
          timeZone: user.timeZone ?? undefined,
          returnFieldName: fieldKey === 'name',
          cellFormat: 'json', // 默认使用 JSON 格式
        }),
      ),
    );

    return { records: recordVOs };
  }

  /**
   * 更新单个记录
   */
  async updateRecord(user: UserSO, recordId: string, req: ApiUpdateRecordReqV2): Promise<ApiRecordVO> {
    const { fieldKey = 'name', fields } = req;
    // 更新单个记录的逻辑
    const cellEntries: [string, CellValue | undefined][] = await Promise.all(
      Object.entries(fields).map(async ([fk, value]) => {
        const field = this.database.getFieldByFieldKey(fk);
        const cellValue = await this.transformToCellValue(field, value);
        return [field.id, cellValue];
      }),
    );
    const cells: RecordData = Object.fromEntries(cellEntries);
    const updatedRecord = await LockUtil.databaseGlobalLock(this.database.id, async () => {
      const updatedRecords = await this.database.updateRecords(user, [{ recordId, cells }]);
      return updatedRecords[0];
    });
    return updatedRecord.toApiRecordVO({
      locale: user.locale,
      timeZone: user.timeZone,
      returnFieldName: fieldKey === 'name',
      cellFormat: 'json', // 默认使用 JSON 格式
    });
  }

  /**
   * 更新多个记录
   */
  async updateRecords(user: UserSO, req: ApiUpdateRecordsReqV2): Promise<ApiUpdateRecordsRes> {
    const { fieldKey = 'name', records = [] } = req;
    const updates: RecordBulkUpdates = await Promise.all(
      records.map(async (record) => {
        const cellEntries: [string, CellValue | undefined][] = await Promise.all(
          Object.entries(record.fields).map(async ([fk, value]) => {
            const field = this.database.getFieldByFieldKey(fk);
            const cellValue = await this.transformToCellValue(field, value);
            return [field.id, cellValue];
          }),
        );
        const cells = Object.fromEntries(cellEntries);
        return { recordId: record.id, cells };
      }),
    );

    const updatedRecords = await LockUtil.databaseGlobalLock(this.database.id, async () =>
      // 批量更新
      this.database.updateRecords(user, updates),
    );

    const recordVOs = await Promise.all(
      updatedRecords.map((record) =>
        record.toApiRecordVO({
          locale: user.locale,
          timeZone: user.timeZone ?? undefined,
          returnFieldName: fieldKey === 'name',
          cellFormat: 'json', // 默认使用 JSON 格式
        }),
      ),
    );
    return { records: recordVOs };
  }

  /**
   * 删除单个记录
   */
  async deleteRecord(user: UserSO, recordId: string): Promise<ApiDeleteRecordRes> {
    await LockUtil.databaseGlobalLock(this.database.id, async () => {
      await this.database.deleteRecords(user, [recordId]);
    });
    return { id: recordId, deleted: true };
  }

  /**
   * 删除多个记录
   */
  async deleteRecords(user: UserSO, recordIds: string[]): Promise<ApiDeleteRecordRes[]> {
    await LockUtil.databaseGlobalLock(this.database.id, async () => {
      await this.database.deleteRecords(user, recordIds);
    });
    return [...recordIds.map((id) => ({ id, deleted: true }))];
  }

  private static async getDatabase(databaseId: string): Promise<DatabaseSO> {
    const database = await DatabaseSO.getDatabaseById(databaseId);
    if (!database) {
      throw new HTTPException(404, { message: `Database with ID ${databaseId} not found` });
    }
    return database;
  }

  /**
   * 检查用户在数据库中创建新记录的权限
   */
  private async checkEntitlement(numOfNewRecords: number) {
    const space = await this.database.getSpace();
    const entitlement = await space.getEntitlement();
    await entitlement.checkRecordsPerDatabaseUsage(this.database.id, numOfNewRecords);
    await entitlement.checkUsageExceed({ feature: 'RECORDS_PER_SPACE', value: numOfNewRecords });
  }

  private validateOffset(offset: string) {
    try {
      return new mongoose.Types.ObjectId(offset);
    } catch (_) {
      // 非法offset值
      throw new HTTPException(400, { message: 'Invalid offset value' });
    }
  }

  /**
   *
   * @param input 输入值
   * @returns 转换后的 CellValue
   */
  private async transformToCellValue(field: FieldSO, input: FieldCellValueInput): Promise<CellValue> {
    if (Array.isArray(input)) {
      if (input.every((item) => typeof item === 'string')) {
        return input; // for multi select, member, link record id
      }
      const ids = input.map((item) => item.id);
      const attachments = await AttachmentSO.findMany(ids);
      return attachments.map((attachment) => attachment.toVO());
    }
    if (typeof input === 'string' && field.type === 'SINGLE_SELECT') {
      return [input]; // for single select
    }
    return input;
  }
}
