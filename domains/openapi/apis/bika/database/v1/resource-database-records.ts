import assert from 'assert';
import { z, createRoute, type OpenAPIHono } from '@hono/zod-openapi';
import { DatabaseController } from '@bika/domains/database/apis';
import { NodeController } from '@bika/domains/node/apis';
import { UserSO } from '@bika/domains/user/server/user-so';
import { RecordRenderVOSchema } from '@bika/types/database/vo';
import {
  ApiCreateRecordReqSchema,
  ApiCreateRecordsReqSchema,
  ApiUpdateRecordsReqSchema,
  ApiRecordUpdateReqSchema,
  ApiUpdateRecordReqSchema,
  ApiRecordsQueryDTOSchema,
  ApiGetRecordsReq,
  ApiGetRecordsReqSchema,
  RecordSortArraySchema,
} from '@bika/types/openapi/dto';
import { createResponseVOSchema, GetRecordsResVOSchema, ResponseVOBuilder } from '@bika/types/openapi/vo';
import { RecordController } from '../../../vika/controller/database';
import { type ContextVariable, SpaceParamSchema } from '../../types';

const recordPaginationOpenAPISchema = createResponseVOSchema(GetRecordsResVOSchema).openapi('RecordPaginationVO');

// 获取指定视图的记录
export const getDatabaseViewsRecordsResourceRoute = createRoute({
  tags: ['Databases'],
  description: `Get Database Views's Records`,
  deprecated: true,
  method: 'get',
  path: '/v1/spaces/:spaceId/resources/databases/:databaseId/views/:viewId/records',
  request: {
    query: ApiRecordsQueryDTOSchema,
    params: SpaceParamSchema.extend({
      databaseId: z.string().openapi({ param: { name: 'databaseId', in: 'path', required: true }, example: 'dat***' }),
      viewId: z.string().openapi({ param: { name: 'viewId', in: 'path', required: true }, example: 'vim***' }),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: recordPaginationOpenAPISchema,
        },
      },
      description: 'Database Records Pagination Response',
    },
  },
});

// 获取数据表的记录
export const getDatabaseRecordsResourceRoute = createRoute({
  tags: ['Databases'],
  description: 'Get Database Records',
  deprecated: true,
  method: 'get',
  path: '/v1/spaces/:spaceId/resources/databases/:databaseId/records',
  request: {
    query: ApiRecordsQueryDTOSchema,
    params: SpaceParamSchema.extend({
      databaseId: z.string().openapi({ param: { name: 'databaseId', in: 'path', required: true }, example: 'dat***' }),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: recordPaginationOpenAPISchema,
        },
      },
      description: 'Database Records Pagination Response',
    },
  },
});

// 创建单行数据表的记录
export const createDatabaseRecordResourceRoute = createRoute({
  tags: ['Databases'],
  description: 'Create Database Record',
  deprecated: true,
  method: 'post',
  path: '/v1/spaces/:spaceId/resources/databases/:databaseId/records',
  request: {
    body: {
      content: {
        'application/json': {
          schema: ApiCreateRecordReqSchema,
        },
      },
    },
    params: SpaceParamSchema.extend({
      databaseId: z.string().openapi({ param: { name: 'databaseId', in: 'path', required: true }, example: 'dat***' }),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(RecordRenderVOSchema).openapi({
            description: 'created records',
          }),
        },
      },
      description: 'Database Records Pagination Response',
    },
  },
});

// 批量创建数据表的记录
export const createDatabaseRecordsResourceRoute = createRoute({
  tags: ['Databases'],
  description: 'Create Database Multiple Record',
  deprecated: true,
  method: 'post',
  path: '/v1/spaces/:spaceId/resources/databases/:databaseId/records/batch',
  request: {
    body: {
      content: {
        'application/json': {
          schema: ApiCreateRecordsReqSchema,
        },
      },
    },
    params: SpaceParamSchema.extend({
      databaseId: z.string().openapi({ param: { name: 'databaseId', in: 'path', required: true }, example: 'dat***' }),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(RecordRenderVOSchema.array()).openapi({
            description: 'Array of created records',
          }),
        },
      },
      description: 'Database Records Response',
    },
  },
});

/**
 * 更新数据表的记录
 * @deprecated use `updateRecordResourceRoute` instead
 */
export const updateDatabaseRecordResourceRoute = createRoute({
  tags: ['Databases'],
  description: 'Update Database Single Record',
  method: 'patch',
  deprecated: true,
  path: '/v1/spaces/:spaceId/resources/databases/:databaseId/records',
  request: {
    body: {
      content: {
        'application/json': {
          schema: ApiRecordUpdateReqSchema,
        },
      },
    },
    params: SpaceParamSchema.extend({
      databaseId: z.string().openapi({ param: { name: 'databaseId', in: 'path', required: true }, example: 'dat***' }),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(RecordRenderVOSchema).openapi({
            description: 'Array of created records',
          }),
        },
      },
      description: 'Database Records Pagination Response',
    },
  },
});

/**
 * 更新单个数据表的记录
 */
export const updateRecordResourceRoute = createRoute({
  tags: ['Databases'],
  description: 'Update Database Record',
  deprecated: true,
  method: 'patch',
  path: '/v1/spaces/:spaceId/resources/databases/:databaseId/records/:recordId',
  request: {
    body: {
      content: {
        'application/json': {
          schema: ApiUpdateRecordReqSchema,
        },
      },
    },
    params: SpaceParamSchema.extend({
      databaseId: z.string().openapi({ param: { name: 'databaseId', in: 'path', required: true }, example: 'dat***' }),
      recordId: z.string().openapi({ param: { name: 'recordId', in: 'path', required: true }, example: 'rec***' }),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(RecordRenderVOSchema).openapi({
            description: 'updated record view',
          }),
        },
      },
      description: 'Database Records Response',
    },
  },
});

// 批量更新数据表的记录
export const updateRecordsResourceRoute = createRoute({
  tags: ['Databases'],
  description: 'Update Database Multiple Record',
  deprecated: true,
  method: 'put',
  path: '/v1/spaces/:spaceId/resources/databases/:databaseId/records/batch',
  request: {
    body: {
      content: {
        'application/json': {
          schema: ApiUpdateRecordsReqSchema,
        },
      },
    },
    params: SpaceParamSchema.extend({
      databaseId: z.string().openapi({ param: { name: 'databaseId', in: 'path', required: true }, example: 'dat***' }),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(RecordRenderVOSchema.array()).openapi({
            description: 'Array of updated records',
          }),
        },
      },
      description: 'Database Records Response',
    },
  },
});

// 删除数据表的记录
export const deleteDatabaseRecordResourceRoute = createRoute({
  tags: ['Databases'],
  description: 'Delete Database Record',
  deprecated: true,
  method: 'delete',
  path: '/v1/spaces/:spaceId/resources/databases/:databaseId/records/:recordId',
  request: {
    params: SpaceParamSchema.extend({
      databaseId: z.string().openapi({ param: { name: 'databaseId', in: 'path', required: true }, example: 'dat***' }),
      recordId: z.string().openapi({ param: { name: 'recordId', in: 'path', required: true }, example: 'rec***' }),
    }),
  },
  responses: {
    200: {
      description: 'Database Records Response',
    },
  },
});

// 批量删除数据表的记录
export const deleteDatabaseRecordsResourceRoute = createRoute({
  tags: ['Databases'],
  description: 'Delete Database Multiple Record',
  deprecated: true,
  method: 'delete',
  path: '/v1/spaces/:spaceId/resources/databases/:databaseId/records',
  request: {
    params: SpaceParamSchema.extend({
      databaseId: z.string().openapi({ param: { name: 'databaseId', in: 'path', required: true }, example: 'dat***' }),
    }),
    query: z.object({
      records: z
        .string()
        .array()
        .max(100)
        .openapi({
          param: { name: 'records', in: 'query', required: true },
          example: ['rec1***', 'rec2***'],
          description: 'Array of record IDs to delete, max 100 records',
        }),
    }),
  },
  responses: {
    200: {
      description: 'Database Records Response',
    },
  },
});

export function initResourceDatabaseRecordsRoutes(app: OpenAPIHono<{ Variables: ContextVariable }>) {
  app.openapi(getDatabaseViewsRecordsResourceRoute, async (c) => {
    const userId = c.get('token').userId;
    const ctx = c.get('context');
    const { spaceId, databaseId, viewId } = c.req.param();
    const user = await UserSO.init(userId);
    // check user in space
    await user.checkExistSpace(spaceId);
    const node = await NodeController.retrieveNodeTree(ctx, {
      id: databaseId,
    });
    assert(node.permission?.abilities.readNode === true, 'no permission to read node');
    const rawQuery = c.req.valid('query');
    const validatedQuery: Partial<ApiGetRecordsReq> = {};
    rawQuery.viewId = viewId;
    if (rawQuery.pageNum) {
      validatedQuery.pageNum = parseInt(rawQuery.pageNum, 10);
    }
    if (rawQuery.pageSize) {
      validatedQuery.pageSize = parseInt(rawQuery.pageSize, 10);
    }
    if (rawQuery.maxRecords) {
      //
      validatedQuery.maxRecords = parseInt(rawQuery.maxRecords, 10);
    }
    if (rawQuery.fields) {
      validatedQuery.fields = rawQuery.fields.split(',');
    }
    if (rawQuery.recordIds) {
      validatedQuery.recordIds = rawQuery.recordIds.split(',');
    }
    if (rawQuery.filter) {
      validatedQuery.filter = rawQuery.filter;
    }
    if (rawQuery.sort) {
      // @ts-expect-error
      const s = JSON.parse(decodeURIComponent(rawQuery.sort));
      validatedQuery.sort = RecordSortArraySchema.parse(s);
    }
    const query = ApiGetRecordsReqSchema.parse({
      ...rawQuery,
      ...validatedQuery,
    });
    const responseVO = await RecordController.getRecords(user, databaseId, query);
    return c.json(responseVO);
  });

  app.openapi(getDatabaseRecordsResourceRoute, async (c) => {
    const userId = c.get('token').userId;
    const ctx = c.get('context');
    const { spaceId, databaseId } = c.req.param();
    const user = await UserSO.init(userId);
    // check user in space
    await user.checkExistSpace(spaceId);
    const node = await NodeController.retrieveNodeTree(ctx, {
      id: databaseId,
    });
    assert(node.permission?.abilities.readNode === true, 'no permission to read node');
    // Parse query
    const rawQuery = c.req.valid('query');
    const validatedQuery: Partial<ApiGetRecordsReq> = {};
    if (rawQuery.pageNum) {
      validatedQuery.pageNum = parseInt(rawQuery.pageNum, 10);
    }
    if (rawQuery.pageSize) {
      validatedQuery.pageSize = parseInt(rawQuery.pageSize, 10);
    }
    if (rawQuery.maxRecords) {
      validatedQuery.maxRecords = parseInt(rawQuery.maxRecords, 10);
    }
    if (rawQuery.fields) {
      validatedQuery.fields = rawQuery.fields.split(',');
    }
    if (rawQuery.recordIds) {
      validatedQuery.recordIds = rawQuery.recordIds.split(',');
    }
    if (rawQuery.filter) {
      validatedQuery.filter = rawQuery.filter;
    }
    if (rawQuery.sort) {
      // @ts-expect-error
      const s = JSON.parse(decodeURIComponent(rawQuery.sort));
      validatedQuery.sort = RecordSortArraySchema.parse(s);
    }
    const query = ApiGetRecordsReqSchema.parse({
      ...rawQuery,
      ...validatedQuery,
    });
    const responseVO = await RecordController.getRecords(user, databaseId, query);
    return c.json(responseVO);
  });

  app.openapi(createDatabaseRecordResourceRoute, async (c) => {
    const userId = c.get('token').userId;
    const ctx = c.get('context');
    const { spaceId, databaseId } = c.req.param();
    const user = await UserSO.init(userId);
    // check user in space
    await user.checkExistSpace(spaceId);
    const body = c.req.valid('json');

    const node = await NodeController.retrieveNodeTree(ctx, {
      id: databaseId,
    });
    assert(node.permission?.abilities.readNode === true, 'no permission to read node');
    const ret = await DatabaseController.createRecordWithUser(user, user.settings.locale || 'en', {
      ...body,
      databaseId,
    });
    const response = ResponseVOBuilder.success(ret);
    return c.json(response);
  });

  app.openapi(createDatabaseRecordsResourceRoute, async (c) => {
    const ctx = c.get('context');
    const { spaceId, databaseId } = c.req.param();
    const body = c.req.valid('json');

    const userId = c.get('token').userId;
    const user = await UserSO.init(userId);
    // check user in space
    await user.checkExistSpace(spaceId);

    const node = await NodeController.retrieveNodeTree(ctx, {
      id: databaseId,
    });
    assert(node.permission?.abilities.readNode === true, 'no permission to read node');
    const ret = await DatabaseController.createRecordsWithUser(user, user.settings.locale || 'en', {
      ...body,
      databaseId,
    });
    const response = ResponseVOBuilder.success(ret);
    return c.json(response);
  });

  app.openapi(updateDatabaseRecordResourceRoute, async (c) => {
    const ctx = c.get('context');
    const { spaceId, databaseId } = c.req.param();
    const body = c.req.valid('json');

    const userId = c.get('token').userId;
    const user = await UserSO.init(userId);
    // check user in space
    await user.checkExistSpace(spaceId);

    const node = await NodeController.retrieveNodeTree(ctx, {
      id: databaseId,
    });
    assert(node.permission?.abilities.readNode === true, 'no permission to read node');
    const ret = await DatabaseController.updateRecord(user, {
      ...body,
      databaseId,
    });
    const response = ResponseVOBuilder.success(ret);
    return c.json(response);
  });

  app.openapi(updateRecordResourceRoute, async (c) => {
    const ctx = c.get('context');
    const { spaceId, databaseId, recordId } = c.req.param();
    const body = c.req.valid('json');

    const userId = c.get('token').userId;
    const user = await UserSO.init(userId);
    // check user in space
    await user.checkExistSpace(spaceId);

    const node = await NodeController.retrieveNodeTree(ctx, {
      id: databaseId,
    });
    assert(node.permission?.abilities.readNode === true, 'no permission to read node');
    const ret = await DatabaseController.updateRecord(user, {
      databaseId,
      id: recordId,
      cells: body.cells,
    });
    const response = ResponseVOBuilder.success(ret);
    return c.json(response);
  });

  app.openapi(updateRecordsResourceRoute, async (c) => {
    const ctx = c.get('context');
    const { spaceId, databaseId } = c.req.param();
    const body = c.req.valid('json');

    const userId = c.get('token').userId;
    const user = await UserSO.init(userId);
    // check user in space
    await user.checkExistSpace(spaceId);

    const node = await NodeController.retrieveNodeTree(ctx, {
      id: databaseId,
    });
    assert(node.permission?.abilities.readNode === true, 'no permission to read node');
    const ret = await DatabaseController.updateRecords(user, {
      updates: body.records,
      databaseId,
    });
    const response = ResponseVOBuilder.success(ret);
    return c.json(response);
  });

  app.openapi(deleteDatabaseRecordResourceRoute, async (c) => {
    const ctx = c.get('context');
    const { spaceId, databaseId, recordId } = c.req.param();
    const userId = c.get('token').userId;
    const user = await UserSO.init(userId);
    // check user in space
    await user.checkExistSpace(spaceId);
    const node = await NodeController.retrieveNodeTree(ctx, {
      id: databaseId,
    });
    assert(node.permission?.abilities.readNode === true, 'no permission to read node');
    const ret = await DatabaseController.deleteRecords(user, {
      databaseId,
      recordIds: [recordId],
    });
    const response = ResponseVOBuilder.success(ret);
    return c.json(response);
  });

  // 批量删除记录
  app.openapi(deleteDatabaseRecordsResourceRoute, async (c) => {
    const ctx = c.get('context');
    const { spaceId, databaseId } = c.req.param();
    const { records: recordIds } = c.req.valid('query');
    const userId = c.get('token').userId;
    const user = await UserSO.init(userId);
    // check user in space
    await user.checkExistSpace(spaceId);
    const node = await NodeController.retrieveNodeTree(ctx, {
      id: databaseId,
    });
    assert(node.permission?.abilities.readNode === true, 'no permission to read node');
    const ret = await DatabaseController.deleteRecords(user, {
      databaseId,
      recordIds,
    });
    const response = ResponseVOBuilder.success(ret);
    return c.json(response);
  });
}
