import { createRoute, type <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-openapi';
import { UserSO } from '@bika/domains/user/server/user-so';
import { UserVOSchema } from '@bika/types/user/vo';
import type { ContextVariable } from './types';

export const getUserProfile = createRoute({
  tags: ['User'],
  description: 'Get user profile',
  method: 'get',
  path: '/v1/user/profile',
  responses: {
    200: {
      content: {
        'application/json': {
          schema: UserVOSchema,
        },
      },
      description: 'Node',
    },
  },
});

export function initUserRoutes(app: OpenAPIHono<{ Variables: ContextVariable }>) {
  app.openapi(getUserProfile, async (c) => {
    const userId = c.get('token').userId;

    const user = await UserSO.init(userId);
    return c.json(user.toVO());
  });
}
