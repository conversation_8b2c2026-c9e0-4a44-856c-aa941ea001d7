/**
 * Site Admin API，总控API, 只能站长管理员调用
 */
import { Hono } from 'hono';
import { SiteSamlAPIs } from '@bika/domains/auth/apis';
import SiteAdminOpenAPIs from './site-admin-openapi';

const app = new Hono();

// 站点级SAML开放API, 不拦截
app.route('/saml', SiteSamlAPIs);

// 下面的API需要站长管理员验证密码凭证
app.route('/openapi', SiteAdminOpenAPIs);

// app.post('/zapier', async (c) => {
//   console.log('todo');
//   return c.json({});
// });

// app.post('/make', async (c) => {
//   console.log('todo');
//   return c.json({});
// });

// // 其它，通过EDGE_TOKEN校验或调用
// app.post('/others', async (c) => {
//   console.log('todo');
//   return c.json({});
// });

export default app;
