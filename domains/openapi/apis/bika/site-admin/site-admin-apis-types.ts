import { z } from '@hono/zod-openapi';

export const IdParamSchema = z.object({
  id: z
    .string()
    .min(1)
    .openapi({
      param: {
        name: 'id',
        in: 'path',
      },
      example: 'usrAbc123',
    }),
});

export const PaginationSchema = z.object({
  pageNo: z
    .number()
    .min(1)
    .optional()
    .default(1)
    .openapi({
      param: {
        name: 'pageNo',
        in: 'query',
        required: false,
      },
      example: 1,
    }),
  pageSize: z
    .number()
    .min(1)
    .max(50)
    .default(100)
    .optional()
    .openapi({
      param: {
        name: 'pageSize',
        in: 'query',
        required: false,
      },
      example: 100,
    }),
});

export const UserCreateDTOSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1),
  email: z.string().email(),
  phone: z.string().optional(),
});

export type UserCreateDTO = z.infer<typeof UserCreateDTOSchema>;

export const UserUpdateDTOSchema = z.object({
  name: z.string().optional(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
});

export type UserUpdateDTO = z.infer<typeof UserUpdateDTOSchema>;

export const SpaceCreateDTOSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1),
  owner: z.string(),
  customMemberId: z.string().optional(),
});
export type SpaceCreateDTO = z.infer<typeof SpaceCreateDTOSchema>;

export const SpaceUpdateDTOSchema = z.object({
  name: z.string().min(1),
});
export type SpaceUpdateDTO = z.infer<typeof SpaceUpdateDTOSchema>;

export const MemberCreateDTOSchema = z.object({
  id: z.string().optional(),
  userId: z.string(),
  spaceId: z.string(),
  name: z.string().optional(),
  isGuest: z.boolean().optional(),
  teamIds: z.string().array().optional(),
  roleIds: z.string().array().optional(),
});
export type MemberCreateDTO = z.infer<typeof MemberCreateDTOSchema>;

export const MemberUpdateDTOSchema = z.object({
  name: z.string().optional(),
  teamIds: z.string().array().optional(),
  roleIds: z.string().array().optional(),
});
export type MemberUpdateDTO = z.infer<typeof MemberUpdateDTOSchema>;

export const TeamCreateDTOSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1),
  spaceId: z.string(),
  parentId: z.string().optional(),
  sequence: z.number().optional(),
  isGuest: z.boolean().optional(),
});
export type TeamCreateDTO = z.infer<typeof TeamCreateDTOSchema>;

export const TeamUpdateDTOSchema = z.object({
  name: z.string().optional(),
});
export type TeamUpdateDTO = z.infer<typeof TeamUpdateDTOSchema>;

export type SiteAdminAuthVariables = { userId: string };
