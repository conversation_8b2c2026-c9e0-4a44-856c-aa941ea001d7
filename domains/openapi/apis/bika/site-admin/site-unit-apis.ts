import { z, createRoute, OpenAPIHono } from '@hono/zod-openapi';
import { SpaceSO } from '@bika/domains/space/server';
import { RoleController } from '@bika/domains/unit/apis';
import { MemberSO, TeamSO } from '@bika/domains/unit/server';
import { RoleSO } from '@bika/domains/unit/server/role-so';
import { UserSO } from '@bika/domains/user/server';
import { createResponseVOSchema, ResponseVOBuilder } from '@bika/types/openapi/vo';
import { PaginationInfoSchema } from '@bika/types/shared';
import { RoleCreateSchema, RoleListSchema, RoleUpdateSchema } from '@bika/types/unit/dto';
import { MemberVOSchema, RoleVOSchema, TeamBaseVOSchema } from '@bika/types/unit/vo';
import {
  IdParamSchema,
  MemberCreateDTOSchema,
  MemberUpdateDTOSchema,
  PaginationSchema,
  TeamCreateDTOSchema,
  TeamUpdateDTOSchema,
  SiteAdminAuthVariables,
} from './site-admin-apis-types';
// 站点级通讯录API列表

// 获取成员
const getMember = createRoute({
  method: 'get',
  path: '/members/{id}',
  request: {
    params: IdParamSchema,
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(MemberVOSchema.extend({ teams: z.array(TeamBaseVOSchema) })).openapi(
            'MemberResponse',
          ),
        },
      },
      description: 'Retrieve a member',
    },
  },
});

// 创建成员
const createMember = createRoute({
  method: 'post',
  path: '/members',
  request: {
    body: {
      content: {
        'application/json': {
          schema: MemberCreateDTOSchema,
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(MemberVOSchema.extend({ teams: z.array(TeamBaseVOSchema) })).openapi(
            'MemberResponse',
          ),
        },
      },
      description: 'Create a member',
    },
  },
});

// 更新成员
const updateMember = createRoute({
  method: 'put',
  path: '/members/{id}',
  request: {
    params: IdParamSchema,
    body: {
      content: {
        'application/json': {
          schema: MemberUpdateDTOSchema,
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(MemberVOSchema.extend({ teams: z.array(TeamBaseVOSchema) })).openapi(
            'MemberResponse',
          ),
        },
      },
      description: 'Update a member',
    },
  },
});

// 删除成员
const deleteMember = createRoute({
  method: 'delete',
  path: '/members/{id}',
  request: {
    params: IdParamSchema,
  },
  responses: {
    200: {
      description: 'Delete a member',
    },
  },
});

// 获取部门信息
const getTeam = createRoute({
  method: 'get',
  path: '/teams/{id}',
  request: {
    params: IdParamSchema,
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(TeamBaseVOSchema).openapi('TeamResponse'),
        },
      },
      description: 'Retrieve a team',
    },
  },
});

// 获取子部门列表
const getTeamChildren = createRoute({
  method: 'get',
  path: '/teams/{id}/children',
  request: {
    // 允许传0, 即代表根部门
    params: IdParamSchema,
    query: PaginationSchema.extend({
      spaceId: z.string().openapi({
        param: {
          name: 'spaceId',
          in: 'query',
          required: true,
          description: 'Indicate the team belongs to which space',
        },
        example: 'spcAbc123',
      }),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(
            PaginationInfoSchema.extend({
              data: z.array(TeamBaseVOSchema),
            }),
          ).openapi('TeamListResponse'),
        },
      },
      description: 'Retrieve children teams',
    },
  },
});

// 创建部门
const createTeam = createRoute({
  method: 'post',
  path: '/teams',
  request: {
    body: {
      content: {
        'application/json': {
          schema: TeamCreateDTOSchema,
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(TeamBaseVOSchema).openapi('TeamResponse'),
        },
      },
      description: 'Create a team',
    },
  },
});

// 更新部门
const updateTeam = createRoute({
  method: 'put',
  path: '/teams/{id}',
  request: {
    params: IdParamSchema,
    body: {
      content: {
        'application/json': {
          schema: TeamUpdateDTOSchema,
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(TeamBaseVOSchema).openapi('TeamResponse'),
        },
      },
      description: 'Update a team',
    },
  },
});

// 删除部门
const deleteTeam = createRoute({
  method: 'delete',
  path: '/teams/{id}',
  request: {
    params: IdParamSchema,
  },
  responses: {
    200: {
      description: 'Delete a team',
    },
  },
});

const RoleRoute = {
  listRoles: createRoute({
    tags: ['Roles'],
    description: 'List Roles',
    method: 'get',
    path: '/roles',
    request: {
      query: RoleListSchema,
    },
    responses: {
      200: {
        content: {
          'application/json': {
            schema: createResponseVOSchema(
              PaginationInfoSchema.extend({ roles: z.array(RoleVOSchema) }).openapi('RolePaginationVO'),
            ),
          },
        },
        description: 'Success Get Roles',
      },
    },
  }),
  createRole: createRoute({
    tags: ['Roles'],
    description: 'Create Role',
    method: 'post',
    path: '/roles',
    request: {
      body: {
        content: {
          'application/json': {
            schema: RoleCreateSchema,
          },
        },
      },
    },
    responses: {
      200: {
        content: {
          'application/json': {
            schema: createResponseVOSchema(RoleVOSchema.openapi('RoleVO')),
          },
        },
        description: 'Success Create Role',
      },
    },
  }),
  updateRole: createRoute({
    tags: ['Roles'],
    description: 'Create Role',
    method: 'put',
    path: '/roles/:id',
    request: {
      params: z.object({
        id: z.string().openapi({ param: { name: 'id', in: 'path', required: true } }),
      }),
      body: {
        content: {
          'application/json': {
            schema: RoleUpdateSchema.omit({ spaceId: true, id: true }),
          },
        },
      },
    },
    responses: {
      200: {
        content: {
          'application/json': {
            schema: createResponseVOSchema(RoleVOSchema.openapi('RoleVO')),
          },
        },
        description: 'Success Update Role',
      },
    },
  }),
  deleteRole: createRoute({
    tags: ['Roles'],
    description: 'Delete Role',
    method: 'delete',
    path: '/roles/:id',
    request: {
      params: z.object({
        id: z.string().openapi({ param: { name: 'id', in: 'path', required: true } }),
      }),
    },
    responses: {
      200: {
        content: {
          'application/json': {
            schema: createResponseVOSchema(z.object({}).optional()),
          },
        },
        description: 'Success Delete Role',
      },
    },
  }),
};
/**
 * 定义站点通讯录API
 */
export const initSiteUnitAPIs = (app: OpenAPIHono<{ Variables: SiteAdminAuthVariables }>) => {
  app.openapi(getMember, async (c) => {
    const { id } = c.req.valid('param');
    const member = await MemberSO.init(id);
    const memberVO = await member.toVO({ withDetail: true });
    return c.json(ResponseVOBuilder.success(memberVO), 200);
  });
  app.openapi(createMember, async (c) => {
    const userId = c.get('userId');
    const memberDTO = c.req.valid('json');
    const space = await SpaceSO.init(memberDTO.spaceId);
    let teamIds: string[] = [];
    if (memberDTO.teamIds && memberDTO.teamIds.length > 0) {
      const teams = await space.getTeams(memberDTO.teamIds, true);
      teamIds = teams.map((team) => team.id);
    } else {
      const rootTeam = await space.getRootTeam(memberDTO.isGuest);
      teamIds = [rootTeam.id];
    }
    const member = await space.addUser({
      userId: memberDTO.userId,
      customMemberId: memberDTO.id,
      customMemberName: memberDTO.name,
      teamIds,
      roleIds: memberDTO.roleIds,
      isGuest: memberDTO.isGuest || false,
      createdBy: userId,
    });
    const memberVO = await member.toVO({ withDetail: true });
    return c.json(ResponseVOBuilder.success(memberVO), 200);
  });

  app.openapi(updateMember, async (c) => {
    const userId = c.get('userId');
    const user = await UserSO.init(userId);
    const { id } = c.req.valid('param');
    const memberDTO = c.req.valid('json');
    const member = await MemberSO.init(id);
    await member.update(user, { name: memberDTO.name, teamIds: memberDTO.teamIds, roleIds: memberDTO.roleIds });
    const memberVO = await member.toVO({ withDetail: true });
    return c.json(ResponseVOBuilder.success(memberVO), 200);
  });

  app.openapi(deleteMember, async (c) => {
    const { id } = c.req.valid('param');
    const member = await MemberSO.init(id);
    await member.delete();
    return c.json({}, 200);
  });

  app.openapi(getTeam, async (c) => {
    const { id } = c.req.valid('param');
    const team = await TeamSO.init(id);
    const teamVO = await team.toVO({ withDetail: true });
    return c.json(ResponseVOBuilder.success(teamVO), 200);
  });

  app.openapi(getTeamChildren, async (c) => {
    const { id } = c.req.valid('param');
    const { spaceId, pageNo = 1, pageSize = 10 } = c.req.valid('query');
    const space = await SpaceSO.init(spaceId);
    const parentTeam = id === '0' ? await space.getRootTeam() : await space.getTeam(id);
    const { pagination, list } = await TeamSO.find({ spaceId, parentId: parentTeam.id }, { pageNo, pageSize });
    const response = ResponseVOBuilder.success({
      pagination,
      data: await Promise.all(list.map((t) => t.toVO())),
    });
    return c.json(response, 200);
  });

  app.openapi(createTeam, async (c) => {
    const userId = c.get('userId');
    const teamDTO = c.req.valid('json');
    const space = await SpaceSO.init(teamDTO.spaceId);
    const parentTeam =
      teamDTO.parentId && teamDTO.parentId.length > 0
        ? await space.getTeam(teamDTO.parentId)
        : await space.getRootTeam(teamDTO.isGuest);
    const subTeam = await parentTeam.createSubTeam({ userId, id: teamDTO.id, name: teamDTO.name });
    const teamVO = await subTeam.toVO({ withDetail: true });
    return c.json(ResponseVOBuilder.success(teamVO), 200);
  });

  app.openapi(updateTeam, async (c) => {
    const userId = c.get('userId');
    const { id } = c.req.valid('param');
    const teamDTO = c.req.valid('json');
    const team = await TeamSO.init(id);
    await team.update({ name: teamDTO.name, updatedBy: userId });
    const teamVO = await team.toVO({ withDetail: true });
    return c.json(ResponseVOBuilder.success(teamVO), 200);
  });

  app.openapi(deleteTeam, async (c) => {
    const { id } = c.req.valid('param');
    const team = await TeamSO.init(id);
    await team.delete();
    return c.json({}, 200);
  });

  // role API
  app.openapi(RoleRoute.listRoles, async (c) => {
    const userId = c.get('userId');
    const user = await UserSO.init(userId);
    const query = RoleListSchema.parse(c.req.query());
    const roles = await RoleController.list(user, query);
    return c.json(ResponseVOBuilder.success(roles));
  });

  app.openapi(RoleRoute.createRole, async (c) => {
    const userId = c.get('userId');
    const user = await UserSO.init(userId);
    const dto = c.req.valid('json');
    const space = await SpaceSO.init(dto.spaceId);
    const roleSO = await space.createRole(user.id, {
      id: dto.id,
      name: dto.name,
      manageSpace: dto.manageSpace,
      setting: dto.permissions && { permissions: dto.permissions },
    });
    const vo = roleSO.toVO({ locale: user.locale });
    return c.json(ResponseVOBuilder.success(vo), 200);
  });

  app.openapi(RoleRoute.updateRole, async (c) => {
    const userId = c.get('userId');
    const { id } = c.req.valid('param');
    const user = await UserSO.init(userId);
    const dto = c.req.valid('json');
    const role = await RoleSO.init(id);
    await role.update(user.id, {
      name: dto.name,
      manageSpace: dto.manageSpace,
      setting: dto.permissions && { permissions: dto.permissions },
    });
    const vo = role.toVO({ locale: user.locale });
    return c.json(ResponseVOBuilder.success(vo));
  });

  app.openapi(RoleRoute.deleteRole, async (c) => {
    const { id } = c.req.valid('param');
    const role = await RoleSO.init(id);
    await role.delete();
    return c.json(ResponseVOBuilder.success({}));
  });
};
