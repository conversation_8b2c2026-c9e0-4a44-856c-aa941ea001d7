import { createRoute, OpenAPIHono, z } from '@hono/zod-openapi';
import { OutgoingWebhookSO } from '@bika/domains/event/server/event/outgoing-webhook-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { ServerEventTypesSchema } from '@bika/types/events/bo';
import { OutgoingWebhookCreateDTOSchema } from '@bika/types/events/dto';
import { BaseOutgoingWebhookVOSchema } from '@bika/types/events/vo';
import { createResponseVOSchema, ResponseVOBuilder } from '@bika/types/openapi/vo';
import { SiteAdminAuthVariables } from './site-admin-apis-types';

// create site outgoing webhook
export const registerSiteOutgoingWebhook = createRoute({
  tags: ['Outgoing Webhooks'],
  description: `Register Site's Outgoing Webhooks`,
  method: 'post',
  path: '/outgoing-webhooks',
  request: {
    body: {
      content: {
        'application/json': {
          schema: OutgoingWebhookCreateDTOSchema.extend({
            eventType: z.enum([
              ServerEventTypesSchema.enum.BEFORE_MEMBER_JOINED,
              ServerEventTypesSchema.enum.ON_MEMBER_INVITE,
              ServerEventTypesSchema.enum.DO_MEMBER_INVITE,
            ]),
          }),
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(BaseOutgoingWebhookVOSchema),
        },
      },
      description: 'Outgoing Webhook Value Object (VO)',
    },
  },
});

export const listSiteOutgoingWebhooks = createRoute({
  tags: ['Outgoing Webhooks'],
  description: `List Site's Outgoing Webhooks`,
  method: 'get',
  path: '/outgoing-webhooks',
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(BaseOutgoingWebhookVOSchema),
        },
      },
      description: 'Outgoing Webhook Value Object (VO)',
    },
  },
});

export const deleteSiteOutgoingWebhook = createRoute({
  tags: ['Outgoing Webhooks'],
  description: `Delete Site's Outgoing Webhook`,
  method: 'delete',
  path: '/outgoing-webhooks/{id}',
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(BaseOutgoingWebhookVOSchema),
        },
      },
      description: 'Outgoing Webhook Value Object (VO)',
    },
  },
});

export const initSiteOutgoingWebhookAPIs = (app: OpenAPIHono<{ Variables: SiteAdminAuthVariables }>) => {
  app.openapi(listSiteOutgoingWebhooks, async (c) => {
    const hooks = await OutgoingWebhookSO.list('SITE_ADMIN');
    const hookVos = await Promise.all(hooks.map((hook) => hook.toVO()));
    return c.json(ResponseVOBuilder.success(hookVos));
  });

  app.openapi(registerSiteOutgoingWebhook, async (c) => {
    const userId = c.get('userId');

    const user = await UserSO.init(userId);

    const bo = c.req.valid('json');

    const hook = await OutgoingWebhookSO.create({ bo, scope: 'SITE_ADMIN', userId: user.id });
    return c.json(ResponseVOBuilder.success(hook.toVO()));
  });

  app.openapi(deleteSiteOutgoingWebhook, async (c) => {
    const { id } = c.req.param();

    await OutgoingWebhookSO.delete(id);

    return c.json(ResponseVOBuilder.success(null));
  });
};
