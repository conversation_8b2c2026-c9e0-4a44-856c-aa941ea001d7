import { createRoute, OpenAPIHono } from '@hono/zod-openapi';
import { SpaceSO } from '@bika/domains/space/server';
import { UserSO } from '@bika/domains/user/server';
import { createResponseVOSchema, ResponseVOBuilder } from '@bika/types/openapi/vo';
import { SpaceVOSchema } from '@bika/types/space/vo';
import {
  IdParamSchema,
  SpaceCreateDTOSchema,
  SpaceUpdateDTOSchema,
  SiteAdminAuthVariables,
} from './site-admin-apis-types';

// 站点级空间站API列表
// 创建空间
const createSpace = createRoute({
  method: 'post',
  path: '/spaces',
  request: {
    body: {
      content: {
        'application/json': {
          schema: SpaceCreateDTOSchema,
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(SpaceVOSchema).openapi('Space'),
        },
      },
      description: 'Create a space',
    },
  },
});

// 更改空间站
const updateSpace = createRoute({
  method: 'put',
  path: '/spaces/{id}',
  request: {
    params: IdParamSchema,
    body: {
      content: {
        'application/json': {
          schema: SpaceUpdateDTOSchema,
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(SpaceVOSchema).openapi('Space'),
        },
      },
      description: 'Update a space',
    },
  },
});

/**
 * 定义站点空间站API
 */
export const initSiteSpaceAPIs = (app: OpenAPIHono<{ Variables: SiteAdminAuthVariables }>) => {
  app.openapi(createSpace, async (c) => {
    const { id, name, owner, customMemberId } = c.req.valid('json');
    const user = await UserSO.init(owner);
    const space = await SpaceSO.createSpace(user.id, { id, name, memberId: customMemberId });
    const spaceVO = await space.toVO();
    return c.json(ResponseVOBuilder.success(spaceVO), 200);
  });
  app.openapi(updateSpace, async (c) => {
    const userId = c.get('userId');
    const user = await UserSO.init(userId);
    const { id } = c.req.valid('param');
    const { name } = c.req.valid('json');
    const space = await SpaceSO.init(id);
    await space.update(user, { name });
    const updatedSpace = await SpaceSO.init(space.id);
    const spaceVO = await updatedSpace.toVO();
    return c.json(ResponseVOBuilder.success(spaceVO), 200);
  });
};
