import { basicAuth } from 'hono/basic-auth';
import { UserSO } from '@bika/domains/user/server';
import { SiteAdminSO } from '@bika/domains/user/server/site-admin-so';

export const SiteAdminAuth = () =>
  basicAuth({
    verifyUser: async (username, password, c) => {
      // 站长管理员账号密码校验
      const user = await UserSO.findByUsername(username);
      if (!user) {
        return false;
      }
      const isMatch = await user.validatePassword(password);
      if (!isMatch) {
        return false;
      }
      const isAdmin = await SiteAdminSO.isAdmin(user.id);
      if (isAdmin) {
        c.set('userId', user.id);
        return true;
      }
      return false;
    },
  });
