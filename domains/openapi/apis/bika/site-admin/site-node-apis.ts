import { createRoute, OpenAPIHono, z } from '@hono/zod-openapi';
import { NodeController } from '@bika/domains/node/apis';
import { UserSO } from '@bika/domains/user/server/user-so';
import { NodeGrantPermissionSchema } from '@bika/types/node/dto';
import { createResponseVOSchema, ResponseVOBuilder } from '@bika/types/openapi/vo';
import { SiteAdminAuthVariables } from './site-admin-apis-types';

export const grantNodePermission = createRoute({
  tags: ['Nodes'],
  description: `Grant permission`,
  method: 'post',
  path: '/nodes/{id}/permissions',
  request: {
    params: z.object({
      id: z
        .string()
        .describe('Node ID')
        .openapi({ param: { name: 'id', in: 'path', required: true } }),
    }),
    body: {
      content: {
        'application/json': {
          schema: NodeGrantPermissionSchema.omit({ spaceId: true, id: true }),
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(z.object({}).optional()),
        },
      },
      description: 'Success Grant Node Permission',
    },
  },
});

export const initSiteNodeAPIs = (app: OpenAPIHono<{ Variables: SiteAdminAuthVariables }>) => {
  app.openapi(grantNodePermission, async (c) => {
    const userId = c.get('userId');
    const user = await UserSO.init(userId);
    const { id } = c.req.param();
    const { unitIds, privilege } = c.req.valid('json');
    await NodeController.grantNodePermissionByUser(user, { id, unitIds, privilege });
    return c.json(ResponseVOBuilder.success(null));
  });
};
