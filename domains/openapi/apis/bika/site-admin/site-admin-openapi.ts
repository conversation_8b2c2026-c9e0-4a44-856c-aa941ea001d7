import { OpenAPIHono } from '@hono/zod-openapi';
import { HTTPException } from 'hono/http-exception';
import { errors, ServerError } from '@bika/contents/config/server/error';
import { ResponseVOBuilder } from '@bika/types/openapi/vo';
import { SiteAdminAuthVariables } from './site-admin-apis-types';
import { SiteAdminAuth } from './site-admin-auth-middleware';
import { initSiteNodeAPIs } from './site-node-apis';
import { initSiteOutgoingWebhookAPIs } from './site-outgoing-webhook-apis';
import { initSiteSpaceAPIs } from './site-space-apis';
import { initSiteUnitAPIs } from './site-unit-apis';
import { initSiteUserAPIs } from './site-user-apis';

const app = new OpenAPIHono<{ Variables: SiteAdminAuthVariables }>({
  strict: false,
  defaultHook: (result, c) => {
    // 请求参数验证错误统一处理
    if (!result.success) {
      const res = ResponseVOBuilder.error('Parameter Validation Error', 400);
      return c.json(res, 400);
    }
    return result;
  },
});

// 定义站点管理员API文档
app.doc('/openapi.json', {
  openapi: '3.0.0',
  info: {
    version: process.env.VERSION || 'UNKNOW VERSION',
    title: 'Bika.ai Site Admin OpenAPI Documentation',
    'x-logo': {
      url: '/assets/icons/logo/bika-logo-text.png',
      altText: 'Bika LOGO',
      href: '/',
    },
  },
  // Url prefix
  servers: [
    {
      url: '/api/site-admin/openapi',
    },
  ],
});

// 注册 Basic Auth 安全方案
app.openAPIRegistry.registerComponent('securitySchemes', 'BasicAuth', {
  type: 'http',
  scheme: 'basic',
});

app.use(SiteAdminAuth());

// 全局异常处理
app.onError((err, c) => {
  //  ZOD 参数验证错误不会经过这里, 请注意, 会在 defaultHook 中处理
  //   console.log(err instanceof ServerError);
  if (err instanceof ServerError) {
    const res = ResponseVOBuilder.error(err.message, err.code);
    return c.json(res, 500);
  }
  // HTTPException 异常处理
  if (err instanceof HTTPException) {
    const res = ResponseVOBuilder.error(err.message, err.status);
    return c.json(res, err.status);
  }
  // 很少出现的未知错误, 除非前面两个都没处理
  const unknownServerError = ResponseVOBuilder.error(errors.common.unknown.message.en, errors.common.unknown.code);
  return c.json(unknownServerError, 500);
});

// 初始化站点管理员API列表
initSiteUserAPIs(app);
initSiteSpaceAPIs(app);
initSiteUnitAPIs(app);
initSiteOutgoingWebhookAPIs(app);
initSiteNodeAPIs(app);
export default app;
