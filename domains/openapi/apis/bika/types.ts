import { z } from '@hono/zod-openapi';
import { type UserDeveloperTokenSO } from '@bika/domains/user/server/user-developer-token-so';
import type { ApiFetchRequestContext } from '@bika/types/user/vo';

export type ContextVariable = {
  token: UserDeveloperTokenSO;
  context: ApiFetchRequestContext;
};
export const SpaceParamSchema = z.object({
  spaceId: z.string().openapi({ param: { name: 'spaceId', in: 'path', required: true }, example: 'spc***' }),
});

export const NodeParamSchema = SpaceParamSchema.extend({
  nodeId: z.string().openapi({ param: { name: 'nodeId', in: 'path', required: true }, example: 'dat***' }),
});
