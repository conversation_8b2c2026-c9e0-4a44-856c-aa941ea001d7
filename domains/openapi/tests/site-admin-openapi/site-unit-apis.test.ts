import { nanoid } from 'nanoid';
import { describe, expect, test } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import * as utils from '@bika/domains/shared/server';
import { SiteAdminSO } from '@bika/domains/user/server/site-admin-so';
import { SpaceVO } from '@bika/types/space/vo';
import { MemberVO, TeamPaginationVO, TeamVO } from '@bika/types/unit/vo';
import { UserVO } from '@bika/types/user/vo';
import SiteAdminAPIs from '../../apis/bika/site-admin/site-admin-apis';
import {
  MemberCreateDTO,
  MemberUpdateDTO,
  SpaceCreateDTO,
  TeamCreateDTO,
  UserCreateDTO,
} from '../../apis/bika/site-admin/site-admin-apis-types';

describe('Site Unit APIs', () => {
  const createUserRequest = async (user: UserCreateDTO, encodedCredentials: string) => {
    const response = await SiteAdminAPIs.request('/openapi/users', {
      method: 'POST',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(user),
    });
    expect(response.status).toBe(200);
    const responseData = await response.json();
    const userVO = responseData.data as UserVO;
    expect(userVO.id).toBe(user.id);
    expect(userVO.name).toBe(user.name);
    expect(userVO.email).toBe(user.email);
    expect(userVO.phone).toBe(user.phone);
    return userVO;
  };
  const createTeamRequest = async (team: TeamCreateDTO, encodedCredentials: string) => {
    const response = await SiteAdminAPIs.request('/openapi/teams', {
      method: 'POST',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(team),
    });
    expect(response.status).toBe(200);
    const responseData = await response.json();
    const teamVO = responseData.data as TeamVO;
    expect(teamVO.id).toBe(team.id);
    expect(teamVO.name).toBe(team.name);
    return teamVO;
  };
  const getTeamRequest = async (teamId: string, encodedCredentials: string) => {
    const response = await SiteAdminAPIs.request(`/openapi/teams/${teamId}`, {
      method: 'GET',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
      },
    });
    expect(response.status).toBe(200);
    const responseData = await response.json();
    const teamVO = responseData.data as TeamVO;
    expect(teamVO.id).toBe(teamId);
    return teamVO;
  };
  const createMemberRequest = async (member: MemberCreateDTO, encodedCredentials: string) => {
    const response = await SiteAdminAPIs.request('/openapi/members', {
      method: 'POST',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(member),
    });
    expect(response.status).toBe(200);
    const responseData = await response.json();
    const memberVO = responseData.data as MemberVO;
    expect(memberVO.id).toBe(member.id);
    expect(memberVO.userId).toBe(member.userId);
    return memberVO;
  };
  const getMemberRequest = async (memberId: string, encodedCredentials: string) => {
    const response = await SiteAdminAPIs.request(`/openapi/members/${memberId}`, {
      method: 'GET',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
      },
    });
    expect(response.status).toBe(200);
    const responseData = await response.json();
    const memberVO = responseData.data as MemberVO;
    expect(memberVO.id).toBe(memberId);
    return memberVO;
  };
  test('contact crud test', async () => {
    // 初始化一个站点管理员
    const user = await MockContext.createUser('site-admin');
    await user.updatePassword({ password: '123456', confirmPassword: '123456' });
    await SiteAdminSO.addAdmin(user.id);

    const encodedCredentials = Buffer.from(`${user.email}:123456`).toString('base64');

    // 创建一个用户
    const userId = nanoid();
    const userDTO: UserCreateDTO = {
      id: userId, // 自定义ID
      name: 'marry',
      email: utils.generateEmail('aitable.ai'),
      phone: '4455542243',
    };
    await createUserRequest(userDTO, encodedCredentials);

    // 创建一个空间站
    const spaceId = `${nanoid()}_space`;
    const spaceDTO: SpaceCreateDTO = {
      id: spaceId,
      name: 'space',
      owner: userId,
      customMemberId: `${nanoid()}_marry`,
    };

    const spaceResponse = await SiteAdminAPIs.request('/openapi/spaces', {
      method: 'POST',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(spaceDTO),
    });

    expect(spaceResponse.status).toBe(200);
    const spaceResponseData = await spaceResponse.json();
    const spaceVO = spaceResponseData.data as SpaceVO;
    expect(spaceVO.id).toBe(spaceId);
    expect(spaceVO.name).toBe(spaceDTO.name);
    expect(spaceVO.createBy).toBe(userDTO.name);

    // 初始化几个部门
    // 产品工程 -> 研发部 -> 前端组
    // 产品工程 -> 研发部 -> 后端组
    // 市场运营 -> 运营部
    // 市场运营 -> 销售部
    const product: TeamCreateDTO = {
      id: `${nanoid()}_product`,
      name: '产品工程',
      spaceId,
    };
    await createTeamRequest(product, encodedCredentials);
    const develop: TeamCreateDTO = {
      id: `${nanoid()}_develop`,
      name: '研发部',
      spaceId,
      parentId: product.id,
    };
    await createTeamRequest(develop, encodedCredentials);
    const front: TeamCreateDTO = {
      id: `${nanoid()}_front`,
      name: '前端组',
      spaceId,
      parentId: develop.id,
    };
    await createTeamRequest(front, encodedCredentials);
    const back: TeamCreateDTO = {
      id: `${nanoid()}_back`,
      name: '后端组',
      spaceId,
      parentId: develop.id,
    };
    await createTeamRequest(back, encodedCredentials);
    const testing: TeamCreateDTO = {
      id: `${nanoid()}_testing`,
      name: '测试组',
      spaceId,
      parentId: develop.id,
    };
    await createTeamRequest(testing, encodedCredentials);
    const market: TeamCreateDTO = {
      id: `${nanoid()}_market`,
      name: '市场运营',
      spaceId,
    };
    await createTeamRequest(market, encodedCredentials);
    const operation: TeamCreateDTO = {
      id: `${nanoid()}_operation`,
      name: '运营部',
      spaceId,
      parentId: market.id,
    };
    await createTeamRequest(operation, encodedCredentials);
    const sales: TeamCreateDTO = {
      id: `${nanoid()}_sales`,
      name: '销售部',
      spaceId,
      parentId: market.id,
    };
    await createTeamRequest(sales, encodedCredentials);

    // 初始化几个员工
    // 前端组 -> jack
    // 后端组 -> simon
    // 测试组 -> petter
    // 前后端组 -> jeff
    // 运营部 -> lily
    // 销售部 -> tom
    // 运营部/销售部 -> chris
    const userJack: UserCreateDTO = {
      id: nanoid(),
      name: 'jack',
      email: utils.generateEmail('aitable.ai'),
      phone: '13611112222',
    };
    await createUserRequest(userJack, encodedCredentials);
    const jack: MemberCreateDTO = {
      id: `${nanoid()}_jack`,
      userId: userJack.id!,
      spaceId,
      name: 'jack',
      teamIds: [front.id!],
    };
    await createMemberRequest(jack, encodedCredentials);
    const userSimon: UserCreateDTO = {
      id: nanoid(),
      name: 'simon',
      email: utils.generateEmail('aitable.ai'),
      phone: '13611112223',
    };
    await createUserRequest(userSimon, encodedCredentials);
    const simon: MemberCreateDTO = {
      id: `${nanoid()}_simon`,
      userId: userSimon.id!,
      spaceId,
      name: 'simon',
      teamIds: [back.id!],
    };
    await createMemberRequest(simon, encodedCredentials);
    const userPetter: UserCreateDTO = {
      id: nanoid(),
      name: 'petter',
      email: utils.generateEmail('aitable.ai'),
      phone: '13611112229',
    };
    await createUserRequest(userPetter, encodedCredentials);
    const petter: MemberCreateDTO = {
      id: `${nanoid()}_petter`,
      userId: userPetter.id!,
      spaceId,
      name: 'petter',
      teamIds: [testing.id!],
    };
    await createMemberRequest(petter, encodedCredentials);
    const userJeff: UserCreateDTO = {
      id: nanoid(),
      name: 'jeff',
      email: utils.generateEmail('aitable.ai'),
      phone: '13611112224',
    };
    await createUserRequest(userJeff, encodedCredentials);
    const jeff: MemberCreateDTO = {
      id: `${nanoid()}_jeff`,
      userId: userJeff.id!,
      spaceId,
      name: 'jeff',
      teamIds: [front.id!, back.id!],
    };
    await createMemberRequest(jeff, encodedCredentials);
    const userLily: UserCreateDTO = {
      id: nanoid(),
      name: 'lily',
      email: utils.generateEmail('aitable.ai'),
      phone: '13611112225',
    };
    await createUserRequest(userLily, encodedCredentials);
    const lily: MemberCreateDTO = {
      id: `${nanoid()}_lily`,
      userId: userLily.id!,
      spaceId,
      name: 'lily',
      teamIds: [operation.id!],
    };
    await createMemberRequest(lily, encodedCredentials);
    const userTom: UserCreateDTO = {
      id: nanoid(),
      name: 'tom',
      email: utils.generateEmail('aitable.ai'),
      phone: '13611112226',
    };
    await createUserRequest(userTom, encodedCredentials);
    const tom: MemberCreateDTO = {
      id: `${nanoid()}_tom`,
      userId: userTom.id!,
      spaceId,
      name: 'tom',
      teamIds: [sales.id!],
    };
    await createMemberRequest(tom, encodedCredentials);

    const userChris: UserCreateDTO = {
      id: nanoid(),
      name: 'chris',
      email: utils.generateEmail('aitable.ai'),
      phone: '13611112227',
    };
    await createUserRequest(userChris, encodedCredentials);
    const chris: MemberCreateDTO = {
      id: `${nanoid()}_chris`,
      userId: userChris.id!,
      spaceId,
      name: 'chris',
      teamIds: [operation.id!, sales.id!],
    };
    await createMemberRequest(chris, encodedCredentials);

    // 检查一下
    // 前端组 -> jack
    // 后端组 -> simon
    // 测试组 -> petter
    // 前后端组 -> jeff
    // 运营部 -> lily
    // 销售部 -> tom
    // 运营部/销售部 -> chris

    // jack 在前端组, simon在后端组 前端组有jack和jeff, 后端组有simon和jeff
    const jackMemberVO = await getMemberRequest(jack.id!, encodedCredentials);
    expect(jackMemberVO.teams?.map((team) => team.id)).toContain(front.id!);
    const simonMemberVO = await getMemberRequest(simon.id!, encodedCredentials);
    expect(simonMemberVO.teams?.map((team) => team.id)).toContain(back.id!);
    const jeffMemberVO = await getMemberRequest(jeff.id!, encodedCredentials);
    expect(jeffMemberVO.teams?.map((team) => team.id)).toEqual([front.id!, back.id!]);
    const frontTeamVO = await getTeamRequest(front.id!, encodedCredentials);
    expect(frontTeamVO.memberCount).toEqual(2);
    expect(frontTeamVO.children).toBeUndefined();
    expect(frontTeamVO.parentId).toBe(develop.id);
    const backTeamVO = await getTeamRequest(back.id!, encodedCredentials);
    expect(backTeamVO.memberCount).toEqual(2);
    expect(backTeamVO.children).toBeUndefined();
    expect(backTeamVO.parentId).toBe(develop.id);

    // 改一下前端组的名字为《大前端》
    const updateFrontTeam = {
      name: '大前端',
    };
    const updateFrontTeamResponse = await SiteAdminAPIs.request(`/openapi/teams/${front.id}`, {
      method: 'PUT',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateFrontTeam),
    });
    expect(updateFrontTeamResponse.status).toBe(200);
    const updatedFrontTeamVO = await getTeamRequest(front.id!, encodedCredentials);
    expect(updatedFrontTeamVO.name).toBe(updateFrontTeam.name);

    // 改一下jeff的名字为《jeffrey》, 并将她从前端组移除
    const updateJeffMember: MemberUpdateDTO = {
      name: 'jeffrey',
      teamIds: [back.id!],
    };
    const updateJeffMemberResponse = await SiteAdminAPIs.request(`/openapi/members/${jeff.id}`, {
      method: 'PUT',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateJeffMember),
    });
    expect(updateJeffMemberResponse.status).toBe(200);
    const updatedJeffMemberVO = await getMemberRequest(jeff.id!, encodedCredentials);
    expect(updatedJeffMemberVO.name).toBe(updateJeffMember.name);
    expect(updatedJeffMemberVO.teams?.map((team) => team.id)).toEqual([back.id!]);

    // 删除petter
    const deletePetterResponse = await SiteAdminAPIs.request(`/openapi/members/${petter.id}`, {
      method: 'DELETE',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
      },
    });
    expect(deletePetterResponse.status).toBe(200);
    // 测试组应该没有这个人了
    const testingTeamVO = await getTeamRequest(testing.id!, encodedCredentials);
    expect(testingTeamVO.memberCount).toBe(0);

    // 现在删除测试组
    const deleteTestingTeamResponse = await SiteAdminAPIs.request(`/openapi/teams/${testing.id}`, {
      method: 'DELETE',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
      },
    });
    expect(deleteTestingTeamResponse.status).toBe(200);
    // 测试组应该没有了
    const developTeamChildrenResponse = await SiteAdminAPIs.request(
      `/openapi/teams/${develop.id}/children?spaceId=${spaceId}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Basic ${encodedCredentials}`,
        },
      },
    );
    expect(developTeamChildrenResponse.status).toBe(200);
    const developTeamChildrenResponseData = await developTeamChildrenResponse.json();
    const developTeamChildrenVO = developTeamChildrenResponseData.data as TeamPaginationVO;
    expect(developTeamChildrenVO.pagination.total).toBe(2);
    expect(developTeamChildrenVO.data.map((team) => team.id)).toEqual([front.id!, back.id!]);
  });
});
