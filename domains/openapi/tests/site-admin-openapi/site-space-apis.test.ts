import { nanoid } from 'nanoid';
import { describe, expect, test } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { SiteAdminSO } from '@bika/domains/user/server/site-admin-so';
import { SpaceVO } from '@bika/types/space/vo';
import { UserVO } from '@bika/types/user/vo';
import SiteAdminAPIs from '../../apis/bika/site-admin/site-admin-apis';
import { SpaceCreateDTO, SpaceUpdateDTO, UserCreateDTO } from '../../apis/bika/site-admin/site-admin-apis-types';

describe('Site Space APIs', () => {
  test('space crud test', async () => {
    // 初始化一个站点管理员
    const user = await MockContext.createUser('site-admin');
    await user.updatePassword({ password: '123456', confirmPassword: '123456' });
    await SiteAdminSO.addAdmin(user.id);

    const encodedCredentials = Buffer.from(`${user.email}:123456`).toString('base64');

    // 创建一个用户
    const userId = nanoid();
    const userDTO: UserCreateDTO = {
      id: userId, // 自定义ID
      name: 'harry',
      email: '<EMAIL>',
      phone: '333334444',
    };
    const response = await SiteAdminAPIs.request('/openapi/users', {
      method: 'POST',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userDTO),
    });

    expect(response.status).toBe(200);
    const responseData = await response.json();
    const userVO = responseData.data as UserVO;
    expect(userVO.id).toBe(userId);
    expect(userVO.name).toBe(userDTO.name);
    expect(userVO.email).toBe(userDTO.email);
    expect(userVO.phone).toBe(userDTO.phone);

    // 创建一个空间站
    const spaceId = nanoid();
    const spaceDTO: SpaceCreateDTO = {
      id: spaceId,
      name: 'space',
      owner: userId,
    };

    const spaceResponse = await SiteAdminAPIs.request('/openapi/spaces', {
      method: 'POST',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(spaceDTO),
    });

    expect(spaceResponse.status).toBe(200);
    const spaceResponseData = await spaceResponse.json();
    const spaceVO = spaceResponseData.data as SpaceVO;
    expect(spaceVO.id).toBe(spaceId);
    expect(spaceVO.name).toBe(spaceDTO.name);
    expect(spaceVO.createBy).toBe(userVO.name);

    // 创建重复ID的空间
    const errResponse = await SiteAdminAPIs.request('/openapi/spaces', {
      method: 'POST',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(spaceDTO),
    });

    expect(errResponse.status).toBe(500);
    const errResponseData = await errResponse.json();
    // Space already exists
    expect(errResponseData.code).toBe(3006);

    // 更新空间站名称
    const updateSpaceDTO: SpaceUpdateDTO = {
      name: 'new space name',
    };

    const updateSpaceResponse = await SiteAdminAPIs.request(`/openapi/spaces/${spaceId}`, {
      method: 'PUT',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateSpaceDTO),
    });

    expect(updateSpaceResponse.status).toBe(200);
    const updateSpaceResponseData = await updateSpaceResponse.json();
    const updateSpaceVO = updateSpaceResponseData.data as SpaceVO;
    expect(updateSpaceVO.id).toBe(spaceId);
    expect(updateSpaceVO.name).toBe(updateSpaceDTO.name);

    // 更改一个不存在的空间站
    const updateNotfoundSpaceResponse = await SiteAdminAPIs.request(`/openapi/spaces/11111`, {
      method: 'PUT',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateSpaceDTO),
    });

    expect(updateNotfoundSpaceResponse.status).toBe(500);
    const updateNotfoundSpaceResponseData = await updateNotfoundSpaceResponse.json();
    // Space not found
    expect(updateNotfoundSpaceResponseData.code).toBe(3005);
  });
});
