import { random } from 'lodash';
import { describe, expect, test } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { SiteAdminSO } from '@bika/domains/user/server/site-admin-so';
import { UserVO } from '@bika/types/user/vo';
import SiteAdminAPIs from '../../apis/bika/site-admin/site-admin-apis';
import { UserCreateDTO, UserUpdateDTO } from '../../apis/bika/site-admin/site-admin-apis-types';

describe('Site User APIs', () => {
  test('site admin authroize test', async () => {
    // 初始化一个站点管理员
    const user = await MockContext.createUser('site-admin');
    await user.updatePassword({ password: '123456', confirmPassword: '123456' });
    await SiteAdminSO.addAdmin(user.id);

    // 测试受保护的站点API

    // 错误的凭证
    const errorEncodedCredentials = Buffer.from(`${user.email}:666666`).toString('base64');
    const errorRes = await SiteAdminAPIs.request('/openapi/users/1', {
      method: 'GET',
      headers: {
        Authorization: `Basic ${errorEncodedCredentials}`,
      },
    });
    // 401 Unauthorized
    expect(errorRes.status).toBe(401);

    // 正确的凭证
    const mockUser = await MockContext.createUser('user');
    const encodedCredentials = Buffer.from(`${user.email}:123456`).toString('base64');
    const correctRes = await SiteAdminAPIs.request(`/openapi/users/${mockUser.id}`, {
      method: 'GET',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
      },
    });
    // 200 OK
    expect(correctRes.status).toBe(200);

    // 正确的凭证, 但是参数不正确
    // const validationErrorRes = await SiteAdminAPIs.request(`/openapi/users/`, {
    //   method: 'GET',
    //   headers: {
    //     Authorization: `Basic ${encodedCredentials}`,
    //   },
    // });
    // // 400 Bad Request
    // expect(validationErrorRes.status).toBe(400);

    // 正确的凭证, 但是用户不存在
    const notFoundRes = await SiteAdminAPIs.request('/openapi/users/999999', {
      method: 'GET',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
      },
    });
    // 500 Internal Server Error
    expect(notFoundRes.status).toBe(500);
    const notFoundResponseData = await notFoundRes.json();
    // User not found
    expect(notFoundResponseData.code).toBe(2007);
  });

  test('user crud test', async () => {
    // 初始化一个站点管理员
    const user = await MockContext.createUser('site-admin');
    await user.updatePassword({ password: '123456', confirmPassword: '123456' });
    await SiteAdminSO.addAdmin(user.id);

    const encodedCredentials = Buffer.from(`${user.email}:123456`).toString('base64');
    // const userId = nanoid();
    const userName = `test-user-${random(1000, 9999)}`;
    const userDTO: UserCreateDTO = {
      // id: userId, // 自定义ID
      name: userName,
      email: `${userName}@bika.ai`,
      phone: '11112222',
    };
    const response = await SiteAdminAPIs.request('/openapi/users', {
      method: 'POST',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userDTO),
    });
    console.log('response', response);
    expect(response.status).toBe(200);
    const responseData = await response.json();
    const userVO = responseData.data as UserVO;
    // expect(userVO.id).toBe(userId);
    expect(userVO.name).toBe(userDTO.name);
    expect(userVO.email).toBe(userDTO.email);
    expect(userVO.phone).toBe(userDTO.phone);

    const userId = userVO.id;

    // 创建重复邮箱的用户
    const errResponse = await SiteAdminAPIs.request('/openapi/users', {
      method: 'POST',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userDTO),
    });

    expect(errResponse.status).toBe(500);
    const errResponseData = await errResponse.json();
    // User already exists
    expect(errResponseData.code).toBe(2010);

    // 更新用户信息
    const updateUserDTO: UserUpdateDTO = {
      name: 'tom',
      email: '<EMAIL>',
      phone: '1234567890',
    };

    const updateResponse = await SiteAdminAPIs.request(`/openapi/users/${userId}`, {
      method: 'PUT',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateUserDTO),
    });
    expect(updateResponse.status).toBe(200);
    const updateResponseData = await updateResponse.json();
    const updateUserVO = updateResponseData.data as UserVO;
    expect(updateUserVO.id).toBe(userId);
    expect(updateUserVO.name).toBe(updateUserDTO.name);
    expect(updateUserVO.email).toBe(updateUserDTO.email);
    expect(updateUserVO.phone).toBe(updateUserDTO.phone);

    // 修改一个不存在的用户
    const notFoundResponse = await SiteAdminAPIs.request('/openapi/users/999999', {
      method: 'PUT',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateUserDTO),
    });
    expect(notFoundResponse.status).toBe(500);
    const notFoundResponseData = await notFoundResponse.json();
    // User not found
    expect(notFoundResponseData.code).toBe(2007);

    // 删除不存在的用户
    const deleteNotFoundResponse = await SiteAdminAPIs.request('/openapi/users/999999', {
      method: 'DELETE',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
      },
    });
    expect(deleteNotFoundResponse.status).toBe(500);
    const deleteNotFoundResponseData = await deleteNotFoundResponse.json();
    // User not found
    expect(deleteNotFoundResponseData.code).toBe(2007);

    // 删除存在的用户
    const deleteResponse = await SiteAdminAPIs.request(`/openapi/users/${userId}`, {
      method: 'DELETE',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
      },
    });
    expect(deleteResponse.status).toBe(200);

    // 再次查找已经不存在了
    const findDeletedResponse = await SiteAdminAPIs.request(`/openapi/users/${userId}`, {
      method: 'GET',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
      },
    });
    expect(findDeletedResponse.status).toBe(500);

    // 再次创建相同的邮箱用户
    const reCreateResponse = await SiteAdminAPIs.request('/openapi/users', {
      method: 'POST',
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userDTO),
    });
    expect(reCreateResponse.status).toBe(200);
    const newResponseData = await reCreateResponse.json();
    const newUserVO = newResponseData.data as UserVO;
    expect(newUserVO.id).not.toBe(userId);
  });
});
