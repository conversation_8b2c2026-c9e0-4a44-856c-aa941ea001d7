import { sleep } from 'sharelib/sleep';
import { test, expect, describe } from 'vitest';
// import { Logger } from '@bika/domains/shared/server';
// import { createVika } from './vika-openapi/util';
import { createBika<PERSON><PERSON> } from './bika-openapi/mock-http-client';
import { MockContext } from '../../__tests__/mock';
import { BikaOpenAPIs } from '../apis/bika';

describe('Test Bika Official OpenAPI', async () => {
  test('should success with /v1/system/meta', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const token = await user.developer.createToken();

    const name = 'new openapi database';
    const databaseNodeSO = await rootFolder.createChildSimple(user, {
      name,
      resourceType: 'DATABASE',
    });
    expect(databaseNodeSO).toBeDefined();
    expect(databaseNodeSO.name).toBe(name);

    const bika = createBika<PERSON>pi(token.model.token);
    const meta = await bika.system.meta();
    console.log(meta);
    expect(meta.hostname).toBeDefined();
  });

  test('should success with /openapi.json', async () => {
    const resJSON = await BikaOpenAPIs.request(`/openapi.json`, {
      method: 'GET',
    });
    expect(resJSON.status).toBe(200);
  });

  // Token 有 Bearer 前缀
  test('should success with Bearer prefix', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    const token = await user.developer.createToken();

    const name = 'new openapi database';
    const databaseNodeSO = await rootFolder.createChildSimple(user, {
      name,
      resourceType: 'DATABASE',
    });
    expect(databaseNodeSO).toBeDefined();
    expect(databaseNodeSO.name).toBe(name);

    const resJSON = await BikaOpenAPIs.request(`/v1/spaces/${space.id}/resources/databases/${databaseNodeSO.id}`, {
      method: 'GET',
      headers: { Authorization: `Bearer ${token.model.token}` },
    });
    expect(resJSON.status).toBe(200);
    const resData = await resJSON.json();
    expect(resData.success).toBe(true);
    expect(resData.code).toBe(200);
    // expect(resData.data.total).toBe(0);
    // expect(resData.data.pageNum).toBe(1);
    // expect(resData.data.pageSize).toBe(0);
    // expect(Array.isArray(resData.data.records)).toBeTruthy();
  });

  test('should /spaces list', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const token = await user.developer.createToken();

    const name = 'new openapi database';
    const databaseNodeSO = await rootFolder.createChildSimple(user, {
      name,
      resourceType: 'DATABASE',
    });
    expect(databaseNodeSO).toBeDefined();
    expect(databaseNodeSO.name).toBe(name);
    const bika = createBikaApi(token.model.token);

    const spaces = await bika.space.list();
    expect(spaces.length).toBeGreaterThan(0);
  });

  test('should /spaces get', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    const token = await user.developer.createToken();

    const name = 'new openapi database';
    const databaseNodeSO = await rootFolder.createChildSimple(user, {
      name,
      resourceType: 'DATABASE',
    });
    expect(databaseNodeSO).toBeDefined();
    expect(databaseNodeSO.name).toBe(name);

    const bika = createBikaApi(token.model.token);
    const rSpace = await bika.space.get(space.id);
    expect(rSpace.id).toBe(space.id);
  });

  test('should /nodes list', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    const token = await user.developer.createToken();

    const name = 'new openapi database';
    const databaseNodeSO = await rootFolder.createChildSimple(user, {
      name,
      resourceType: 'DATABASE',
    });
    expect(databaseNodeSO).toBeDefined();
    expect(databaseNodeSO.name).toBe(name);
    const bika = createBikaApi(token.model.token);
    const rSpace = await bika.space.get(space.id);
    expect(rSpace.id).toBe(space.id);
    const nodes = await rSpace.nodes.list();
    expect(nodes.length).toBeGreaterThan(0);
  });

  test('should /nodes get', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    const token = await user.developer.createToken();

    const name = 'new openapi database';
    const databaseNodeSO = await rootFolder.createChildSimple(user, {
      name,
      resourceType: 'DATABASE',
    });
    expect(databaseNodeSO).toBeDefined();
    expect(databaseNodeSO.name).toBe(name);
    const bika = createBikaApi(token.model.token);
    const rSpace = await bika.space.get(space.id);
    expect(rSpace.id).toBe(space.id);
    const node = await rSpace.nodes.get(databaseNodeSO.id);

    expect(node.id).toBe(databaseNodeSO.id);
  });

  test('should /resources/databases get', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    const token = await user.developer.createToken();

    const name = 'new openapi database';
    const databaseNodeSO = await rootFolder.createChildSimple(user, {
      name,
      resourceType: 'DATABASE',
    });
    expect(databaseNodeSO).toBeDefined();
    expect(databaseNodeSO.name).toBe(name);
    const bika = createBikaApi(token.model.token);
    const rSpace = await bika.space.get(space.id);
    expect(rSpace.id).toBe(space.id);
    const node = await rSpace.nodes.get(databaseNodeSO.id);
    const database = await node.asDatabase();

    expect(database.id).toBe(databaseNodeSO.id);
    await sleep(1000);

    // Views
    const views = await database.views.list();
    expect(views.length).toBe(1);

    // Views Records
    const view = views[0];
    expect(view).toBeDefined();
    const viewRecords = await view.records.list();
    expect(viewRecords.total).toBeGreaterThanOrEqual(0);
    await sleep(1000);
    // Database Records
    const dbRecords = await database.records.list();
    expect(dbRecords.total).toBeGreaterThanOrEqual(0);

    // Records Updated, Create, Delete

    // database.records.create({});

    // database.records.update({});

    // database.records.delete({});
  });

  test('CRUD /outgoing-webhooks', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    const token = await user.developer.createToken();

    const name = 'new openapi database';
    const databaseNodeSO = await rootFolder.createChildSimple(user, {
      name,
      resourceType: 'DATABASE',
    });
    expect(databaseNodeSO).toBeDefined();
    expect(databaseNodeSO.name).toBe(name);
    const bika = createBikaApi(token.model.token);
    const rSpace = await bika.space.get(space.id);
    expect(rSpace.id).toBe(space.id);
    const oWebhooks = await rSpace.outgoingWebhooks.list();
    expect(oWebhooks.length).toBe(0);
    await sleep(1000);
    const oWebhook = await rSpace.outgoingWebhooks.register({
      eventType: 'ON_RECORD_CREATED',
      name: 'Test Outgoing Webhook',
      description: 'Test Outgoing Webhook Desc',
      callbackURL: 'https://bika.ai',
    });
    expect(oWebhook.id).toBeDefined();

    // 有了
    const oWebhooks2 = await rSpace.outgoingWebhooks.list();
    expect(oWebhooks2.length).toBe(1);
    expect(oWebhooks2[0].id).toBe(oWebhook.id);

    // 删掉了
    await sleep(1000);
    const delHook = await rSpace.outgoingWebhooks.delete({ id: oWebhook.id });
    expect(delHook).toBe(true);

    const oWebhooks3 = await rSpace.outgoingWebhooks.list();
    expect(oWebhooks3.length).toBe(0);

    // const resJSON = await BikaOpenAPIs.request(`/v1/spaces/${space.id}/outgoing-webhooks`, {
    //   method: 'POST',
    //   headers: { Authorization: `Bearer ${token.model.token}` },
    // });
    // expect(resJSON.status).toBe(200);
    // const data = await resJSON.json();
    // const spaces: SpaceVO[] = data.data;
    // expect(spaces.length).toBeGreaterThan(0);
  });

  test('CRUD /embed-links', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    const token = await user.developer.createToken();

    const name = 'new openapi database';
    const databaseNodeSO = await rootFolder.createChildSimple(user, {
      name,
      resourceType: 'DATABASE',
    });
    expect(databaseNodeSO).toBeDefined();
    expect(databaseNodeSO.name).toBe(name);
    const bika = createBikaApi(token.model.token);
    const rSpace = await bika.space.get(space.id);
    expect(rSpace.id).toBe(space.id);
    const eLinks = await rSpace.embedLinks.list();
    expect(eLinks.length).toBe(0);
    await sleep(1000);
    const eLink = await rSpace.embedLinks.create({
      objectType: 'NODE_RESOURCE',
      objectId: databaseNodeSO.id,
    });
    expect(eLink.id).toBeDefined();

    // 有了
    const eLinks2 = await rSpace.embedLinks.list();
    expect(eLinks2.length).toBe(1);
    expect(eLinks2[0].id).toBe(eLink.id);

    // 删掉了
    await sleep(1000);
    const delHook = await rSpace.embedLinks.delete({ id: eLink.id });
    expect(delHook).toBe(true);

    const eLink3 = await rSpace.embedLinks.list();
    expect(eLink3.length).toBe(0);
  });

  // Token 无 Bearer 前缀
  //   test('should success without Bearer prefix', async () => {
  //     // Token 无 Bearer 前缀
  //     const resJSON = await BikaOpenAPIs.request(`/datasheets/${databaseNodeSO.id}/records`, {
  //       headers: { Authorization: token.model.token },
  //     });
  //     const resData = await resJSON.json();
  //     expect(resData.success).toBe(true);
  //     expect(resData.code).toBe(200);
  //     expect(resData.data.total).toBe(0);
  //     expect(resData.data.pageNum).toBe(1);
  //     expect(resData.data.pageSize).toBe(0);
  //     expect(Array.isArray(resData.data.records)).toBeTruthy();
  //   });

  //   // 错误的 Token
  //   test('should failed with wrong token', async () => {
  //     const resJSON = await BikaOpenAPIs.request(`/datasheets/${databaseNodeSO.id}/records`, {
  //       headers: { Authorization: `Bearer ${token.model.token}x` },
  //     });
  //     const resData = await resJSON.json();
  //     expect(resData.success).toBe(false);
  //     expect(resData.code).toBe(401);
  //     expect(resData.message).toBe('Unauthorized');
  //   });

  //   // 无 Token
  //   test('should failed without token', async () => {
  //     const resJSON = await BikaOpenAPIs.request(`/datasheets/${databaseNodeSO.id}/records`);
  //     const resData = await resJSON.json();
  //     expect(resData.success).toBe(false);
  //     expect(resData.code).toBe(401);
  //     expect(resData.message).toBe('Unauthorized');
  //   });

  //   // 使用 Vika SDK
  //   test('should success with Vika SDK', async () => {
  //     const vika = createVika(token.model.token);

  //     const data = await vika.datasheet(databaseNodeSO.id).records.query();
  //     expect(data.success).toBe(true);
  //     expect(data.code).toBe(200);
  //   });

  //   // 使用 Vika SDK 错误 Token
  //   test('should failed with Vika SDK wrong token', async () => {
  //     const vika = createVika(`${token.model.token}x`);

  //     const data = await vika.datasheet(databaseNodeSO.id).records.query();
  //     expect(data.success).toBe(false);
  //     expect(data.code).toBe(401);
  //     expect(data.message).toBe('Unauthorized');
  //   });
});
