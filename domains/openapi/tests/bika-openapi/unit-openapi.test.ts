import { test, expect, describe } from 'vitest';
import { z } from 'zod';
import { PaginationInfoSchema } from '@bika/types/shared';
import { MemberVOSchema, RoleVOSchema, TeamBaseVOSchema } from '@bika/types/unit/vo';
import { MockContext } from '../../../__tests__/mock/mock.context';
import { BikaOpenAPIs } from '../../apis/bika';

describe('Role OpenAPI test', async () => {
  test('should success with get /v1/spaces/:spaceId/roles', async () => {
    const { user, space } = await MockContext.initUserContext();
    const token = await user.developer.createToken();
    const resJSON = await BikaOpenAPIs.request(`/v1/spaces/${space.id}/roles`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
      },
    });
    expect(resJSON.status).toBe(200);
    const json = await resJSON.json();
    const roles = PaginationInfoSchema.extend({ roles: z.array(RoleVOSchema) }).parse(json.data);
    expect(roles.pageNo).toBe(1);
    expect(roles.pageSize).toBe(27);
    expect(roles.total).toBe(1); // space admin
    expect(roles.roles).toHaveLength(1);
  });

  test('should success with post /v1/spaces/:spaceId/roles', async () => {
    const { user, space } = await MockContext.initUserContext();
    const token = await user.developer.createToken();
    const resJSON = await BikaOpenAPIs.request(`/v1/spaces/${space.id}/roles`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
      },
      body: JSON.stringify({
        name: 'test',
      }),
    });
    expect(resJSON.status).toBe(200);
    const role = RoleVOSchema.parse((await resJSON.json()).data);
    expect(role.name).toBe('test');
    expect(role.id).toBeDefined();
  });

  test('should success with put /v1/spaces/:spaceId/roles/:roleId', async () => {
    const { user, space } = await MockContext.initUserContext();
    const token = await user.developer.createToken();
    const role = await space.createRole(user.id, { name: 'test', manageSpace: false });
    const updateResJSON = await BikaOpenAPIs.request(`/v1/spaces/${space.id}/roles/${role.id}`, {
      method: 'PUT',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
      },
      body: JSON.stringify({
        name: 'test2',
      }),
    });
    expect(updateResJSON.status).toBe(200);
    const updateRole = RoleVOSchema.parse((await updateResJSON.json()).data);
    expect(updateRole.name).toBe('test2');
  });

  test('should success with delete /v1/spaces/:spaceId/roles/:roleId', async () => {
    const { user, space } = await MockContext.initUserContext();
    const token = await user.developer.createToken();
    const role = await space.createRole(user.id, { name: 'test', manageSpace: false });
    const resJSON = await BikaOpenAPIs.request(`/v1/spaces/${space.id}/roles/${role.id}`, {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
      },
    });
    expect(resJSON.status).toBe(200);
    const json = await resJSON.json();
    expect(json.data).toBeDefined();
  });
});

describe('Team OpenAPI test', async () => {
  test('should success with get /v1/spaces/:spaceId/teams/:teamId/members', async () => {
    const { user, space, member } = await MockContext.initUserContext();
    const rootTeam = await space.getRootTeam();
    const token = await user.developer.createToken();
    const team = await rootTeam.createTeam(user.id, 'test');
    await team.addMembers(user, [member.id]);
    const resJSON = await BikaOpenAPIs.request(`/v1/spaces/${space.id}/teams/${team.id}/members`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
      },
    });
    expect(resJSON.status).toBe(200);
    const json = await resJSON.json();
    const members = PaginationInfoSchema.extend({ members: z.array(MemberVOSchema) }).parse(json.data);
    expect(members.pageNo).toBe(1);
    expect(members.pageSize).toBe(27);
    expect(members.total).toBe(1); // space admin
    expect(members.members).toHaveLength(1);
    expect(members.members[0].id).toBe(member.id);
  });

  test('should success with get root sub teams /v1/spaces/:spaceId/teams', async () => {
    const { user, space } = await MockContext.initUserContext();
    const token = await user.developer.createToken();
    const rootTeam = await space.getRootTeam();
    await rootTeam.createTeam(user.id, 'test');
    const resJSON = await BikaOpenAPIs.request(`/v1/spaces/${space.id}/teams`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
      },
    });
    expect(resJSON.status).toBe(200);
    const json = await resJSON.json();
    const teams = PaginationInfoSchema.extend({ teams: z.array(TeamBaseVOSchema) }).parse(json.data);
    expect(teams.pageNo).toBe(1);
    expect(teams.pageSize).toBe(27);
    expect(teams.total).toBe(1);
    expect(teams.teams).toHaveLength(1);
    expect(teams.teams[0].name).toBe('test');
    expect(teams.teams[0].id).toBeDefined();
  });

  test('should success with get /v1/spaces/:spaceId/teams?parentTeamId=${team.id}', async () => {
    const { user, space } = await MockContext.initUserContext();
    const token = await user.developer.createToken();
    const rootTeam = await space.getRootTeam();
    const team = await rootTeam.createTeam(user.id, 'test');
    await team.createTeam(user.id, 'test2');
    const resJSON = await BikaOpenAPIs.request(`/v1/spaces/${space.id}/teams?parentTeamId=${team.id}`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
      },
    });
    expect(resJSON.status).toBe(200);
    const json = await resJSON.json();
    const teams = PaginationInfoSchema.extend({ teams: z.array(TeamBaseVOSchema) }).parse(json.data);
    expect(teams.pageNo).toBe(1);
    expect(teams.pageSize).toBe(27);
    expect(teams.total).toBe(1);
    expect(teams.teams).toHaveLength(1);
    expect(teams.teams[0].name).toBe('test2');
    expect(teams.teams[0].id).toBeDefined();
  });

  // test create team
  test('should success with post create team at root /v1/spaces/:spaceId/teams', async () => {
    const { user, space } = await MockContext.initUserContext();
    const token = await user.developer.createToken();
    const resJSON = await BikaOpenAPIs.request(`/v1/spaces/${space.id}/teams`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
      },
      body: JSON.stringify({
        name: 'test',
        parentTeamId: '',
      }),
    });
    expect(resJSON.status).toBe(200);
    const json = await resJSON.json();
    const team = TeamBaseVOSchema.parse(json.data);
    expect(team.name).toBe('test');
    expect(team.id).toBeDefined();
  });

  // test create team at sub team
  test('should success with post create team at sub team /v1/spaces/:spaceId/teams', async () => {
    const { user, space } = await MockContext.initUserContext();
    const token = await user.developer.createToken();
    const rootTeam = await space.getRootTeam();
    const team = await rootTeam.createTeam(user.id, 'test');
    const resJSON = await BikaOpenAPIs.request(`/v1/spaces/${space.id}/teams`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
      },
      body: JSON.stringify({
        name: 'test2',
        parentTeamId: team.id,
      }),
    });
    expect(resJSON.status).toBe(200);
    const json = await resJSON.json();
    const subTeam = TeamBaseVOSchema.parse(json.data);
    expect(subTeam.name).toBe('test2');
    expect(subTeam.id).toBeDefined();
  });

  // test update team
  test('should success with put /v1/spaces/:spaceId/teams/:teamId', async () => {
    const { user, space } = await MockContext.initUserContext();
    const token = await user.developer.createToken();
    const rootTeam = await space.getRootTeam();
    const team = await rootTeam.createTeam(user.id, 'test');
    const resJSON = await BikaOpenAPIs.request(`/v1/spaces/${space.id}/teams/${team.id}`, {
      method: 'PUT',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
      },
      body: JSON.stringify({
        name: 'test2',
      }),
    });
    expect(resJSON.status).toBe(200);
    const json = await resJSON.json();
    const updateTeam = TeamBaseVOSchema.parse(json.data);
    expect(updateTeam.name).toBe('test2');
  });
  // test delete team
  test('should success with delete /v1/spaces/:spaceId/teams/:teamId', async () => {
    const { user, space } = await MockContext.initUserContext();
    const token = await user.developer.createToken();
    const rootTeam = await space.getRootTeam();
    const team = await rootTeam.createTeam(user.id, 'test');
    const resJSON = await BikaOpenAPIs.request(`/v1/spaces/${space.id}/teams/${team.id}`, {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
      },
    });
    expect(resJSON.status).toBe(200);
  });
});

describe('Member OpenAPI test', async () => {
  test('should success with get /v1/spaces/:spaceId/members/:memberId', async () => {
    const { user, space, member } = await MockContext.initUserContext();
    const token = await user.developer.createToken();
    const resJSON = await BikaOpenAPIs.request(`/v1/spaces/${space.id}/members/${member.id}`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
      },
    });
    expect(resJSON.status).toBe(200);
    const json = await resJSON.json();
    const apiMember = MemberVOSchema.parse(json.data);
    expect(apiMember.id).toBe(member.id);
    expect(apiMember.teams).toBeDefined();
    expect(apiMember.roles).toBeDefined();
  });

  // test update member
  test('should success with put /v1/spaces/:spaceId/members/:memberId', async () => {
    const { user, space, member } = await MockContext.initUserContext();
    const token = await user.developer.createToken();
    const resJSON = await BikaOpenAPIs.request(`/v1/spaces/${space.id}/members/${member.id}`, {
      method: 'PUT',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
      },
      body: JSON.stringify({
        name: 'test2',
      }),
    });
    expect(resJSON.status).toBe(200);
    const json = await resJSON.json();
    const updateMember = MemberVOSchema.parse(json.data);
    expect(updateMember.name).toBe('test2');
  });
  // test delete member
  test('should success with delete /v1/spaces/:spaceId/members/:memberId', async () => {
    const { user, space, member } = await MockContext.initUserContext();
    const token = await user.developer.createToken();
    // 需要先创建一个成员
    const { user: user2 } = await MockContext.createMockUser();
    const rootTeam = await space.getRootTeam();
    const member2 = await space.joinUser(user2.id, rootTeam.id);
    const resJSON = await BikaOpenAPIs.request(`/v1/spaces/${space.id}/members/${member2.id}`, {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
      },
    });
    expect(resJSON.status).toBe(200);
  });
});
