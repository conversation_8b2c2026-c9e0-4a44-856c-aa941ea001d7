import { test, expect, describe, vi } from 'vitest';
import { z } from 'zod';
import { MockContext } from '@bika/domains/__tests__/mock';
import { PaginationInfoSchema } from '@bika/types/shared';
import { RoleVOSchema } from '@bika/types/unit/vo';
import { BikaOpenAPIs } from '../../apis/bika';

describe('rate limit middleware for http test', async () => {
  test('get space roles -- rate limit for free plan -- 1 concurrent', async () => {
    vi.stubEnv('SKIP_RATE_LIMIT', 'false');
    const { user, space } = await MockContext.initUserContext();
    const token = await user.developer.createToken();
    // space is free plan, openapiRate is 2
    // 请求1次
    const resJSON = await BikaOpenAPIs.request(`/v1/spaces/${space.id}/roles`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
      },
    });
    expect(resJSON.status).toBe(200);
    const json = await resJSON.json();
    const roles = PaginationInfoSchema.extend({ roles: z.array(RoleVOSchema) }).parse(json.data);
    expect(roles.pageNo).toBe(1);
    expect(roles.pageSize).toBe(27);
    expect(roles.total).toBe(1); // space admin
    expect(roles.roles).toHaveLength(1);
  });

  test('get space roles -- rate limit for free plan -- 2 concurrent', async () => {
    vi.stubEnv('SKIP_RATE_LIMIT', 'false');
    const { user, space } = await MockContext.initUserContext();
    const token = await user.developer.createToken();
    const promises = [];
    for (let i = 0; i < 2; i++) {
      promises.push(
        BikaOpenAPIs.request(`/v1/spaces/${space.id}/roles`, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token.model.token}`,
          },
        }),
      );
    }
    const resJSONs = await Promise.all(promises);
    for (const resJSON of resJSONs) {
      expect(resJSON.status).toBe(200);
    }
  });

  test('get space roles -- rate limit for free plan -- 3 concurrent', async () => {
    vi.stubEnv('SKIP_RATE_LIMIT', 'false');
    const { user, space } = await MockContext.initUserContext();
    const token = await user.developer.createToken();
    const promises = [];
    for (let i = 0; i < 3; i++) {
      promises.push(
        BikaOpenAPIs.request(`/v1/spaces/${space.id}/roles`, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token.model.token}`,
          },
        }),
      );
    }
    const resJSONs = await Promise.all(promises);
    const limitedCall = resJSONs.find((resJSON) => resJSON.status === 429);
    const json = await limitedCall!.json();
    expect(json.data.remainingPoints).toBe(0);
    expect(json.data.resetTime).toBeDefined();
    expect(json.data.limit).toBe(2);
  });
});
