import { describe, test, expect, vi } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { DefaultNumberFieldProperty } from '@bika/types/database/bo';
import { RecordRenderVO } from '@bika/types/database/vo';
import { ApiCreateRecordsReq, ApiUpdateRecordsReq, RecordSorts } from '@bika/types/openapi/dto';
import { GetRecordsResVO, ResponseVO } from '@bika/types/openapi/vo';
import { DatabaseSO } from '../../../database/server/database-so';
import { BikaOpenAPIs } from '../../apis/bika';

describe('Bika OpenAPI - Database Record API Tests', async () => {
  const { space, user, rootFolder } = await MockContext.initUserContext();
  const token = await user.developer.createToken();

  const getRecordsRequest = async (
    databaseId: string,
    options?: { filter?: string; sorts?: RecordSorts },
  ): Promise<GetRecordsResVO> => {
    const { filter, sorts } = options || {};
    const query = new URLSearchParams();
    if (filter) {
      console.log(`filter: `, filter);
      query.append('filter', filter);
    }

    if (sorts) {
      sorts.forEach(({ field, order }, index) => {
        query.append(`sort[${index}][field]`, field);
        query.append(`sort[${index}][order]`, order);
      });
    }

    // 构建基础URL
    const baseUrl = `/v1/spaces/${space.id}/resources/databases/${databaseId}/records`;

    // 组合完整URL（包含查询参数）
    const urlWithParams = `${baseUrl}${query.toString() ? `?${query.toString()}` : ''}`;

    console.log(`请求URL: `, urlWithParams);

    const response = await BikaOpenAPIs.request(urlWithParams, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
        'Content-Type': 'application/json',
      },
    });

    expect(response.status).toBe(200);
    const responseData = await response.json();
    const responseVO = responseData as ResponseVO<GetRecordsResVO>;
    // console.log('Response VO:', JSON.stringify(responseVO, null, 2));
    expect(responseVO.success).toBe(true);
    return responseVO.data;
  };

  const createRecordsRequest = async (databaseId: string, body: ApiCreateRecordsReq): Promise<RecordRenderVO[]> => {
    const response = await BikaOpenAPIs.request(
      `/v1/spaces/${space.id}/resources/databases/${databaseId}/records/batch`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token.model.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      },
    );

    expect(response.status).toBe(200);
    const responseData = await response.json();
    return responseData.data as RecordRenderVO[];
  };

  const updateRecordsRequest = async (databaseId: string, body: ApiUpdateRecordsReq): Promise<RecordRenderVO[]> => {
    const response = await BikaOpenAPIs.request(
      `/v1/spaces/${space.id}/resources/databases/${databaseId}/records/batch`,
      {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${token.model.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      },
    );

    expect(response.status).toBe(200);
    const responseData = await response.json();
    return responseData.data as RecordRenderVO[];
  };

  const deleteRecordsRequest = async (databaseId: string, recordIds: string[]): Promise<void> => {
    const query = new URLSearchParams();
    recordIds.forEach((id) => query.append('records', id));
    const response = await BikaOpenAPIs.request(
      `/v1/spaces/${space.id}/resources/databases/${databaseId}/records?${query.toString()}`,
      {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token.model.token}`,
          'Content-Type': 'application/json',
        },
      },
    );
    expect(response.status).toBe(200);
    const responseData = await response.json();
    expect(responseData.success).toBe(true);
  };

  test('test records crud api', async () => {
    vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing
    // 创建表
    const databaseNode = await rootFolder.createChildSimple(user, {
      name: 'database',
      resourceType: 'DATABASE',
      fields: [
        {
          name: 'single_text',
          type: 'SINGLE_TEXT',
        },
        {
          name: 'number',
          type: 'NUMBER',
          property: DefaultNumberFieldProperty,
        },
      ],
    });
    const database = await databaseNode.toResourceSO<DatabaseSO>();
    const singleTextField = database.getFieldByFieldKey('single_text');
    const numberField = database.getFieldByFieldKey('number');

    // 使用OpenAPI批量创建记录
    const records: ApiCreateRecordsReq = {
      cells: [
        {
          single_text: 'text1',
          number: 1,
        },
        {
          single_text: 'text2',
          number: 2,
        },
      ],
    };
    const createdRecords = await createRecordsRequest(database.id, records);
    expect(createdRecords.length).toBe(2);
    expect(createdRecords[0].cells[singleTextField.id]?.data).toEqual('text1');
    expect(createdRecords[0].cells[singleTextField.id]?.value).toEqual('text1');
    expect(createdRecords[0].cells[numberField.id]?.data).toEqual(1);
    expect(createdRecords[0].cells[numberField.id]?.value).toEqual('1');
    expect(createdRecords[1].cells[singleTextField.id].data).toEqual('text2');
    expect(createdRecords[1].cells[singleTextField.id].value).toEqual('text2');
    expect(createdRecords[1].cells[numberField.id]?.data).toEqual(2);
    expect(createdRecords[1].cells[numberField.id]?.value).toEqual('2');

    // 批量修改记录
    const updateRecordsDTO: ApiUpdateRecordsReq = {
      records: [
        {
          recordId: createdRecords[0].id,
          cells: {
            single_text: 'updated_text1',
            number: 10,
          },
        },
        {
          recordId: createdRecords[1].id,
          cells: {
            single_text: 'updated_text2',
            number: 20,
          },
        },
      ],
    };
    const updatedRecords = await updateRecordsRequest(database.id, updateRecordsDTO);
    expect(updatedRecords.length).toBe(2);
    expect(updatedRecords[0].cells[singleTextField.id]?.data).toEqual('updated_text1');
    expect(updatedRecords[0].cells[singleTextField.id]?.value).toEqual('updated_text1');
    expect(updatedRecords[0].cells[numberField.id]?.data).toEqual(10);
    expect(updatedRecords[0].cells[numberField.id]?.value).toEqual('10');
    expect(updatedRecords[1].cells[singleTextField.id]?.data).toEqual('updated_text2');
    expect(updatedRecords[1].cells[singleTextField.id]?.value).toEqual('updated_text2');
    expect(updatedRecords[1].cells[numberField.id]?.data).toEqual(20);
    expect(updatedRecords[1].cells[numberField.id]?.value).toEqual('20');

    const recordIds = createdRecords.map((record) => record.id);
    // 批量删除记录
    await deleteRecordsRequest(database.id, recordIds);

    // 验证记录是否被删除
    const recordsAfterDelete = await getRecordsRequest(database.id);
    expect(recordsAfterDelete.total).toBe(0);
  });

  /**
   * 获取记录API使用排序测试
   */
  test('Get Records Test - sort', async () => {
    vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing
    // 创建表
    const databaseNode = await rootFolder.createChildSimple(user, {
      name: 'database',
      resourceType: 'DATABASE',
      fields: [
        {
          name: 'single_text',
          type: 'SINGLE_TEXT',
        },
        {
          name: 'number',
          type: 'NUMBER',
          property: DefaultNumberFieldProperty,
        },
        {
          name: 'created_at',
          type: 'CREATED_TIME',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
        },
      ],
    });
    const database = await databaseNode.toResourceSO<DatabaseSO>();
    const singleTextField = database.getFieldByFieldKey('single_text');
    const numberField = database.getFieldByFieldKey('number');

    // 使用OpenAPI批量创建几行记录
    const recordsToCreate: ApiCreateRecordsReq = {
      cells: [
        {
          [singleTextField.id]: 'text1',
          [numberField.id]: 1,
        },
        {
          [singleTextField.id]: 'text2',
          [numberField.id]: 2,
        },
        {
          [singleTextField.id]: 'text3',
          [numberField.id]: 3,
        },
        // 空行
        {},
      ],
    };
    const createdRecords = await createRecordsRequest(database.id, recordsToCreate);
    expect(createdRecords.length).toBe(4);

    // 测试: 按照单行文本字段升序, 数字字段降序
    const sorts: RecordSorts = [{ field: numberField.id, order: 'asc' }];
    const recordPagination = await getRecordsRequest(database.id, { sorts });
    expect(recordPagination.total).toBe(4);
    console.log(recordPagination.records);
  });

  /**
   * 获取记录API使用过滤测试 - 文本字段过滤
   */
  test('Get Records Test - text field filter', async () => {
    vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing
    // 创建表
    const databaseNode = await rootFolder.createChildSimple(user, {
      name: 'database',
      resourceType: 'DATABASE',
      fields: [
        {
          name: 'single_text',
          type: 'SINGLE_TEXT',
        },
        {
          name: 'long_text',
          type: 'LONG_TEXT',
        },
        {
          name: 'url',
          type: 'URL',
        },
        {
          name: 'email',
          type: 'EMAIL',
        },
        {
          name: 'phone',
          type: 'PHONE',
        },
        {
          name: 'created_at',
          type: 'CREATED_TIME',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
        },
      ],
    });
    const database = await databaseNode.toResourceSO<DatabaseSO>();
    const singleTextField = database.getFieldByFieldKey('single_text');
    const longTextField = database.getFieldByFieldKey('long_text');
    const urlField = database.getFieldByFieldKey('url');
    const emailField = database.getFieldByFieldKey('email');
    const phoneField = database.getFieldByFieldKey('phone');

    // 使用OpenAPI批量创建几行记录
    const recordsToCreate: ApiCreateRecordsReq = {
      cells: [
        {
          [singleTextField.id]: 'text1',
          [longTextField.id]: 'long text 1',
          [urlField.id]: 'https://example.com/1',
          [emailField.id]: '<EMAIL>',
          [phoneField.id]: '1234567890',
        },
        {
          [singleTextField.id]: 'text2',
          [longTextField.id]: 'long text 2',
          [urlField.id]: 'https://example.com/2',
          [emailField.id]: '<EMAIL>',
          [phoneField.id]: '0987654321',
        },
        // 空行
        {},
      ],
    };
    const createdRecords = await createRecordsRequest(database.id, recordsToCreate);
    expect(createdRecords.length).toBe(3);

    // 测试: 为空 -> `==null`或`==NULL`
    const isEmptyTest = async () => {
      const filter = `single_text==null`;
      const recordPagination = await getRecordsRequest(database.id, { filter });
      expect(recordPagination.total).toBe(3);
      expect(recordPagination.records.length).toBe(1);
      expect(recordPagination.records[0].fields.single_text).toEqual(null);
      console.log('created_at', recordPagination.records[0].fields.created_at_1);
      expect(recordPagination.records[0].fields.created_at).not.toBeNull();
      expect(recordPagination.records[0].fields.created_at).toBeDefined();

      const filter2 = `single_text==NULL`;
      const recordPagination2 = await getRecordsRequest(database.id, { filter: filter2 });
      expect(recordPagination2.total).toBe(3);
      expect(recordPagination2.records.length).toBe(1);
      expect(recordPagination2.records[0].fields.single_text).toEqual(null);
      expect(recordPagination2.records[0].fields.created_at).not.toBeNull();
      expect(recordPagination2.records[0].fields.created_at).toBeDefined();
    };

    // 测试: 不为空 -> `!=null`或`!=NULL`
    const isNotEmptyTest = async () => {
      const filter = `single_text!=null`;
      const recordPagination = await getRecordsRequest(database.id, { filter });
      expect(recordPagination.total).toBe(3);
      expect(recordPagination.records.length).toBe(2);
      expect(recordPagination.records[0].fields.single_text).toEqual('text2');
      expect(recordPagination.records[0].fields.created_at).not.toBeNull();
      expect(recordPagination.records[0].fields.created_at).toBeDefined();
      expect(recordPagination.records[1].fields.single_text).toEqual('text1');
      expect(recordPagination.records[1].fields.created_at).not.toBeNull();
      expect(recordPagination.records[1].fields.created_at).toBeDefined();

      const filter2 = `single_text!=NULL`;
      const recordPagination2 = await getRecordsRequest(database.id, { filter: filter2 });
      expect(recordPagination2.total).toBe(3);
      expect(recordPagination2.records.length).toBe(2);
      expect(recordPagination2.records[0].fields.single_text).toEqual('text2');
      expect(recordPagination2.records[0].fields.created_at).not.toBeNull();
      expect(recordPagination2.records[0].fields.created_at).toBeDefined();
      expect(recordPagination2.records[1].fields.single_text).toEqual('text1');
      expect(recordPagination2.records[1].fields.created_at).not.toBeNull();
      expect(recordPagination2.records[1].fields.created_at).toBeDefined();
    };

    // 测试: 等于 -> `==`
    const isTest = async () => {
      const filter = `single_text==text1,single_text==text2`;
      const recordPagination = await getRecordsRequest(database.id, { filter });
      expect(recordPagination.total).toBe(3);
      expect(recordPagination.records.length).toBe(2);
      expect(recordPagination.records[0].fields.single_text).toEqual('text2');
      expect(recordPagination.records[0].fields.created_at).not.toBeNull();
      expect(recordPagination.records[0].fields.created_at).toBeDefined();
      expect(recordPagination.records[1].fields.single_text).toEqual('text1');
      expect(recordPagination.records[1].fields.created_at).not.toBeNull();
      expect(recordPagination.records[1].fields.created_at).toBeDefined();
    };

    // 测试: 不等于 -> `!=`
    const isNotTest = async () => {
      const filter = `single_text!=text1;single_text!=text2`;
      const recordPagination = await getRecordsRequest(database.id, { filter });
      expect(recordPagination.total).toBe(3);
      expect(recordPagination.records.length).toBe(1);
      expect(recordPagination.records[0].fields.single_text).toEqual(null);
      expect(recordPagination.records[0].fields.created_at).not.toBeNull();
      expect(recordPagination.records[0].fields.created_at).toBeDefined();
    };

    // 测试: 包含 -> `=c=`
    const containsTest = async () => {
      const filter = `single_text=c=text1`;
      const recordPagination = await getRecordsRequest(database.id, { filter });
      expect(recordPagination.total).toBe(3);
      expect(recordPagination.records.length).toBe(1);
      expect(recordPagination.records[0].fields.single_text).toEqual('text1');
      expect(recordPagination.records[0].fields.created_at).not.toBeNull();
      expect(recordPagination.records[0].fields.created_at).toBeDefined();
    };

    // 测试: 不包含 -> `=nc=`
    const doesNotContainTest = async () => {
      const filter = `single_text=nc=text1`;
      const recordPagination = await getRecordsRequest(database.id, { filter });
      expect(recordPagination.total).toBe(3);
      expect(recordPagination.records.length).toBe(2);
      expect(recordPagination.records[0].fields.single_text).toEqual(null);
      expect(recordPagination.records[0].fields.created_at).not.toBeNull();
      expect(recordPagination.records[0].fields.created_at).toBeDefined();
      expect(recordPagination.records[1].fields.single_text).toEqual('text2');
      expect(recordPagination.records[1].fields.created_at).not.toBeNull();
      expect(recordPagination.records[1].fields.created_at).toBeDefined();
    };

    await isEmptyTest();
    await isNotEmptyTest();
    await isTest();
    await isNotTest();
    await containsTest();
    await doesNotContainTest();
  });

  /**
   * 获取记录API使用过滤测试 - 数字字段过滤
   */
  test('Get Records Test - number field filter', async () => {
    vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing
    // 创建表
    const databaseNode = await rootFolder.createChildSimple(user, {
      name: 'database',
      resourceType: 'DATABASE',
      fields: [
        {
          name: 'single_text',
          type: 'SINGLE_TEXT',
        },
        {
          name: 'number',
          type: 'NUMBER',
          property: DefaultNumberFieldProperty,
        },
        {
          name: 'created_at',
          type: 'CREATED_TIME',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
        },
      ],
    });
    const database = await databaseNode.toResourceSO<DatabaseSO>();
    const singleTextField = database.getFieldByFieldKey('single_text');
    const numberField = database.getFieldByFieldKey('number');

    // 使用OpenAPI批量创建几行记录
    const recordsToCreate: ApiCreateRecordsReq = {
      cells: [
        {
          [singleTextField.id]: 'text1',
          [numberField.id]: 1,
        },
        {
          [singleTextField.id]: 'text2',
          [numberField.id]: 2,
        },
        {
          [singleTextField.id]: 'text3',
          [numberField.id]: 3,
        },
        // 空行
        {},
      ],
    };
    const createdRecords = await createRecordsRequest(database.id, recordsToCreate);
    expect(createdRecords.length).toBe(4);

    // 测试: 为空 -> `==null`或`==NULL`
    const isEmptyTest = async () => {
      const filter = `number==null`;
      const recordPagination = await getRecordsRequest(database.id, { filter });
      expect(recordPagination.total).toBe(4);
      expect(recordPagination.records.length).toBe(1);
      expect(recordPagination.records[0].fields.number).toEqual(null);
      expect(recordPagination.records[0].fields.created_at).not.toBeNull();
      expect(recordPagination.records[0].fields.created_at).toBeDefined();

      const filter2 = `number==NULL`;
      const recordPagination2 = await getRecordsRequest(database.id, { filter: filter2 });
      expect(recordPagination2.total).toBe(4);
      expect(recordPagination2.records.length).toBe(1);
      expect(recordPagination2.records[0].fields.number).toEqual(null);
      expect(recordPagination2.records[0].fields.created_at).not.toBeNull();
      expect(recordPagination2.records[0].fields.created_at).toBeDefined();
    };

    // 测试: 不为空 -> `!=null`或`!=NULL`
    const isNotEmptyTest = async () => {
      const filter = `number!=null`;
      const recordPagination = await getRecordsRequest(database.id, { filter });
      expect(recordPagination.total).toBe(4);
      expect(recordPagination.records.length).toBe(3);
      expect(recordPagination.records[0].fields.number).toEqual('3');
      expect(recordPagination.records[0].fields.created_at).not.toBeNull();
      expect(recordPagination.records[0].fields.created_at).toBeDefined();
      expect(recordPagination.records[1].fields.number).toEqual('2');
      expect(recordPagination.records[1].fields.created_at).not.toBeNull();
      expect(recordPagination.records[1].fields.created_at).toBeDefined();
      expect(recordPagination.records[2].fields.number).toEqual('1');
      expect(recordPagination.records[2].fields.created_at).not.toBeNull();
      expect(recordPagination.records[2].fields.created_at).toBeDefined();

      const filter2 = `number!=NULL`;
      const recordPagination2 = await getRecordsRequest(database.id, { filter: filter2 });
      expect(recordPagination2.total).toBe(4);
      expect(recordPagination2.records.length).toBe(3);
      expect(recordPagination2.records[0].fields.number).toEqual('3');
      expect(recordPagination2.records[0].fields.created_at).not.toBeNull();
      expect(recordPagination2.records[0].fields.created_at).toBeDefined();
      expect(recordPagination2.records[1].fields.number).toEqual('2');
      expect(recordPagination2.records[1].fields.created_at).not.toBeNull();
      expect(recordPagination2.records[1].fields.created_at).toBeDefined();
      expect(recordPagination2.records[2].fields.number).toEqual('1');
      expect(recordPagination2.records[2].fields.created_at).not.toBeNull();
      expect(recordPagination2.records[2].fields.created_at).toBeDefined();
    };

    // 测试: 等于 -> `==`
    const isTest = async () => {
      const filter = `number==1,number==2`;
      const recordPagination = await getRecordsRequest(database.id, { filter });
      expect(recordPagination.total).toBe(4);
      expect(recordPagination.records.length).toBe(2);
      expect(recordPagination.records[0].fields.number).toEqual('2');
      expect(recordPagination.records[0].fields.created_at).not.toBeNull();
      expect(recordPagination.records[0].fields.created_at).toBeDefined();
      expect(recordPagination.records[1].fields.number).toEqual('1');
      expect(recordPagination.records[1].fields.created_at).not.toBeNull();
      expect(recordPagination.records[1].fields.created_at).toBeDefined();
    };

    // 测试: 不等于 -> `!=`
    const isNotTest = async () => {
      const filter = `number!=1;number!=2`;
      const recordPagination = await getRecordsRequest(database.id, { filter });
      expect(recordPagination.total).toBe(4);
      expect(recordPagination.records.length).toBe(2);
      expect(recordPagination.records[0].fields.number).toBeNull();
      expect(recordPagination.records[0].fields.created_at).not.toBeNull();
      expect(recordPagination.records[0].fields.created_at).toBeDefined();
      expect(recordPagination.records[1].fields.number).toEqual('3');
      expect(recordPagination.records[1].fields.created_at).not.toBeNull();
      expect(recordPagination.records[1].fields.created_at).toBeDefined();
    };

    // 测试: 大于 -> `>`
    const greaterThanTest = async () => {
      const filter = `number>1`;
      const recordPagination = await getRecordsRequest(database.id, { filter });
      expect(recordPagination.total).toBe(4);
      expect(recordPagination.records.length).toBe(2);
      expect(recordPagination.records[0].fields.number).toEqual('3');
      expect(recordPagination.records[0].fields.created_at).not.toBeNull();
      expect(recordPagination.records[0].fields.created_at).toBeDefined();
      expect(recordPagination.records[1].fields.number).toEqual('2');
      expect(recordPagination.records[1].fields.created_at).not.toBeNull();
      expect(recordPagination.records[1].fields.created_at).toBeDefined();
    };

    // 测试: 小于 -> `<`
    const lessThanTest = async () => {
      const filter = `number<3`;
      const recordPagination = await getRecordsRequest(database.id, { filter });
      expect(recordPagination.total).toBe(4);
      expect(recordPagination.records.length).toBe(2);
      expect(recordPagination.records[0].fields.number).toEqual('2');
      expect(recordPagination.records[0].fields.created_at).not.toBeNull();
      expect(recordPagination.records[0].fields.created_at).toBeDefined();
      expect(recordPagination.records[1].fields.number).toEqual('1');
      expect(recordPagination.records[1].fields.created_at).not.toBeNull();
      expect(recordPagination.records[1].fields.created_at).toBeDefined();
    };

    // 测试: 大于等于 -> `>=`
    const greaterThanOrEqualTest = async () => {
      const filter = `number>=2`;
      const recordPagination = await getRecordsRequest(database.id, { filter });
      expect(recordPagination.total).toBe(4);
      expect(recordPagination.records.length).toBe(2);
      expect(recordPagination.records[0].fields.number).toEqual('3');
      expect(recordPagination.records[0].fields.created_at).not.toBeNull();
      expect(recordPagination.records[0].fields.created_at).toBeDefined();
      expect(recordPagination.records[1].fields.number).toEqual('2');
      expect(recordPagination.records[1].fields.created_at).not.toBeNull();
      expect(recordPagination.records[1].fields.created_at).toBeDefined();
    };

    // 测试: 小于等于 -> `<=`
    const lessThanOrEqualTest = async () => {
      const filter = `number<=2`;
      const recordPagination = await getRecordsRequest(database.id, { filter });
      expect(recordPagination.total).toBe(4);
      expect(recordPagination.records.length).toBe(2);
      expect(recordPagination.records[0].fields.number).toEqual('2');
      expect(recordPagination.records[0].fields.created_at).not.toBeNull();
      expect(recordPagination.records[0].fields.created_at).toBeDefined();
      expect(recordPagination.records[1].fields.number).toEqual('1');
      expect(recordPagination.records[1].fields.created_at).not.toBeNull();
      expect(recordPagination.records[1].fields.created_at).toBeDefined();
    };

    await isEmptyTest();
    await isNotEmptyTest();
    await isTest();
    await isNotTest();
    await greaterThanTest();
    await lessThanTest();
    await greaterThanOrEqualTest();
    await lessThanOrEqualTest();
  });

  /**
   * 复选框字段过滤测试
   */
  test('Get Records Test - checkbox field filter', async () => {
    vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing
    // 创建表
    const databaseNode = await rootFolder.createChildSimple(user, {
      name: 'database',
      resourceType: 'DATABASE',
      fields: [
        {
          name: 'single_text',
          type: 'SINGLE_TEXT',
        },
        {
          name: 'checkbox',
          type: 'CHECKBOX',
        },
        {
          name: 'created_at',
          type: 'CREATED_TIME',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
        },
      ],
    });
    const database = await databaseNode.toResourceSO<DatabaseSO>();
    const checkboxField = database.getFieldByFieldKey('checkbox');

    // 使用OpenAPI批量创建几行记录
    const recordsToCreate: ApiCreateRecordsReq = {
      cells: [
        {
          [checkboxField.id]: true,
        },
        {
          [checkboxField.id]: false,
        },
        {
          [checkboxField.id]: null,
        },
        // 空行
        {},
      ],
    };
    const createdRecords = await createRecordsRequest(database.id, recordsToCreate);
    expect(createdRecords.length).toBe(4);

    const isTrueTest = async () => {
      const filter = `checkbox==true,checkbox==1`;
      const recordPagination = await getRecordsRequest(database.id, { filter });
      expect(recordPagination.total).toBe(4);
      expect(recordPagination.records.length).toBe(1);
      expect(recordPagination.records[0].fields.checkbox).toEqual('1');
      expect(recordPagination.records[0].fields.created_at).not.toBeNull();
      expect(recordPagination.records[0].fields.created_at).toBeDefined();
    };

    const isFalseTest = async () => {};

    const isNullTest = async () => {};

    await isTrueTest();
    await isFalseTest();
    await isNullTest();
  });
});
