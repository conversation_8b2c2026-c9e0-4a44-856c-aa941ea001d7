import { describe, expect, test, vi } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import {
  DefaultAutoNumberFieldProperty,
  DefaultCurrencyFieldProperty,
  DefaultDateTimeFieldProperty,
  DefaultNumberFieldProperty,
  DefaultPercentFieldProperty,
  DefaultRatingFieldProperty,
  DefaultDateRangeFieldProperty,
} from '@bika/types/database/bo';
import {
  ApiCreateRecordsReqV2,
  ApiGetRecordsReqV2,
  ApiGetRecordsReqV2Schema,
  ApiUpdateRecordReqV2,
  ApiUpdateRecordsReqV2,
  RecordField,
} from '@bika/types/openapi/dto';
import {
  ApiCreateRecordsRes,
  ApiDeleteRecordRes,
  ApiGetRecordsRes,
  ApiRecordVO,
  ApiUpdateRecordsRes,
  OpenAPIAttachmentCellValue,
  OpenAPIMemberCellValue,
  ResponseVO,
} from '@bika/types/openapi/vo';
import { BikaOpenAPIs } from '../../apis/bika';

describe('Bika OpenAPI Database V2 Tests', async () => {
  const { space, user, member, rootFolder } = await MockContext.initUserContext();
  const token = await user.developer.createToken();

  const rootTeam = await space.getRootTeam();
  // 初始化一个部门和成员kelvin
  const devTeam = await rootTeam.createTeam(user.id, 'dev team');
  const kelvinUser = await MockContext.createUser('kelvin');
  const kelvin = await space.joinUser(kelvinUser.id, devTeam.id);

  // 初始化一个角色和成员jane
  const productRole = await space.createRole(user.id, { name: 'product' });
  const janeUser = await MockContext.createUser('jane');
  const jane = await space.joinUser(janeUser.id, rootTeam.id);
  await productRole.addUnit(user.id, jane.id);

  // 获取记录
  const listRecordsRequest = async (databaseId: string, request?: ApiGetRecordsReqV2): Promise<ApiGetRecordsRes> => {
    const { timeZone, userLocale, offset, pageSize, maxRecords, filter, sort, fields, fieldKey, cellFormat } =
      request || ApiGetRecordsReqV2Schema.parse({});
    const query = new URLSearchParams();

    if (timeZone) {
      query.append('timeZone', timeZone);
    }
    if (userLocale) {
      query.append('userLocale', userLocale);
    }

    if (offset) {
      query.append('offset', offset);
    }

    if (pageSize !== undefined) {
      query.append('pageSize', pageSize.toString());
    }

    if (maxRecords !== undefined) {
      query.append('maxRecords', maxRecords.toString());
    }

    if (fields) {
      fields.forEach((field) => {
        query.append('fields', field);
      });
    }

    if (filter) {
      console.log(`filter: `, filter);
      query.append('filter', filter);
    }

    if (sort) {
      sort.forEach(({ field, order }, index) => {
        query.append(`sort[${index}][field]`, field);
        query.append(`sort[${index}][order]`, order);
      });
    }

    if (fieldKey) {
      query.append('fieldKey', fieldKey);
    }

    if (cellFormat) {
      query.append('cellFormat', cellFormat);
    }

    // 构建基础URL
    const baseUrl = `/v2/spaces/${space.id}/resources/databases/${databaseId}/records`;

    // 组合完整URL（包含查询参数）
    const urlWithParams = `${baseUrl}${query.toString() ? `?${query.toString()}` : ''}`;

    console.log(`请求URL: `, urlWithParams);

    const response = await BikaOpenAPIs.request(urlWithParams, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
        'Content-Type': 'application/json',
      },
    });

    const responseData = await response.json();
    const responseVO = responseData as ResponseVO<ApiGetRecordsRes>;
    if (response.status !== 200) {
      console.error('Error Response:', responseData);
    }
    // console.log('Response VO:', JSON.stringify(responseVO, null, 2));
    expect(response.status).toBe(200);
    expect(responseVO.success).toBe(true);
    return responseVO.data;
  };

  // 创建记录
  const createRecordsRequest = async (
    databaseId: string,
    body: ApiCreateRecordsReqV2,
  ): Promise<ApiCreateRecordsRes> => {
    const response = await BikaOpenAPIs.request(`/v2/spaces/${space.id}/resources/databases/${databaseId}/records`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });
    expect(response.status).toBe(200);
    const responseData = await response.json();
    // console.log('Create Records Response:', responseData);
    return responseData.data as ApiCreateRecordsRes;
  };

  // 更新单个记录
  const updateRecordRequest = async (
    databaseId: string,
    recordId: string,
    body: ApiUpdateRecordReqV2,
  ): Promise<ApiRecordVO> => {
    const response = await BikaOpenAPIs.request(
      `/v2/spaces/${space.id}/resources/databases/${databaseId}/records/${recordId}`,
      {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${token.model.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      },
    );

    expect(response.status).toBe(200);
    const responseData = await response.json();
    return responseData.data as ApiRecordVO;
  };

  // 批量更新记录
  const updateRecordsRequest = async (
    databaseId: string,
    body: ApiUpdateRecordsReqV2,
  ): Promise<ApiUpdateRecordsRes> => {
    const response = await BikaOpenAPIs.request(`/v2/spaces/${space.id}/resources/databases/${databaseId}/records`, {
      method: 'PUT',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    expect(response.status).toBe(200);
    const responseData = await response.json();
    return responseData.data as ApiUpdateRecordsRes;
  };

  // 删除单条记录
  const deleteRecordRequest = async (databaseId: string, recordId: string): Promise<ApiDeleteRecordRes> => {
    const response = await BikaOpenAPIs.request(
      `/v2/spaces/${space.id}/resources/databases/${databaseId}/records/${recordId}`,
      {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token.model.token}`,
          'Content-Type': 'application/json',
        },
      },
    );
    expect(response.status).toBe(200);
    const responseData = await response.json();
    expect(responseData.success).toBe(true);
    return responseData.data as ApiDeleteRecordRes;
  };

  // 批量删除记录
  const deleteRecordsRequest = async (databaseId: string, recordIds: string[]): Promise<ApiDeleteRecordRes[]> => {
    const query = new URLSearchParams();
    recordIds.forEach((id) => query.append('records', id));
    // 组合完整URL（包含查询参数）
    const baseUrl = `/v2/spaces/${space.id}/resources/databases/${databaseId}/records`;
    const urlWithParams = `${baseUrl}${query.toString() ? `?${query.toString()}` : ''}`;
    console.log(`请求URL: `, urlWithParams);

    const response = await BikaOpenAPIs.request(urlWithParams, {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
        'Content-Type': 'application/json',
      },
    });
    expect(response.status).toBe(200);
    const responseData = await response.json();
    expect(responseData.success).toBe(true);
    return responseData.data as ApiDeleteRecordRes[];
  };

  // 测试获取记录的查询参数
  describe('List Records - query parameters test collection', async () => {
    // 创建表(全字段)
    const databaseNode = await rootFolder.createChildSimple(user, {
      name: 'database',
      resourceType: 'DATABASE',
      fields: [
        {
          name: 'single_text',
          type: 'SINGLE_TEXT',
        },
        {
          name: 'long_text',
          type: 'LONG_TEXT',
        },
        {
          name: 'url',
          type: 'URL',
        },
        {
          name: 'phone',
          type: 'PHONE',
        },
        {
          name: 'email',
          type: 'EMAIL',
        },
        {
          name: 'number',
          type: 'NUMBER',
          property: DefaultNumberFieldProperty,
        },
        {
          name: 'currency',
          type: 'CURRENCY',
          property: DefaultCurrencyFieldProperty,
        },
        {
          name: 'percent',
          type: 'PERCENT',
          property: DefaultPercentFieldProperty,
        },
        {
          name: 'rating',
          type: 'RATING',
          property: DefaultRatingFieldProperty,
        },

        {
          name: 'auto_number',
          type: 'AUTO_NUMBER',
          property: DefaultAutoNumberFieldProperty,
        },
        {
          name: 'checkbox',
          type: 'CHECKBOX',
        },
        {
          name: 'single_select',
          type: 'SINGLE_SELECT',
          property: {
            options: [],
          },
        },
        {
          name: 'multi_select',
          type: 'MULTI_SELECT',
          property: {
            options: [],
          },
        },
        {
          name: 'member',
          type: 'MEMBER',
          property: { many: true },
        },
        {
          name: 'created_by',
          type: 'CREATED_BY',
        },
        {
          name: 'modified_by',
          type: 'MODIFIED_BY',
        },
        {
          name: 'date',
          type: 'DATETIME',
          property: DefaultDateTimeFieldProperty,
        },
        {
          name: 'date_range',
          type: 'DATERANGE',
          property: DefaultDateRangeFieldProperty,
        },
        {
          name: 'created_at',
          type: 'CREATED_TIME',
          property: DefaultDateTimeFieldProperty,
        },
        {
          name: 'modified_at',
          type: 'MODIFIED_TIME',
          property: DefaultDateTimeFieldProperty,
        },
        {
          name: 'attachment',
          type: 'ATTACHMENT',
        },
      ],
    });
    const database = await databaseNode.toResourceSO<DatabaseSO>();

    const attachmentSO = await MockContext.createMockAttachment(user);
    const attachmentVO = attachmentSO.toVO();

    // 创建示例数据
    const toCreateRecords: ApiCreateRecordsReqV2 = {
      records: [
        {
          fields: {
            single_text: 'Example Text',
            long_text: 'This is a long text example.',
            url: 'https://example.com',
            phone: '+1234567890',
            email: '<EMAIL>',
            number: 42,
            currency: 100.5,
            percent: 75,
            rating: 4,
            checkbox: true,
            single_select: 'Done',
            multi_select: ['Weekday', 'Friday'],
            member: [kelvin.id, devTeam.id, productRole.id],
            date: '2023-10-01T20:05:45.000Z',
            date_range: '2023-10-01T00:00:00.000Z/2023-10-15T00:00:00.000Z',
            attachment: [{ id: attachmentSO.id }],
          },
        },
        {
          fields: {
            single_text: 'Another Text',
            long_text: 'This is another long text example.',
            url: 'https://another-example.com',
            phone: '+0987654321',
            email: '<EMAIL>',
            number: 84,
            currency: 200.75,
            percent: 50,
            rating: 5,
            // checkbox: false,
            single_select: 'In Progress',
            multi_select: ['Monday', 'Saturday'],
            member: [jane.id],
            date: '2023-09-02T12:30:00.000Z',
            date_range: '2023-09-02T00:00:00.000Z/2023-09-20T00:00:00.000Z',
          },
        },
        {
          fields: {}, // 空行
        },
      ],
    };

    const createdRecords = await createRecordsRequest(database.id, toCreateRecords);
    expect(createdRecords.records.length).toBe(3);

    // 创建关联字段
    const linkField = await database.createField(user, {
      name: 'link',
      type: 'LINK',
      property: {
        foreignDatabaseId: database.id,
      },
    });

    // 引用文本
    const singleTextField = database.getFieldByFieldKey('single_text');
    await database.createField(user, {
      name: 'lookup_single_text',
      type: 'LOOKUP',
      property: {
        relatedLinkFieldId: linkField.id,
        lookupTargetFieldId: singleTextField.id,
      },
    });
    // 引用数字字段
    const numberField = database.getFieldByFieldKey('number');
    await database.createField(user, {
      name: 'lookup_number',
      type: 'LOOKUP',
      property: {
        relatedLinkFieldId: linkField.id,
        lookupTargetFieldId: numberField.id,
      },
    });
    // 引用货币字段
    const currencyField = database.getFieldByFieldKey('currency');
    await database.createField(user, {
      name: 'lookup_currency',
      type: 'LOOKUP',
      property: {
        relatedLinkFieldId: linkField.id,
        lookupTargetFieldId: currencyField.id,
      },
    });
    // 引用百分比字段
    const percentField = database.getFieldByFieldKey('percent');
    await database.createField(user, {
      name: 'lookup_percent',
      type: 'LOOKUP',
      property: {
        relatedLinkFieldId: linkField.id,
        lookupTargetFieldId: percentField.id,
      },
    });
    // 引用日期字段
    const dateField = database.getFieldByFieldKey('date');
    await database.createField(user, {
      name: 'lookup_date',
      type: 'LOOKUP',
      property: {
        relatedLinkFieldId: linkField.id,
        lookupTargetFieldId: dateField.id,
      },
    });
    // 引用日期范围字段
    const dateRangeField = database.getFieldByFieldKey('date_range');
    await database.createField(user, {
      name: 'lookup_date_range',
      type: 'LOOKUP',
      property: {
        relatedLinkFieldId: linkField.id,
        lookupTargetFieldId: dateRangeField.id,
      },
    });
    // 引用成员字段
    const memberField = database.getFieldByFieldKey('member');
    await database.createField(user, {
      name: 'lookup_member',
      type: 'LOOKUP',
      property: {
        relatedLinkFieldId: linkField.id,
        lookupTargetFieldId: memberField.id,
      },
    });
    // 引用附件字段
    const attachmentField = database.getFieldByFieldKey('attachment');
    await database.createField(user, {
      name: 'lookup_attachment',
      type: 'LOOKUP',
      property: {
        relatedLinkFieldId: linkField.id,
        lookupTargetFieldId: attachmentField.id,
      },
    });
    // 引用单选字段
    const singleSelectField = database.getFieldByFieldKey('single_select');
    await database.createField(user, {
      name: 'lookup_single_select',
      type: 'LOOKUP',
      property: {
        relatedLinkFieldId: linkField.id,
        lookupTargetFieldId: singleSelectField.id,
      },
    });
    // 引用多选字段
    const multiSelectField = database.getFieldByFieldKey('multi_select');
    await database.createField(user, {
      name: 'lookup_multi_select',
      type: 'LOOKUP',
      property: {
        relatedLinkFieldId: linkField.id,
        lookupTargetFieldId: multiSelectField.id,
      },
    });
    // 引用创建者字段
    const createdByField = database.getFieldByFieldKey('created_by');
    await database.createField(user, {
      name: 'lookup_created_by',
      type: 'LOOKUP',
      property: {
        relatedLinkFieldId: linkField.id,
        lookupTargetFieldId: createdByField.id,
      },
    });
    // 引用修改者字段
    const modifiedByField = database.getFieldByFieldKey('modified_by');
    await database.createField(user, {
      name: 'lookup_modified_by',
      type: 'LOOKUP',
      property: {
        relatedLinkFieldId: linkField.id,
        lookupTargetFieldId: modifiedByField.id,
      },
    });
    // 引用创建时间字段
    const createdAtField = database.getFieldByFieldKey('created_at');
    await database.createField(user, {
      name: 'lookup_created_at',
      type: 'LOOKUP',
      property: {
        relatedLinkFieldId: linkField.id,
        lookupTargetFieldId: createdAtField.id,
      },
    });
    // 引用修改时间字段
    const modifiedAtField = database.getFieldByFieldKey('modified_at');
    await database.createField(user, {
      name: 'lookup_modified_at',
      type: 'LOOKUP',
      property: {
        relatedLinkFieldId: linkField.id,
        lookupTargetFieldId: modifiedAtField.id,
      },
    });

    // 创建公式字段
    await database.createField(user, {
      name: 'formula',
      type: 'FORMULA',
      property: {
        expression: `{${singleTextField.id}} & ' - ' & {${numberField.id}}`,
      },
    });

    // 创建关联记录
    const updateRecordReq: ApiUpdateRecordReqV2 = {
      fields: {
        link: [createdRecords.records[0].id, createdRecords.records[1].id], // 关联到刚创建的两条记录
      },
    };
    const updatedRecord = await updateRecordRequest(database.id, createdRecords.records[0].id, updateRecordReq);
    expect(updatedRecord.id).toBe(createdRecords.records[0].id);
    expect(updatedRecord.fields.link).toEqual([createdRecords.records[0].id, createdRecords.records[1].id]);

    // 一些error情况测试
    test('List Records - error request test', async () => {
      vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing

      // 无效的时区
      await expect(listRecordsRequest(database.id, { timeZone: 'Invalid/Timezone' })).rejects.toThrowError();

      // cellFormat=string时, 没传递timeZone
      await expect(listRecordsRequest(database.id, { cellFormat: 'string' })).rejects.toThrowError();

      // maxRecords=0
      await expect(listRecordsRequest(database.id, { maxRecords: 0 })).rejects.toThrowError();

      // pageSize>100
      await expect(listRecordsRequest(database.id, { pageSize: 101 })).rejects.toThrowError();

      // pageSize<=0
      await expect(listRecordsRequest(database.id, { pageSize: 0 })).rejects.toThrowError();

      // 无效的offset
      await expect(listRecordsRequest(database.id, { offset: '12345abc' })).rejects.toThrowError();
    });

    // cellFormat 单元格值格式参数测试
    test('List Records - cellFormat test', async () => {
      vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing

      // cellFormat = json
      const { records } = await listRecordsRequest(database.id, { fieldKey: 'name' });
      expect(records.length).toBe(3);
      // 第一行
      const row1 = records[0];
      expect(row1.fields.single_text).toBe('Example Text');
      expect(row1.fields.long_text).toBe('This is a long text example.');
      expect(row1.fields.url).toBe('https://example.com');
      expect(row1.fields.phone).toBe('+1234567890');
      expect(row1.fields.email).toBe('<EMAIL>');
      expect(row1.fields.number).toBe(42);
      expect(row1.fields.currency).toBe(100.5);
      expect(row1.fields.percent).toBe(75);
      expect(row1.fields.rating).toBe(4);
      expect(row1.fields.auto_number).toBe(1);
      expect(row1.fields.checkbox).toBe(true);
      expect(row1.fields.single_select).toEqual('Done');
      expect(row1.fields.multi_select).toEqual(['Weekday', 'Friday']);
      const expectedMembers: OpenAPIMemberCellValue[] = [
        { id: kelvin.id, type: 'Member', name: 'kelvin' },
        { id: devTeam.id, type: 'Team', name: 'dev team' },
        { id: productRole.id, type: 'Role', name: 'product' },
      ];
      expect(row1.fields.member).toEqual(expectedMembers);
      expect(row1.fields.date).toBe('2023-10-01T20:05:45.000Z');
      expect(row1.fields.date_range).toBe('2023-10-01T00:00:00.000Z/2023-10-15T00:00:00.000Z');
      expect(row1.fields.created_by).toEqual({ id: member.id, name: member.name });
      const expectedAttachments: OpenAPIAttachmentCellValue[] = [
        {
          id: attachmentSO.id,
          name: attachmentVO.name,
          size: attachmentVO.size,
          mimeType: attachmentVO.mimeType,
          url: attachmentSO.fullPath,
          thumbnailUrl: attachmentSO.thumbnailUrl,
        },
      ];
      expect(row1.fields.attachment).toEqual(expectedAttachments);
      expect(row1.fields.link).toEqual([records[0].id, records[1].id]); // 应该关联到两条记录
      expect(row1.fields.lookup_single_text).toEqual(['Example Text', 'Another Text']);
      expect(row1.fields.lookup_number).toEqual([42, 84]);
      expect(row1.fields.lookup_currency).toEqual([100.5, 200.75]);
      expect(row1.fields.lookup_percent).toEqual([75, 50]);
      expect(row1.fields.lookup_date).toEqual(['2023-10-01T20:05:45.000Z', '2023-09-02T12:30:00.000Z']);
      expect(row1.fields.lookup_date_range).toEqual([
        '2023-10-01T00:00:00.000Z/2023-10-15T00:00:00.000Z',
        '2023-09-02T00:00:00.000Z/2023-09-20T00:00:00.000Z',
      ]);
      expect(row1.fields.lookup_member).toEqual([
        { id: kelvin.id, type: 'Member', name: 'kelvin' },
        { id: devTeam.id, type: 'Team', name: 'dev team' },
        { id: productRole.id, type: 'Role', name: 'product' },
        { id: jane.id, type: 'Member', name: 'jane' },
      ]);
      expect(row1.fields.lookup_attachment).toEqual(expectedAttachments);
      expect(row1.fields.lookup_single_select).toEqual(['Done', 'In Progress']);
      expect(row1.fields.lookup_multi_select).toEqual(['Weekday', 'Friday', 'Monday', 'Saturday']);
      expect(row1.fields.lookup_created_by).toEqual([{ id: member.id, name: member.name }]);
      expect(row1.fields.lookup_modified_by).toEqual([{ id: member.id, name: member.name }]);
      expect(row1.fields.lookup_created_at).toHaveLength(2);
      expect(row1.fields.lookup_modified_at).toHaveLength(2);
      expect(row1.fields.formula).toEqual('Example Text - 42'); // 公式字段

      // 第二行
      const row2 = records[1];
      expect(row2.fields.single_text).toBe('Another Text');
      expect(row2.fields.long_text).toBe('This is another long text example.');
      expect(row2.fields.url).toBe('https://another-example.com');
      expect(row2.fields.phone).toBe('+0987654321');
      expect(row2.fields.email).toBe('<EMAIL>');
      expect(row2.fields.number).toBe(84);
      expect(row2.fields.currency).toBe(200.75);
      expect(row2.fields.percent).toBe(50);
      expect(row2.fields.rating).toBe(5);
      expect(row2.fields.auto_number).toBe(2);
      expect(row2.fields.checkbox).toBe(false);
      expect(row2.fields.single_select).toEqual('In Progress');
      expect(row2.fields.multi_select).toEqual(['Monday', 'Saturday']);
      const expectedRow2Members: OpenAPIMemberCellValue[] = [{ id: jane.id, type: 'Member', name: 'jane' }];
      expect(row2.fields.member).toEqual(expectedRow2Members);
      expect(row2.fields.date).toBe('2023-09-02T12:30:00.000Z');
      expect(row2.fields.date_range).toBe('2023-09-02T00:00:00.000Z/2023-09-20T00:00:00.000Z');
      expect(row2.fields.created_by).toEqual({ id: member.id, name: member.name });
      expect(row2.fields.attachment).toBeNull(); // 第二条记录没有附件
      expect(row2.fields.link).toEqual([records[0].id]); // 应该关联了第一行
      expect(row2.fields.lookup_single_text).toEqual(['Example Text']);
      expect(row2.fields.lookup_number).toEqual([42]);
      expect(row2.fields.lookup_currency).toEqual([100.5]);
      expect(row2.fields.lookup_percent).toEqual([75]);
      expect(row2.fields.lookup_date).toEqual(['2023-10-01T20:05:45.000Z']);
      expect(row2.fields.lookup_date_range).toEqual(['2023-10-01T00:00:00.000Z/2023-10-15T00:00:00.000Z']);
      expect(row2.fields.lookup_member).toEqual([
        { id: kelvin.id, type: 'Member', name: 'kelvin' },
        { id: devTeam.id, type: 'Team', name: 'dev team' },
        { id: productRole.id, type: 'Role', name: 'product' },
      ]);
      expect(row2.fields.lookup_attachment).toEqual(expectedAttachments);
      expect(row2.fields.lookup_single_select).toEqual(['Done']);
      expect(row2.fields.lookup_multi_select).toEqual(['Weekday', 'Friday']);
      expect(row2.fields.lookup_created_by).toEqual([{ id: member.id, name: member.name }]);
      expect(row2.fields.lookup_modified_by).toEqual([{ id: member.id, name: member.name }]);
      expect(row2.fields.formula).toEqual('Another Text - 84'); // 公式字段

      // cellFormat = string
      const { records: stringRecords } = await listRecordsRequest(database.id, {
        timeZone: 'Asia/Shanghai',
        fieldKey: 'name',
        cellFormat: 'string',
      });
      expect(stringRecords.length).toBe(3);
      const stringRow1 = stringRecords[0];
      expect(stringRow1.fields.single_text).toEqual('Example Text');
      expect(stringRow1.fields.long_text).toEqual('This is a long text example.');
      expect(stringRow1.fields.url).toEqual('https://example.com');
      expect(stringRow1.fields.phone).toEqual('+1234567890');
      expect(stringRow1.fields.email).toEqual('<EMAIL>');
      expect(stringRow1.fields.number).toEqual('42');
      expect(stringRow1.fields.currency).toEqual('$100.50');
      expect(stringRow1.fields.percent).toEqual('75.00%');
      expect(stringRow1.fields.rating).toEqual('4');
      expect(stringRow1.fields.auto_number).toEqual('1');
      expect(stringRow1.fields.checkbox).toEqual('true');
      expect(stringRow1.fields.single_select).toEqual('Done');
      expect(stringRow1.fields.multi_select).toEqual('Weekday, Friday');
      expect(stringRow1.fields.member).toEqual('kelvin, dev team, product');
      expect(stringRow1.fields.date).toEqual('2023-10-02'); // 时区转换后在上海时间是10月2日
      expect(stringRow1.fields.date_range).toEqual('2023-10-01 -> 2023-10-15');
      expect(stringRow1.fields.created_by).toEqual(member.getName());
      expect(stringRow1.fields.attachment).toEqual(`${attachmentVO.name}(${attachmentSO.fullPath})`);
      expect(stringRow1.fields.link).toEqual('Example Text, Another Text'); // 应该关联到两条记录
      expect(stringRow1.fields.lookup_single_text).toEqual('Example Text, Another Text');
      expect(stringRow1.fields.lookup_number).toEqual('42, 84');
      expect(stringRow1.fields.lookup_currency).toEqual('$100.50, $200.75');
      expect(stringRow1.fields.lookup_percent).toEqual('75.00%, 50.00%');
      expect(stringRow1.fields.lookup_date).toEqual('2023-10-02, 2023-09-02'); // 时区转换后在上海时间是10月2日和9月2日
      expect(stringRow1.fields.lookup_date_range).toEqual('2023-10-01 -> 2023-10-15, 2023-09-02 -> 2023-09-20');
      expect(stringRow1.fields.lookup_member).toEqual('kelvin, dev team, product, jane');
      expect(stringRow1.fields.lookup_attachment).toEqual(`${attachmentVO.name}(${attachmentSO.fullPath})`);
      expect(stringRow1.fields.lookup_single_select).toEqual('Done, In Progress');
      expect(stringRow1.fields.lookup_multi_select).toEqual('Weekday, Friday, Monday, Saturday');
      expect(stringRow1.fields.lookup_created_by).toEqual(member.getName());
      expect(stringRow1.fields.lookup_modified_by).toEqual(member.getName());
      expect(stringRow1.fields.formula).toEqual('Example Text - 42'); // 公式字段

      const stringRow2 = stringRecords[1];
      expect(stringRow2.fields.single_text).toEqual('Another Text');
      expect(stringRow2.fields.long_text).toEqual('This is another long text example.');
      expect(stringRow2.fields.url).toEqual('https://another-example.com');
      expect(stringRow2.fields.phone).toEqual('+0987654321');
      expect(stringRow2.fields.email).toEqual('<EMAIL>');
      expect(stringRow2.fields.number).toEqual('84');
      expect(stringRow2.fields.currency).toEqual('$200.75');
      expect(stringRow2.fields.percent).toEqual('50.00%');
      expect(stringRow2.fields.rating).toEqual('5');
      expect(stringRow2.fields.auto_number).toEqual('2');
      expect(stringRow2.fields.checkbox).toEqual('false');
      expect(stringRow2.fields.single_select).toEqual('In Progress');
      expect(stringRow2.fields.multi_select).toEqual('Monday, Saturday');
      expect(stringRow2.fields.member).toEqual('jane');
      expect(stringRow2.fields.date).toEqual('2023-09-02'); // 时区转换后在上海时间是9月2日
      expect(stringRow2.fields.date_range).toEqual('2023-09-02 -> 2023-09-20');
      expect(stringRow2.fields.created_by).toEqual(member.getName());
      expect(stringRow2.fields.attachment).toEqual(null); // 第二条记录没有附件
      expect(stringRow2.fields.link).toEqual('Example Text'); // 应该关联了第一行, 因为是双向关联
      expect(stringRow2.fields.lookup_single_text).toEqual('Example Text');
      expect(stringRow2.fields.lookup_number).toEqual('42');
      expect(stringRow2.fields.lookup_currency).toEqual('$100.50');
      expect(stringRow2.fields.lookup_percent).toEqual('75.00%');
      expect(stringRow2.fields.lookup_date).toEqual('2023-10-02'); // 时区转换后在上海时间是10月2日
      expect(stringRow2.fields.lookup_date_range).toEqual('2023-10-01 -> 2023-10-15');
      expect(stringRow2.fields.lookup_member).toEqual('kelvin, dev team, product');
      expect(stringRow2.fields.lookup_attachment).toEqual(`${attachmentVO.name}(${attachmentSO.fullPath})`);
      expect(stringRow2.fields.lookup_single_select).toEqual('Done');
      expect(stringRow2.fields.lookup_multi_select).toEqual('Weekday, Friday');
      expect(stringRow2.fields.lookup_created_by).toEqual(member.getName());
      expect(stringRow2.fields.lookup_modified_by).toEqual(member.getName());
      expect(stringRow2.fields.formula).toEqual('Another Text - 84'); // 公式字段
    });

    // fieldKey 字段键返回格式测试
    test('List Records - fieldKey test', async () => {
      vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing

      // 默认情况下，fieldKey = name
      const { records: defaultRecords } = await listRecordsRequest(database.id);
      expect(defaultRecords.length).toBe(3);
      // 验证返回的字段
      expect(defaultRecords[0].fields.single_text).toBe('Example Text');
      expect(defaultRecords[1].fields.single_text).toBe('Another Text');

      // fieldKey = id
      const { records } = await listRecordsRequest(database.id, { fieldKey: 'id' });
      expect(records.length).toBe(3);
      // 验证返回的字段
      expect(records[0].fields[singleTextField.id]).toBe('Example Text');
      expect(records[1].fields[singleTextField.id]).toBe('Another Text');
    });

    // fields 指定字段返回测试
    test('List Records - fields test', async () => {
      vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing

      // 只有一个字段返回: 只返回单行文本字段
      const { records: singleFieldRecords } = await listRecordsRequest(database.id, {
        fields: ['single_text'],
      });
      expect(singleFieldRecords.length).toBe(3);
      // 验证返回的字段
      expect(singleFieldRecords[0].fields.single_text).toBe('Example Text');
      expect(singleFieldRecords[0].fields.long_text).toBeUndefined(); // 其他字段不应该返回
      expect(singleFieldRecords[1].fields.single_text).toBe('Another Text');
      expect(singleFieldRecords[1].fields.long_text).toBeUndefined(); // 其他字段不应该返回
      expect(singleFieldRecords[2].fields.single_text).toBeNull(); // 空行

      // 返回两个以上: 只返回单行文本和数字字段(使用字段名称)
      const { records } = await listRecordsRequest(database.id, {
        fields: ['single_text', 'number'],
      });

      expect(records.length).toBe(3);
      // 验证返回的字段
      expect(records[0].fields.single_text).toBe('Example Text');
      expect(records[0].fields.number).toBe(42);
      expect(records[0].fields.url).toBeUndefined(); // 其他字段不应该返回
      expect(records[1].fields.single_text).toBe('Another Text');
      expect(records[1].fields.number).toBe(84);
      expect(records[1].fields.url).toBeUndefined(); // 其他字段不应该返回

      // 只返回单行文本和数字字段(使用字段ID)
      const { records: idRecords } = await listRecordsRequest(database.id, {
        fields: [singleTextField.id, numberField.id],
      });

      expect(idRecords.length).toBe(3);
      // 验证返回的字段
      expect(idRecords[0].fields.single_text).toBe('Example Text');
      expect(idRecords[0].fields.number).toBe(42);
      expect(idRecords[1].fields.single_text).toBe('Another Text');
      expect(idRecords[1].fields.number).toBe(84);
    });

    // sort 排序参数测试
    test('List Records - sort test', async () => {
      vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing

      // 无排序时
      const { records: unsortedRecords } = await listRecordsRequest(database.id);
      expect(unsortedRecords.length).toBe(3);
      // 验证无排序时的顺序
      expect(unsortedRecords[0].fields.number).toBe(42);
      expect(unsortedRecords[1].fields.number).toBe(84);

      // 排序测试
      // 按照number字段降序排序
      const { records } = await listRecordsRequest(database.id, {
        sort: [
          { field: numberField.id, order: 'desc' },
          { field: singleTextField.id, order: 'asc' },
        ],
      });

      expect(records.length).toBe(3);
      // 验证排序是否正确
      expect(records[0].fields.number).toBe(84);
      expect(records[1].fields.number).toBe(42);
    });

    // filter 文本过滤测试
    test('List Records - text filter test', async () => {
      vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing

      // 为空: `==null` | `==NULL`
      const isEmpty = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'single_text==null',
        });
        expect(records.length).toBe(1);
        const { records: nullRecords } = await listRecordsRequest(database.id, {
          filter: 'single_text==NULL',
        });
        expect(nullRecords.length).toBe(1);
      };
      // 不为空: `!=null` | `!=NULL`
      const isNotEmpty = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'single_text!=null',
        });
        expect(records.length).toBe(2);
        const { records: notNullRecords } = await listRecordsRequest(database.id, {
          filter: 'single_text!=NULL',
        });
        expect(notNullRecords.length).toBe(2);
      };
      // 等于
      const isEqual = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'single_text=="Example Text"',
        });
        expect(records.length).toBe(1);
        expect(records[0].fields.single_text).toBe('Example Text');
      };
      // 不等于
      const isNotEqual = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'single_text!="Example Text"',
        });
        expect(records.length).toBe(2);
        expect(records[0].fields.single_text).toBe('Another Text');
        expect(records[1].fields.single_text).toBeNull(); // 空行
      };
      // 包含
      const isContains = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'single_text=c="Text"',
        });
        expect(records.length).toBe(2);
        expect(records[0].fields.single_text).toBe('Example Text');
        expect(records[1].fields.single_text).toBe('Another Text');
      };
      // 不包含
      const isNotContains = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'single_text=nc="Example"',
        });
        expect(records.length).toBe(2);
        expect(records[0].fields.single_text).toBe('Another Text');
        expect(records[1].fields.single_text).toBeNull(); // 空行
      };

      await isEmpty();
      await isNotEmpty();
      await isEqual();
      await isNotEqual();
      await isContains();
      await isNotContains();
    });

    // filter 数字字段过滤测试
    test('List Records - number filter test', async () => {
      vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing

      // 为空: `==null` | `==NULL`
      const isEmpty = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'number==null',
        });
        expect(records.length).toBe(1);
        const { records: nullRecords } = await listRecordsRequest(database.id, {
          filter: 'number==NULL',
        });
        expect(nullRecords.length).toBe(1);
      };
      // 不为空: `!=null` | `!=NULL`
      const isNotEmpty = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'number!=null',
        });
        expect(records.length).toBe(2);
        const { records: notNullRecords } = await listRecordsRequest(database.id, {
          filter: 'number!=NULL',
        });
        expect(notNullRecords.length).toBe(2);
      };
      // 等于
      const isEqual = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'number==42',
        });
        expect(records.length).toBe(1);
        expect(records[0].fields.number).toBe(42);
      };
      // 不等于
      const isNotEqual = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'number!=42',
        });
        expect(records.length).toBe(2);
        expect(records[0].fields.number).toBe(84);
      };
      // 大于
      const isGreaterThan = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'number>42',
        });
        expect(records.length).toBe(1);
        expect(records[0].fields.number).toBe(84);
      };
      // 大于等于
      const isGreaterThanOrEqual = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'number>=42',
        });
        expect(records.length).toBe(2);
        expect(records[0].fields.number).toBe(42);
        expect(records[1].fields.number).toBe(84);
      };
      // 小于
      const isLessThan = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'number<42',
        });
        expect(records.length).toBe(0);
      };
      // 小于等于
      const isLessThanOrEqual = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'number<=42',
        });
        expect(records.length).toBe(1);
        expect(records[0].fields.number).toBe(42);
      };

      await isEmpty();
      await isNotEmpty();
      await isEqual();
      await isNotEqual();
      await isGreaterThan();
      await isGreaterThanOrEqual();
      await isLessThan();
      await isLessThanOrEqual();
    });

    // filter 复选框过滤测试
    test('List Records - checkbox filter test', async () => {
      vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing

      // 等于true: `==true` | `==TRUE`
      const isTrue = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'checkbox==true',
        });
        expect(records.length).toBe(1);
        expect(records[0].fields.checkbox).toBe(true);
      };
      // 等于false: `==false` | `==FALSE`
      const isFalse = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'checkbox==false',
        });
        expect(records.length).toBe(2);
        expect(records[0].fields.checkbox).toBe(false);
        expect(records[1].fields.checkbox).toBe(false);
      };

      await isTrue();
      await isFalse();
    });

    // filter 单选字段过滤测试
    test('List Records - single select filter test', async () => {
      vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing

      // 为空: `==null` | `==NULL`
      const isEmpty = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'single_select==null',
        });
        expect(records.length).toBe(1);
        const { records: nullRecords } = await listRecordsRequest(database.id, {
          filter: 'single_select==NULL',
        });
        expect(nullRecords.length).toBe(1);
      };
      // 不为空: `!=null` | `!=NULL`
      const isNotEmpty = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'single_select!=null',
        });
        expect(records.length).toBe(2);
        const { records: notNullRecords } = await listRecordsRequest(database.id, {
          filter: 'single_select!=NULL',
        });
        expect(notNullRecords.length).toBe(2);
      };
      // 等于
      const isEqual = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'single_select=="Done"',
        });
        expect(records.length).toBe(1);
        expect(records[0].fields.single_select).toBe('Done');
      };
      // 不等于
      const isNotEqual = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'single_select!="Done"',
        });
        expect(records.length).toBe(2);
        expect(records[0].fields.single_select).toBe('In Progress');
      };
      // 包含
      const isContains = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'single_select=c=("Done","In Progress")',
        });
        expect(records.length).toBe(2);
        expect(records[0].fields.single_select).toBe('Done');
        expect(records[1].fields.single_select).toBe('In Progress');
      };
      // 不包含
      const isNotContains = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'single_select=nc=("In Progress")',
        });
        expect(records.length).toBe(2);
        expect(records[0].fields.single_select).toBe('Done');
        expect(records[1].fields.single_select).toBeNull(); // 空行
      };

      await isEmpty();
      await isNotEmpty();
      await isEqual();
      await isNotEqual();
      await isContains();
      await isNotContains();
    });

    // filter 多选字段过滤测试
    test('List Records - multi select filter test', async () => {
      vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing
      // 为空: `==null` | `==NULL`
      const isEmpty = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'multi_select==null',
        });
        expect(records.length).toBe(1);
        const { records: nullRecords } = await listRecordsRequest(database.id, {
          filter: 'multi_select==NULL',
        });
        expect(nullRecords.length).toBe(1);
      };
      // 不为空: `!=null` | `!=NULL`
      const isNotEmpty = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'multi_select!=null',
        });
        expect(records.length).toBe(2);
        const { records: notNullRecords } = await listRecordsRequest(database.id, {
          filter: 'multi_select!=NULL',
        });
        expect(notNullRecords.length).toBe(2);
      };
      // 等于
      const isEqual = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'multi_select==("Weekday","Friday")',
        });
        expect(records.length).toBe(1);
        expect(records[0].fields.multi_select).toEqual(['Weekday', 'Friday']);
      };
      // 不等于
      const isNotEqual = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'multi_select!=("Weekday","Friday")',
        });
        expect(records.length).toBe(2);
        expect(records[0].fields.multi_select).toEqual(['Monday', 'Saturday']);
      };
      // 包含
      const isContains = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'multi_select=c=("Monday","Friday")',
        });
        expect(records.length).toBe(2);
        expect(records[0].fields.multi_select).toEqual(['Weekday', 'Friday']);
        expect(records[1].fields.multi_select).toEqual(['Monday', 'Saturday']);
      };
      // 不包含
      const isNotContains = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'multi_select=nc=("Friday")',
        });
        expect(records.length).toBe(2);
        expect(records[0].fields.multi_select).toEqual(['Monday', 'Saturday']);
      };

      await isEmpty();
      await isNotEmpty();
      await isEqual();
      await isNotEqual();
      await isContains();
      await isNotContains();
    });

    // filter 日期字段过滤测试
    test('List Records - date filter test', async () => {
      vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing

      // 为空: `==null` | `==NULL`
      const isEmpty = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'date==null',
        });
        expect(records.length).toBe(1);
        expect(records[0].fields.date).toBeNull(); // 空行
        const { records: nullRecords } = await listRecordsRequest(database.id, {
          filter: 'date==NULL',
        });
        expect(nullRecords.length).toBe(1);
        expect(nullRecords[0].fields.date).toBeNull(); // 空行
      };
      // 不为空: `!=null` | `!=NULL`
      const isNotEmpty = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'date!=null',
          timeZone: 'Asia/Shanghai',
          cellFormat: 'string',
        });
        expect(records.length).toBe(2);
        expect(records[0].fields.date).toBe('2023-10-02'); // 时区转换后在上海时间是10月2日
        expect(records[1].fields.date).toBe('2023-09-02');
        const { records: notNullRecords } = await listRecordsRequest(database.id, {
          filter: 'date!=NULL',
          timeZone: 'Asia/Shanghai',
          cellFormat: 'string',
        });
        expect(notNullRecords.length).toBe(2);
        expect(notNullRecords[0].fields.date).toBe('2023-10-02'); // 时区转换后在上海时间是10月2日
        expect(notNullRecords[1].fields.date).toBe('2023-09-02');
      };
      // 等于
      const isEqual = async () => {
        const { records } = await listRecordsRequest(database.id, {
          timeZone: 'Asia/Shanghai', // 设置东八区后, 下面的过滤值会转换为指定范围来查询
          filter: 'date=="2023-10-02"',
          cellFormat: 'string',
        });
        expect(records.length).toBe(1);
        expect(records[0].fields.date).toBe('2023-10-02');
      };
      // 不等于
      const isNotEqual = async () => {
        const { records } = await listRecordsRequest(database.id, {
          timeZone: 'Asia/Shanghai',
          filter: 'date!="2023-10-02"',
          cellFormat: 'string',
        });
        expect(records.length).toBe(2);
        expect(records[0].fields.date).toBe('2023-09-02');
        expect(records[1].fields.date).toBeNull(); // 空行
      };
      // 范围
      const isRange = async () => {
        const { records } = await listRecordsRequest(database.id, {
          timeZone: 'Asia/Shanghai',
          filter: 'date>="2023-10-01";date<="2023-10-31"',
          cellFormat: 'string',
        });
        expect(records.length).toBe(1);
        expect(records[0].fields.date).toBe('2023-10-02');
      };

      await isEmpty();
      await isNotEmpty();
      await isEqual();
      await isNotEqual();
      await isRange();
    });

    // filter 关联字段过滤测试
    test('List Records - link filter test', async () => {
      vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing

      // 为空: `==null` | `==NULL`
      const isEmpty = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'link==null',
        });
        expect(records.length).toBe(1);
        const { records: nullRecords } = await listRecordsRequest(database.id, {
          filter: 'link==NULL',
        });
        expect(nullRecords.length).toBe(1);
      };
      // 不为空: `!=null` | `!=NULL`
      const isNotEmpty = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: 'link!=null',
        });
        expect(records.length).toBe(2);
        const { records: notNullRecords } = await listRecordsRequest(database.id, {
          filter: 'link!=NULL',
        });
        expect(notNullRecords.length).toBe(2);
      };
      // 等于
      const isEqual = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: `link==("${createdRecords.records[0].id}", "${createdRecords.records[1].id}")`,
        });
        expect(records.length).toBe(1);
        expect(records[0].fields.single_text).toBe('Example Text');
      };
      // 不等于
      const isNotEqual = async () => {
        const { records } = await listRecordsRequest(database.id, {
          filter: `link!=("${createdRecords.records[0].id}", "${createdRecords.records[1].id}")`,
        });
        expect(records.length).toBe(2);
        expect(records[0].fields.single_text).toBe('Another Text');
      };

      await isEmpty();
      await isNotEmpty();
      await isEqual();
      await isNotEqual();
    });

    // 使用带有空格/中文的字段名进行过滤测试
    test('List Records - filter with space/Chinese field name', async () => {
      vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing

      // 创建一个带有空格和中文的字段
      const withSpacesField = await database.createField(user, {
        name: '我是 带空格的 字段',
        type: 'SINGLE_TEXT',
      });

      // 创建记录
      const recordsReq: ApiCreateRecordsReqV2 = {
        records: [
          { fields: { single_text: 'Record 1', [withSpacesField.id]: 'Value 1' } },
          { fields: { single_text: 'Record 2', [withSpacesField.id]: 'Value 2' } },
        ],
      };
      const created = await createRecordsRequest(database.id, recordsReq);
      expect(created.records.length).toBe(2);

      // 使用空格和中文字段名进行过滤
      const { records } = await listRecordsRequest(database.id, {
        filter: `{${withSpacesField.getName()}}=="Value 1"`,
      });
      expect(records.length).toBe(1);
      expect(records[0].fields.single_text).toBe('Record 1');
      expect(records[0].fields[withSpacesField.getName()]).toBe('Value 1');
    });
  });

  // 翻页测试
  test('List Records - Offset pagination', async () => {
    vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing
    // 创建表
    const databaseNode = await rootFolder.createChildSimple(user, {
      name: 'database',
      resourceType: 'DATABASE',
      fields: [
        {
          name: 'single_text',
          type: 'SINGLE_TEXT',
        },
        {
          name: 'number',
          type: 'NUMBER',
          property: DefaultNumberFieldProperty,
        },
      ],
    });
    const database = await databaseNode.toResourceSO<DatabaseSO>();

    // 创建11行数据, 以每页2条来翻页
    const toCreateRecords: RecordField[] = [];
    for (let i = 0; i < 11; i++) {
      toCreateRecords.push({
        fields: {
          single_text: `text-${i}`,
          number: i,
        },
      });
    }
    // 分割成每10条一组
    const chunkSize = 10;
    const recordChunks: RecordField[][] = [];
    for (let i = 0; i < toCreateRecords.length; i += chunkSize) {
      recordChunks.push(toCreateRecords.slice(i, i + chunkSize));
    }

    for (const chunk of recordChunks) {
      const createdRecords = await createRecordsRequest(database.id, { records: chunk });
      expect(createdRecords.records.length).toBe(chunk.length);
    }

    // 开始翻页, 以2条为一页, 利用 offset 来翻页, 直到hasMore为false或者offset为空即没有更多数据
    const pageSize = 2;
    let hasMore = true;
    let offset: string | null = null;
    // 计数翻页次数
    let turnPageCount = 0;

    while (hasMore) {
      const recordsResponse = await listRecordsRequest(database.id, {
        pageSize,
        offset: offset ?? undefined,
      });
      //   console.log(`翻页 ${turnPageCount}:`, recordsResponse.records);
      // 叠加翻页次数
      turnPageCount++;
      // 应该每次返回不大于 pageSize 条数据
      expect(recordsResponse.records.length).toBeLessThanOrEqual(pageSize);

      // 如果hasMore为false，表示没有更多数据了
      if (!recordsResponse.hasMore) {
        break;
      }
      hasMore = recordsResponse.hasMore;
      offset = recordsResponse.offset ?? null;
    }

    expect(turnPageCount).toBe(6); // 11条数据，每页2条，应该翻6次
  });

  // 更新记录测试
  test('Update Records', async () => {
    vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing

    // 创建表
    const databaseNode = await rootFolder.createChildSimple(user, {
      name: 'database',
      resourceType: 'DATABASE',
      fields: [
        {
          name: 'single_text',
          type: 'SINGLE_TEXT',
        },
      ],
    });
    const database = await databaseNode.toResourceSO<DatabaseSO>();

    // 创建记录
    const toCreateRecords: ApiCreateRecordsReqV2 = {
      records: [
        { fields: { single_text: 'Record 1' } },
        { fields: { single_text: 'Record 2' } },
        { fields: { single_text: 'Record 3' } },
      ],
    };
    const createdRecords = await createRecordsRequest(database.id, toCreateRecords);
    expect(createdRecords.records.length).toBe(3);

    // 更新记录
    const updatedRecord = await updateRecordRequest(database.id, createdRecords.records[0].id, {
      fields: { single_text: 'Updated Record 1' },
    });
    expect(updatedRecord.id).toBe(createdRecords.records[0].id);
    expect(updatedRecord.fields.single_text).toBe('Updated Record 1');

    // 验证更新后的记录
    const remainingRecords = await listRecordsRequest(database.id);
    expect(remainingRecords.records.length).toBe(3);
    expect(remainingRecords.records[0].fields.single_text).toBe('Updated Record 1');
    expect(remainingRecords.records[1].fields.single_text).toBe('Record 2');
    expect(remainingRecords.records[2].fields.single_text).toBe('Record 3');

    // 批量更新记录
    const toUpdateRecords: ApiUpdateRecordsReqV2 = {
      records: [
        { id: createdRecords.records[1].id, fields: { single_text: 'Updated Record 2' } },
        { id: createdRecords.records[2].id, fields: { single_text: 'Updated Record 3' } },
      ],
    };
    const updatedRecords = await updateRecordsRequest(database.id, toUpdateRecords);
    expect(updatedRecords.records.length).toBe(2);
    expect(updatedRecords.records[0].id).toBe(createdRecords.records[1].id);
    expect(updatedRecords.records[0].fields.single_text).toBe('Updated Record 2');
    expect(updatedRecords.records[1].id).toBe(createdRecords.records[2].id);
    expect(updatedRecords.records[1].fields.single_text).toBe('Updated Record 3');
  });

  // 删除记录测试
  test('Delete Records', async () => {
    vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing
    // 创建表
    const databaseNode = await rootFolder.createChildSimple(user, {
      name: 'database',
      resourceType: 'DATABASE',
      fields: [
        {
          name: 'single_text',
          type: 'SINGLE_TEXT',
        },
      ],
    });
    const database = await databaseNode.toResourceSO<DatabaseSO>();

    // 创建记录
    const toCreateRecords: ApiCreateRecordsReqV2 = {
      records: [
        { fields: { single_text: 'Record 1' } },
        { fields: { single_text: 'Record 2' } },
        { fields: { single_text: 'Record 3' } },
      ],
    };
    const createdRecords = await createRecordsRequest(database.id, toCreateRecords);
    expect(createdRecords.records.length).toBe(3);

    // 删除单条记录
    const deletedRecord = await deleteRecordRequest(database.id, createdRecords.records[0].id);
    expect(deletedRecord.id).toBe(createdRecords.records[0].id);
    expect(deletedRecord.deleted).toBe(true);

    // 验证删除后剩余记录
    const remainingRecords = await listRecordsRequest(database.id);
    expect(remainingRecords.records.length).toBe(2);
    expect(remainingRecords.records[0].fields.single_text).toBe('Record 2');
    expect(remainingRecords.records[1].fields.single_text).toBe('Record 3');

    // 批量删除记录
    const recordIdsToDelete = remainingRecords.records.map((record) => record.id);
    const deletedRecords = await deleteRecordsRequest(database.id, recordIdsToDelete);
    expect(deletedRecords.length).toBe(2);
  });
});
