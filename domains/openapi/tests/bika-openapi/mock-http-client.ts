import { Bika, type IHttpClient, type ResponseVO } from '@bika/sdk-ts';
import { BikaOpenAPIs } from '../../apis/bika/index';

// 覆盖原http client，用于测试
class MockBikaHttpClient implements IHttpClient {
  private _token: string;

  constructor(token: string) {
    this._token = token;
  }

  async post<T, BODY = any>(url: string, body?: BODY): Promise<ResponseVO<T>> {
    const res = await BikaOpenAPIs.request(url, {
      method: 'POST',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this._token}`,
      },
    });
    return res.json();
  }

  async patch<T, BODY = any>(url: string, body?: BODY): Promise<ResponseVO<T>> {
    const res = await BikaOpenAPIs.request(url, {
      method: 'PATCH',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this._token}`,
      },
    });
    return res.json();
  }

  async delete<T>(url: string): Promise<ResponseVO<T>> {
    const res = await BikaOpenAPIs.request(url, {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${this._token}`,
      },
    });
    return res.json();
  }

  async get<T>(url: string): Promise<ResponseVO<T>> {
    const res = await BikaOpenAPIs.request(url, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${this._token}`,
      },
    });
    return res.json();
  }
}

export function createBikaApi(apiKey: string): Bika {
  const bika = new Bika({ apiKey, customHttpClient: new MockBikaHttpClient(apiKey) });

  return bika;
}
