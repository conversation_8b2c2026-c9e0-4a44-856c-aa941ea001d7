/* eslint-disable no-param-reassign */
import { WidgetSO } from '@bika/domains/dashboard/server/widgets/widget-so';
import { CellValueConvertorFactory } from '@bika/domains/database/server/cells/cellvalue-convertor/factory';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { ViewSO } from '@bika/domains/database/server/views/view-so';
import { FieldTypeHelper, PercentFieldTypeHelper } from '@bika/domains/database/shared';
import { UserSO } from '@bika/domains/user/server/user-so';
import {
  NumberWidgetDatasource,
  WidgetDatasourceType,
  NumberWidgetBO,
  NumberWidgetDatabaseDatasource,
  NumberWidgetCustomDatasource,
  ChartMetricsAggregateTypeSchema,
  AggregateType,
} from '@bika/types/dashboard/bo';
import { NumberWidgetRenderVO } from '@bika/types/dashboard/vo';
import { DatabaseField } from '@bika/types/database/bo';
import { NodeRenderOpts } from '@bika/types/node/vo';
import { DatasourceLoaderSO } from '../chart-widget/datasource-loader-so';

export class NumberDatasourceLoaderSO extends DatasourceLoaderSO<NumberWidgetRenderVO> {
  private readonly _widgetSO: WidgetSO;

  get widget(): WidgetSO {
    return this._widgetSO;
  }

  get datasource(): NumberWidgetDatasource {
    return (this.widget.bo as NumberWidgetBO).datasource;
  }

  get type(): WidgetDatasourceType {
    return this.datasource.type;
  }

  get widgetBO(): NumberWidgetBO {
    return this.widget.bo as NumberWidgetBO;
  }

  constructor(widget: WidgetSO) {
    super();
    this._widgetSO = widget;
  }

  static init(widget: WidgetSO) {
    return new NumberDatasourceLoaderSO(widget);
  }

  async getDatabase(): Promise<DatabaseSO | null> {
    if (this.type !== 'DATABASE') {
      return null;
    }
    const datasource = this.datasource as NumberWidgetDatabaseDatasource;
    if (!datasource.databaseId) {
      return null;
    }
    return DatabaseSO.getDatabaseById(datasource.databaseId);
  }

  async getView(): Promise<ViewSO | null> {
    const databaseSO = await this.getDatabase();
    if (!databaseSO) {
      return null;
    }
    const viewId = (this.datasource as NumberWidgetDatabaseDatasource).viewId;
    if (!viewId) {
      return null;
    }
    const viewModel = await ViewSO.findOneById(databaseSO.id, viewId);
    if (!viewModel) {
      return null;
    }
    return new ViewSO(databaseSO, viewModel);
  }

  async load(opts?: NodeRenderOpts): Promise<NumberWidgetRenderVO | undefined> {
    if (this.type === 'DATABASE') {
      const viewSO = await this.getView();
      if (!viewSO) {
        return undefined;
      }
      return this.loadFromDatabase(viewSO, opts);
    }
    if (this.type === 'CUSTOM') {
      const datasource = this.datasource as NumberWidgetCustomDatasource;
      return {
        value: String(datasource.number),
        targetValue: this.getTargetValue(datasource.number),
      };
    }
    return undefined;
  }

  private async loadFromDatabase(viewSO: ViewSO, opts?: NodeRenderOpts): Promise<NumberWidgetRenderVO | undefined> {
    const datasource = this.datasource as NumberWidgetDatabaseDatasource;
    if (datasource.metricsType === 'AGGREGATION_BY_FIELD' && datasource.metrics?.aggregationType === 'HIDDEN') {
      return {
        value: undefined,
        targetValue: this.getTargetValue(0),
      };
    }
    const user: UserSO | undefined = opts?.userId ? await UserSO.init(opts.userId) : undefined;
    if (datasource.metricsType === 'COUNT_RECORDS') {
      const value = await viewSO.totalRecordsCountWithoutGroup(user);
      return {
        value: String(value),
        targetValue: this.getTargetValue(value),
      };
    }
    const groupFieldId = datasource.metrics?.fieldId;
    const groupField = viewSO.database.findFieldByFieldKey(groupFieldId!);
    if (!groupField) {
      return undefined;
    }
    let param: { type: AggregateType; field: DatabaseField } | undefined;
    if (ChartMetricsAggregateTypeSchema.safeParse(datasource.metrics?.aggregationType).success) {
      param = {
        type: datasource.metrics!.aggregationType as AggregateType,
        field: groupField.toBO(),
      };
    }

    const records = await viewSO.aggregateRecordByFieldKey(groupField.toBO(), param, user);

    const toVO = (value: number | undefined): NumberWidgetRenderVO | undefined => {
      if (value === undefined) {
        return {
          value: undefined,
          targetValue: this.getTargetValue(0),
        };
      }
      if (!value) {
        return {
          value: '0',
          targetValue: this.getTargetValue(0),
        };
      }
      if (groupField.type === 'PERCENT' || groupField.type === 'CURRENCY') {
        const helper = FieldTypeHelper.create(groupField.toBO(), { fields: [] }) as PercentFieldTypeHelper;
        const groupFieldVO = groupField.toVO();
        return {
          value: helper.cellValueToString(value)!,
          targetValue: this.getTargetValue(value),
          symbol:
            groupFieldVO.type === 'PERCENT' || groupFieldVO.type === 'CURRENCY'
              ? groupFieldVO.property?.symbol
              : undefined,
          symbolAlign:
            groupFieldVO.type === 'PERCENT' || groupFieldVO.type === 'CURRENCY'
              ? groupFieldVO.property?.symbolAlign
              : undefined,
        };
      }
      if (groupField.type === 'NUMBER') {
        const ctv = CellValueConvertorFactory.create(groupField.toBO(), { fields: [] });
        const groupFieldVO = groupField.toVO();
        return {
          value: ctv.cellValueToString(value)!,
          targetValue: this.getTargetValue(value),
          symbol: groupFieldVO.type === 'NUMBER' ? groupFieldVO.property?.symbol : undefined,
          symbolAlign: groupFieldVO.type === 'NUMBER' ? groupFieldVO.property?.symbolAlign : undefined,
        };
      }
      return {
        value: String(value),
        targetValue: this.getTargetValue(value),
      };
    };

    // format data
    const getValue = (): number | undefined => {
      const { aggregationType } = datasource.metrics || {};
      if (aggregationType === 'NOT_FILLED') {
        return records.reduce((acc, record) => {
          if (this.isEmpty(record.data) && this.isEmpty(record.values)) {
            acc += record.recordCount;
          }
          return acc;
        }, 0);
      }
      if (aggregationType === 'FILLED') {
        return records.reduce((acc: number, record) => {
          if (!this.isEmpty(record.data) || !this.isEmpty(record.values)) {
            acc += record.recordCount;
          }
          return acc;
        }, 0);
      }
      const aggregateNumber: number[] = records
        .map((record) => record.aggregateNumber)
        .filter((number) => number !== undefined && number !== null);

      if (aggregationType === 'SUM') {
        return aggregateNumber.reduce((cur, pre) => cur + pre, 0);
      }
      if (aggregationType === 'AVG') {
        const recordCount = records.reduce((acc, record) => {
          if (record.aggregateNumber !== null && record.aggregateNumber !== undefined) {
            acc += record.recordCount;
          }
          return acc;
        }, 0);
        const sum = records.reduce((cur, pre) => cur + (pre.aggregateNumber || 0) * pre.recordCount, 0);
        return sum / recordCount;
      }
      if (aggregationType === 'MAX') {
        return Math.max(...aggregateNumber);
      }
      if (aggregationType === 'MIN') {
        return Math.min(...aggregateNumber);
      }

      return undefined;
    };
    const value = getValue();
    return toVO(value);
  }

  private getTargetValue(value: number): string | undefined {
    const { targetValue } = this.widgetBO;
    if (!targetValue) {
      return undefined;
    }

    if (Number.isNaN(Number(targetValue))) {
      return String(targetValue);
    }

    const targetNum = Number(targetValue);
    if (targetNum === 0) return `${targetValue}`;
    const percentage = ((value / targetNum) * 100).toFixed(2);
    return `${targetValue} (${percentage}%)`;
  }
}
