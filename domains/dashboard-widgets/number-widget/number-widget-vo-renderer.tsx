'use client';

import { useRef, useEffect, useState } from 'react';
import type { ILocaleContext } from '@bika/contents/i18n/context';
import type { NumberWidgetVO } from '@bika/types/dashboard/vo';
import { Stack } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/text-components';

interface Props {
  widget: NumberWidgetVO;
  locale: ILocaleContext;
}

export function NumberWidgetVORenderer({ widget, locale }: Props) {
  const { summaryDescription, value } = widget;
  const { i: iStr } = locale;
  const containerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const textRef = useRef<HTMLDivElement>(null);
  const [scale, setScale] = useState(1);
  const [valueText, setValueText] = useState<string | undefined>(value?.value);
  const [valueHeight, setValueHeight] = useState<number | undefined>(undefined);

  const symbolText = value?.symbol;
  const symbolAlign = value?.symbolAlign;

  const calculateScale = () => {
    if (!containerRef.current || !contentRef.current) return;

    const containerWidth = containerRef.current.clientWidth;
    const containerHeight = containerRef.current.clientHeight;

    const contentWidth = contentRef.current.scrollWidth;
    const contentHeight = contentRef.current.scrollHeight;

    const SAFE_PADDING = 20;
    const safeWidth = containerWidth - SAFE_PADDING;
    const safeHeight = containerHeight - SAFE_PADDING;

    const isWidthOverflow = contentWidth - containerWidth > 0;
    const isHeightOverflow = contentHeight - containerHeight > 0;

    const textWidth = textRef.current?.scrollWidth;
    const isTextOverflow = textWidth && textWidth > safeWidth;

    if (isWidthOverflow || isHeightOverflow || isTextOverflow) {
      const widthScale = safeWidth / contentWidth;
      const heightScale = safeHeight / contentHeight;
      const newScale = Math.min(widthScale, heightScale, 1);

      if (isTextOverflow) {
        const textScale = safeWidth / textWidth;
        setScale(Math.min(newScale, textScale));
      } else {
        setScale(newScale);
      }
    } else {
      setScale(1);
    }

    // Store the original height to apply scaled height
    if (contentRef.current) {
      setValueHeight(contentRef.current.scrollHeight);
    }
  };

  useEffect(() => {
    calculateScale();

    const resizeHandler = () => calculateScale();
    window.addEventListener('resize', resizeHandler);

    let observer: ResizeObserver;
    if (contentRef.current) {
      observer = new ResizeObserver(calculateScale);
      observer.observe(contentRef.current);
    }

    return () => {
      window.removeEventListener('resize', resizeHandler);
      observer?.disconnect();
    };
  }, [widget.value?.value, widget.value?.targetValue]);

  useEffect(() => {
    if (!valueText || !symbolText) return;

    if (symbolAlign === 'left' && value?.value?.startsWith(symbolText)) {
      setValueText(value.value.substring(symbolText.length));
    }
    if (symbolAlign !== 'left' && value?.value?.endsWith(symbolText)) {
      setValueText(value.value.substring(0, value.value.length - symbolText.length));
    }
  }, []);

  return (
    <Stack ref={containerRef} overflow="hidden" justifyContent="center" height="100%" width="100%" p={1}>
      <div
        ref={contentRef}
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          textAlign: 'center',
          justifyContent: 'center',
          transform: `scale(${scale})`,
          transformOrigin: 'center top',
          transition: 'transform 0.3s ease-out',
          height: valueHeight ? `${valueHeight * scale}px` : 'auto',
        }}
      >
        {summaryDescription && <Typography level="h1">{iStr(summaryDescription)}</Typography>}

        <Stack ref={textRef} direction={symbolAlign === 'left' ? 'row-reverse' : 'row'} alignItems="baseline">
          <Typography
            sx={{
              fontSize: '80px',
              fontWeight: 'bold',
              color: 'var(--brand)',
            }}
          >
            {valueText}
          </Typography>
          <Typography
            sx={{
              fontSize: '50px',
              fontWeight: 'bold',
              color: 'var(--brand)',
            }}
          >
            {symbolText}
          </Typography>
        </Stack>

        {value?.targetValue && <Typography>{value.targetValue}</Typography>}
      </div>
    </Stack>
  );
}
