import assert from 'assert';
import { generateNanoID } from 'basenext/utils/nano-id';
import _ from 'lodash';
import { FolderSO } from '@bika/domains/node/server/folder-so';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { IRelationIdOpts, NodeResourceSO } from '@bika/domains/node/server/types';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { PrismaPromise, Prisma, db, $Enums } from '@bika/server-orm';
import { AiNodeBO, AINodeBOSchema, AINodeAgentSchema, AINodeAgentBO, AINodeSourceBO } from '@bika/types/ai/bo';
import type { AINodeUpdateDTO, AINodeCreateDTO } from '@bika/types/ai/dto';
import { CONST_PREFIX_NODTPL, CONST_PREFIX_FOLD, CONST_PREFIX_AI_NODE } from '@bika/types/database/vo';
import { iString, iStringParse, Locale } from '@bika/types/i18n/bo';
import type { ToBoOptions, NodeResourceType, ToTemplateOptions } from '@bika/types/node/bo';
import type { NodeRenderOpts, ResourceVO } from '@bika/types/node/vo';
import { SkillsetVO } from '@bika/types/skill/vo';
import { AIModelOptions } from '../../ai/server/types';
import { AISkillsetServerRegistry } from '../../ai-skillset/server-registry';
import { MemberModel } from '../../unit/server/types';
import { UserModel } from '../../user/server/types';

const _AINodeInclude = Prisma.validator<Prisma.AiNodeDefaultArgs>()({
  include: {
    node: true,
  },
});

export type AINodeModel = Prisma.AiNodeGetPayload<typeof _AINodeInclude>;

export class AINodeSO extends NodeResourceSO {
  private _model: AINodeModel;

  get resourceType(): NodeResourceType {
    return 'AI';
  }

  public get id() {
    return this._model.id;
  }

  public get model() {
    return this._model;
  }

  public get name(): iString {
    return this._model.name as iString;
  }

  public get spaceId(): string {
    return this._model.spaceId;
  }

  public get templateId(): string | undefined {
    return this._model.templateId || undefined;
  }

  private constructor(private readonly po: AINodeModel) {
    super();
    this._model = po;
  }

  public static async init(id: string) {
    const po = await db.prisma.aiNode.findUnique({
      where: { id },
      include: {
        node: true,
      },
    });
    assert(po, `not found file node by id: ${id}`);

    return new AINodeSO(po);
  }

  public static async initMaybeNull(id: string) {
    const po = await db.prisma.aiNode.findUnique({
      where: { id },
      include: {
        node: true,
      },
    });
    if (po) {
      return new AINodeSO(po);
    }
    return null;
  }

  public static async getByIds(ids: string[]): Promise<AINodeSO[]> {
    const pos = await db.prisma.aiNode.findMany({
      where: { id: { in: ids } },
      include: { node: true },
    });
    return pos.map((po) => new AINodeSO(po));
  }

  toNodeSO(): NodeSO {
    return NodeSO.initWithModel(this._model.node!);
  }

  async getSpace(): Promise<SpaceSO> {
    return SpaceSO.init(this._model.spaceId);
  }

  async toBO(_opts?: ToBoOptions): Promise<AiNodeBO> {
    const savePO = this._model.bo as AiNodeBO;
    const asMember = (await MemberSO.countByRelationId(this.id, this.spaceId)) > 0;
    /**
     * savePO 有可能是空的，原因是创建节点的时候，Prompt 等数据可以不填写
     */
    return {
      ...savePO,
      resourceType: 'AI',
      name: this._model.name as iString,
      description: this._model.description as iString,
      id: this.id,
      asMember,
      icon: this.toNodeSO().icon,
    };
  }

  async toVO(opts?: NodeRenderOpts): Promise<ResourceVO> {
    // throw new Error('Method not implemented.');
    // const attachmentSO = await AttachmentSO.init(this._model.attachmentId);

    // const attachmentVO = attachmentSO.toVO();
    const skillsets = await this.getSkillsets(opts?.locale);
    return {
      id: this.id,
      // name: this._model.name as iString,
      // description: this._model.description as iString,
      resourceType: 'AI',
      skillsets,
      // attachment: attachmentVO,
      // url: attachmentSO.fullPath,
    };
  }

  async toTemplate(opts?: ToTemplateOptions): Promise<AiNodeBO> {
    const { getTemplateId } = opts || {};
    const bo = _.omit(await this.toBO(), 'id');
    if (!bo.templateId) {
      bo.templateId = this.id;
    }

    if (bo.sources?.length) {
      const sources: AINodeSourceBO[] = [];
      for (const source of bo.sources) {
        if (source.type === 'NODE') {
          const templateId = getTemplateId?.(source.nodeId);
          if (templateId) {
            sources.push({ ...source, nodeId: templateId });
          }
        } else {
          sources.push(source);
        }
      }
      bo.sources = sources;
    }
    return bo;
  }

  override relationInstanceId(aiNode: AiNodeBO, opts: IRelationIdOpts): boolean {
    const { replaceInstanceId, convertToInstanceId } = opts;
    // for bo to create new instance
    if (replaceInstanceId) {
      if (aiNode.sources?.length) {
        const sources: AINodeSourceBO[] = [];
        for (const source of aiNode.sources) {
          if (source.type === 'NODE') {
            sources.push({ ...source, nodeId: replaceInstanceId(source.nodeId) });
          } else {
            sources.push(source);
          }
        }
        // eslint-disable-next-line no-param-reassign
        aiNode.sources = sources;
      }
    }
    // for pure template create new instance
    if (convertToInstanceId) {
      const catchConvertToInstanceId = (nodeId: string) => {
        try {
          return convertToInstanceId(nodeId);
        } catch (_e) {
          return undefined;
        }
      };
      if (aiNode.sources?.length) {
        const sources: AINodeSourceBO[] = [];
        for (const source of aiNode.sources) {
          if (source.type === 'NODE') {
            const nodeId = catchConvertToInstanceId(source.nodeId);
            if (nodeId) {
              sources.push({ ...source, nodeId });
            }
          } else {
            sources.push(source);
          }
        }
        // eslint-disable-next-line no-param-reassign
        aiNode.sources = sources;
      }
    }
    return true;
  }

  async setTemplateId(): Promise<PrismaPromise<Prisma.BatchPayload>[]> {
    const operations: PrismaPromise<Prisma.BatchPayload>[] = [];
    if (!this.templateId) {
      operations.push(
        ...[
          db.prisma.aiNode.updateMany({
            where: { id: this.id },
            data: {
              templateId: this.id,
            },
          }),
          db.prisma.node.updateMany({
            where: { id: this.id },
            data: {
              templateId: this.id,
            },
          }),
        ],
      );
    }
    return operations;
  }

  async updateWithNodeInput(
    user: UserSO,
    data: AINodeUpdateDTO,
  ): Promise<{
    operations: PrismaPromise<AINodeModel | Prisma.BatchPayload>[];
    // mongoSessions: MongoTransactionCB[];
  }> {
    const { name, description, bo, templateId, resourceType } = data;
    const realBO: AiNodeBO | undefined = bo
      ? AINodeBOSchema.parse({
          name,
          description,
          templateId,
          resourceType,
          ...bo,
        })
      : undefined;
    // 修改文件的附件
    const operations: PrismaPromise<AINodeModel | Prisma.BatchPayload>[] = [
      db.prisma.aiNode.update({
        where: {
          id: this.id,
        },
        data: {
          name,
          description,
          updatedBy: user.id,
          // TODO: 动态更新
          bo: realBO,
          node: {
            update: {
              name,
              description,
              updatedBy: user.id,
            },
          },
        } as Prisma.MirrorUpdateInput,
        include: {
          node: true,
        },
      }),
      db.prisma.unitMember.updateMany({
        where: {
          relationId: this.id,
          spaceId: this.spaceId,
          relationType: 'AI',
        },
        data: {
          name: iStringParse(name, user.locale),
        },
      }),
    ];

    // const mongoSessions: MongoTransactionCB[] = [];
    // if (file && file.fileType === 'ATTACHMENT' && file.attachmentId !== this.model.attachmentId) {
    //   // 改了附件
    //   const space = await this.getSpace();
    //   const { operations: attachmentAdjustRefOperations, mongoSessions: attachmentRefSessions } =
    //     await SpaceAttachmentSO.buildChangeAttachmentSession(
    //       user.id,
    //       space,
    //       {
    //         type: 'RESOURCE',
    //         id: this.id,
    //       },
    //       {
    //         previousAttachmentId: this.model.attachmentId,
    //         currentAttachment: file.attachment,
    //       },
    //     );
    //   operations.push(...attachmentAdjustRefOperations);
    //   mongoSessions.push(...attachmentRefSessions);
    // }
    return { operations };
  }

  static async boToCreateInput(
    user: UserSO,
    space: SpaceSO,
    nodeId: string,
    data: AINodeCreateDTO,
  ): Promise<{
    createInput: Prisma.AiNodeUncheckedCreateNestedOneWithoutNodeInput | undefined;
    operations: PrismaPromise<unknown>[];
    // mongoSessions: MongoTransactionCB[];
  }> {
    // const operations: PrismaPromise<unknown>[] = [];
    // const mongoSessions: MongoTransactionCB[] = [];
    // TODO: 判断data.fileType，转换成不同的attachment
    const { resourceType, name, description, templateId, bo } = data;
    const operations: PrismaPromise<unknown>[] = [];
    if (resourceType === 'AI') {
      // assert(name === bo.name);
      // assert(name === bo.description);
      // assert(resourceType === bo.resourceType);
      const realBO: AINodeAgentBO | undefined = bo ? AINodeAgentSchema.parse(bo) : undefined;

      const createInput: Prisma.AiNodeUncheckedCreateNestedOneWithoutNodeInput = {
        create: {
          // id: nodeId,
          spaceId: space.id,
          name,
          description,
          templateId,
          createdBy: user.id,
          updatedBy: user.id,
          bo: realBO as object,
          // attachment: {
          //   connect: {
          //     id: file.attachmentId,
          //   },
          // },
        },
      };
      if (realBO?.asMember) {
        // 加入到成员中
        const rootTeam = await space.getRootTeam();
        const { operation: memberOperation } = MemberSO.createMemberOperation({
          name: iStringParse(name),
          userId: user.id,
          spaceId: space.id,
          teamId: rootTeam.id,
          relationType: 'AI',
          relationId: nodeId,
          isOwner: false,
          isGuest: false,
        });
        operations.push(memberOperation);
      }
      return { createInput, operations };
    }
    // 现在先只支持attachment bo file type
    // if (resourceType === 'FILE' && file?.fileType === 'ATTACHMENT') {
    //   const createInput: Prisma.FileNodeUncheckedCreateNestedOneWithoutNodeInput = {
    //     create: {
    //       // id: nodeId,
    //       spaceId: space.id,
    //       name,
    //       description,
    //       templateId,
    //       createdBy: user.id,
    //       updatedBy: user.id,
    //       attachment: {
    //         connect: {
    //           id: file.attachmentId,
    //         },
    //       },
    //     },
    //   };
    //   const createAttachmentRefSession = await SpaceAttachmentSO.createSession(
    //     user.id,
    //     space,
    //     {
    //       id: file.attachment.id,
    //       size: file.attachment.size,
    //     },
    //     { type: 'RESOURCE', id: nodeId },
    //   );
    //   mongoSessions.push(createAttachmentRefSession);
    //   const attachmentAdjustRefOperation = AttachmentSO.adjustRefCountOperation(file.attachment.id, 1);
    //   operations.push(attachmentAdjustRefOperation);
    //   return { createInput, operations, mongoSessions };
    // }
    // if (file?.fileType === 'ATTACHMENT') {
    //   const attachmentId = file.attachmentId;
    //   const createInput: Prisma.FileNodeUncheckedCreateNestedOneWithoutNodeInput = {
    //     create: {
    //       // id: nodeId,
    //       spaceId: space.id,
    //       name,
    //       description,
    //       templateId,
    //       createdBy: user.id,
    //       updatedBy: user.id,
    //       attachmentId,
    //     },
    //   };
    //   return { createInput, operations, mongoSessions };
    // }
    return { createInput: undefined, operations };
  }

  static createWithNodeInput(
    user: UserSO,
    aiNode: AiNodeBO,
    params: {
      spaceId: string;
      parentId: string;
      preNodeId?: string;
      nextNodeId?: string;
      unitId?: string;
      teamId?: string;
    },
  ): { id: string; operations: PrismaPromise<AINodeModel | MemberModel>[] } {
    const id = aiNode.id || generateNanoID(CONST_PREFIX_AI_NODE);
    const userId = user.id;
    const { data: bo, success } = AINodeAgentSchema.safeParse(aiNode);
    const operations: PrismaPromise<AINodeModel | MemberModel>[] = [
      db.prisma.aiNode.create({
        data: {
          spaceId: params.spaceId,
          name: aiNode.name,
          description: aiNode.description,
          templateId: aiNode.templateId,
          createdBy: userId,
          updatedBy: userId,
          bo: success ? (bo as object) : Prisma.JsonNull,
          node: {
            create: {
              id,
              icon: aiNode.icon,
              templateId: aiNode.templateId,
              name: aiNode.name,
              description: aiNode.description,
              type: $Enums.NodeResourceType.AI,
              createdBy: userId,
              updatedBy: userId,
              spaceId: params.spaceId,
              parentId: params.parentId,
              preNodeId: params.preNodeId,
              unitId: params.unitId,
            },
          },
        },
        include: {
          node: true,
        },
      }),
    ];
    if (aiNode.asMember && params.teamId) {
      // 加入到成员中
      const { operation: memberOperation } = MemberSO.createMemberOperation({
        name: iStringParse(aiNode.name, user.locale),
        userId,
        spaceId: params.spaceId,
        teamId: params.teamId,
        relationType: 'AI',
        relationId: id,
        isOwner: false,
        isGuest: false,
      });
      operations.push(memberOperation);
    }
    return { id, operations };
  }

  async getAIModelConfig(): Promise<AIModelOptions | undefined> {
    const bo = await this.toBO();
    // const {
    //   // integrationId,
    //   model,
    //   // aiModel
    // } = bo;
    // let integrationBO: OpenAIIntegration | DeepSeekIntegration | undefined;
    // if (integrationId && model) {
    //   const integration = await IntegrationSO.init(integrationId);
    //   integrationBO = integration.getBO<OpenAIIntegration | DeepSeekIntegration>();
    // }
    // if (aiModel && typeof aiModel !== 'string') {
    //   integrationBO = {
    //     ...aiModel,
    //     name: '',
    //   };
    // }
    // if (integrationBO) {
    //   const getBaseURL = () => {
    //     if (integrationBO.baseUrl) {
    //       return integrationBO.baseUrl;
    //     }
    //     if (integrationBO.type === 'OPENAI') {
    //       return defaultOpenAIBaseURL;
    //     }
    //     return defaultDeepSeekBaseURL;
    //   };
    return {
      model: bo.model, // aiModel as PresetAIModelDef,
      // apiKey: integrationBO.apiKey,
      // baseUrl: getBaseURL(),
      // organizationId: integrationBO.organizationId,
    };
    // }
    // return undefined;
  }

  async getResourceIds(): Promise<string[]> {
    const bo = await this.toBO();
    const allResourceIds = bo.sources?.filter((i) => i.type === 'NODE')?.map((i) => i.nodeId) || [];
    const folderIds = allResourceIds.filter(
      (i) => i.startsWith(CONST_PREFIX_FOLD) || i.startsWith(CONST_PREFIX_NODTPL),
    );
    const allChildIds = await folderIds.reduce(
      async (accPromise, id) => {
        const acc = await accPromise;
        const folder = await FolderSO.init(id);
        if (folder) {
          const childNodeIds = await folder.getAllChildIds();
          return [...acc, ...childNodeIds];
        }
        return acc;
      },
      Promise.resolve([] as string[]),
    );
    return [...new Set([...allResourceIds, ...allChildIds])];
  }

  async getSkillsets(locale?: Locale): Promise<SkillsetVO[]> {
    const bo = await this.toBO();
    const user = UserSO.initWithModel({
      id: 'system',
      settings: {
        locale: locale || 'en',
      },
    } as unknown as UserModel);
    if (bo.skillsets) {
      const skillsetVos = [];
      for (const skillset of bo.skillsets) {
        const skillsetVO = await AISkillsetServerRegistry.getSkillsetVO(user, skillset, locale);
        if (skillsetVO) {
          // 如果 skillset 包含 includes 字段，则需要过滤技能集中的技能
          if ('includes' in skillset && skillset.includes && skillset.includes.length > 0) {
            const filteredSkills = skillsetVO.skills.filter((skill) => skillset.includes!.includes(skill.key));
            skillsetVos.push({
              ...skillsetVO,
              skills: filteredSkills,
            });
          } else {
            skillsetVos.push(skillsetVO);
          }
        }
      }
      return skillsetVos;
    }
    return [];
  }
}
