import classNames from 'classnames';
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import React, { useEffect } from 'react';
import { useTRPCQuery } from '@bika/api-caller/context';
import { spaceHomeTabs } from '@bika/contents/config/client/ai/launcher/tabs';
import { useLocale } from '@bika/contents/i18n';
import { SpaceLauncherDownArea } from '@bika/domains/ai/client/launcher/space-home/space-home-down-area';
import {
  AIWizardWithWelcome,
  type IAIChatWithWelcomeHandle,
} from '@bika/domains/ai/client/wizard/ai-wizard-with-welcome';
import { NodeVOMenu } from '@bika/domains/editor/client/node-vo-menu/node-vo-menu';
import { NodeHeaderTitle } from '@bika/domains/node/client/header/node-header-title-component';
import type { AIIntentParams, LauncherTabType } from '@bika/types/ai/bo';
import { useNodeResourceApiContext } from '@bika/types/node/context';
import type { SkillsetSelectBO } from '@bika/types/skill/bo';
import type { SkillsetVO, SkillVO } from '@bika/types/skill/vo';
import { useSpaceContext } from '@bika/types/space/context';
import { Button, IconButton } from '@bika/ui/button-component';
import AddOutlined from '@bika/ui/icons/components/add_outlined';
// wrench
// import WrenchOutlined from '@bika/ui/icons/components/wrench_outlined';
import CloseOutlined from '@bika/ui/icons/components/close_outlined';
import HistoryOutlined from '@bika/ui/icons/components/history_outlined';
import { Box } from '@bika/ui/layout-components';
import { BMenu } from '@bika/ui/menu/bmenu';
import { NodeIcon } from '@bika/ui/node/icon';
import { HeaderPageComponent } from '@bika/ui/web-layout';
import { SkillsSelectModal } from './skills-select/ai-skills-select-modal';
import { SkillsetIcons } from './skillset-icons';
import { type IAgentInfo, useAgentInfo } from './use-agent-info';
import type { AIChatSelector } from '../../ai/client/chat/hooks/use-ai-chat-cache';
import { useAIChatSession } from '../../ai/client/chat/hooks/use-ai-chat-cache';

interface Props {
  value: IAgentInfo;
}

// const BUTTON_STYLE = 'bg-[--bg-controls] border p-2 border-[--border-default] rounded cursor-pointer';

// 技能集合并函数 - 对相同的技能集合并其中的技能
function mergeSkillsetsWithSkills<
  T extends { kind: string; key: string; skills?: { key: string; [key: string]: unknown }[] },
>(skillsets: T[]): T[] {
  const skillsetMap = new Map<string, T>();

  skillsets.forEach((skillset) => {
    const skillsetKey = `${skillset.kind}-${skillset.key}`;
    const existingSkillset = skillsetMap.get(skillsetKey);

    if (existingSkillset && existingSkillset.skills && skillset.skills) {
      // 如果技能集已存在，合并技能列表
      const existingSkillKeys = new Set(existingSkillset.skills.map((skill) => skill.key));
      const newSkills = skillset.skills.filter((skill) => !existingSkillKeys.has(skill.key));

      skillsetMap.set(skillsetKey, {
        ...existingSkillset,
        skills: [...existingSkillset.skills, ...newSkills],
      });
    } else {
      // 如果技能集不存在，直接添加
      skillsetMap.set(skillsetKey, skillset);
    }
  });

  return Array.from(skillsetMap.values());
}

export function AINodeVORenderer(props: Props) {
  const { value } = props;
  const { key, name, permission, description, iconInfo } = useAgentInfo(value);

  const spaceContext = useSpaceContext();
  const nodeApi = useNodeResourceApiContext();
  const openSidebar = false;
  const locale = useLocale();
  const { t } = useLocale();
  const isAINode = value.type === 'node';
  const isAppBuilder = value.type === 'expert' && value.expertKey === 'builder';
  const isChiefOfStaff = value.type === 'expert' && value.expertKey === 'supervisor';

  const selector: AIChatSelector = {
    type: 'agent',
    spaceId: spaceContext!.data.id,
    agent: isAINode
      ? {
          type: 'node',
          nodeId: key,
        }
      : {
          type: 'expert',
          expertKey: value.expertKey,
        },
    // type: 'ai-agent-node',
    // nodeId: isAINode ? key : `${spaceContext?.data.id}-${key}`,
  };
  const { session, isLoading } = useAIChatSession(selector);
  const chatRef = React.useRef<IAIChatWithWelcomeHandle>(null);
  const searchParams = useSearchParams();
  const router = useRouter();

  // Skills modal state
  const [openSkillsModal, setOpenSkillsModal] = React.useState(false);
  const [customSkillsets, setCustomSkillsets] = React.useState<SkillsetSelectBO[]>(() => session.skillsets || []);

  useEffect(() => {
    setCustomSkillsets(session.skillsets || []);
  }, [session.skillsets]);

  console.log('session.skillsets', session.skillsets, customSkillsets);
  // 获取节点的BO数据来获取技能集配置
  const { data: nodeBO } = nodeApi.node.useNodeResourceBO(isAINode ? key : undefined);

  // 获取用户临时选择的技能集的VOs
  const trpcQuery = useTRPCQuery();
  const { data: customSkillsetsVOs } = trpcQuery.ai.getSkillsets.useQuery(customSkillsets);

  // 获取 AI 节点的技能集数据
  const skillsets = React.useMemo(() => {
    if (
      isAINode &&
      value.node.resource &&
      'resourceType' in value.node.resource &&
      value.node.resource.resourceType === 'AI'
    ) {
      // define: 当前节点已拥有的技能集（这里是VO格式，用于显示）
      const defineSkillsets = value.node.resource.skillsets || [];
      // custom: 用户临时选择的技能集的VOs
      const customSkillsetsArray = customSkillsetsVOs || [];
      const all = mergeSkillsetsWithSkills([...defineSkillsets, ...customSkillsetsArray]);

      console.log(all, 'all');

      return {
        define: defineSkillsets,
        custom: customSkillsetsArray,
        all, // 包含所有技能集，相同的技能集会合并技能
      };
    }
    return { define: [], custom: [], all: [] };
  }, [isAINode, value, customSkillsetsVOs]);

  // 获取当前所有技能集的合集（已有的 + 用户临时选择的），对相同技能集合并技能
  const allSkillsets = React.useMemo(() => {
    if (!isAINode || !nodeBO || nodeBO.resourceType !== 'AI') {
      return [];
    }
    // 合并当前已有的技能集和用户临时选择的技能集，对相同技能集合并技能
    const currentDefineSkillsets = nodeBO.skillsets || [];

    // 使用技能合并函数
    return mergeSkillsetsWithSkills([...currentDefineSkillsets, ...customSkillsets]);
  }, [isAINode, nodeBO, customSkillsets]);

  // 获取已拥有的所有技能ID（用于禁用），包括已有的和用户临时选择的
  const disabledSkillIds = React.useMemo(() => {
    const allSkillIds: string[] = [];
    // 从已有的技能集中收集所有技能ID
    skillsets.define.forEach((skillset: SkillsetVO) => {
      if (skillset.skills) {
        allSkillIds.push(...skillset.skills.map((skill: SkillVO) => skill.key));
      }
    });
    return allSkillIds;
  }, [skillsets.define]);

  // const initWizardId = React.useMemo(() => cacheChatId, [cacheChatId]);

  const initAIIntent: AIIntentParams = React.useMemo(() => {
    if (isAINode) {
      return {
        type: 'AI_NODE',
        nodeId: key,
        icon: isAINode ? value.node.icon : undefined,
      };
    }
    if (isAppBuilder) {
      return {
        type: 'BUILDER',
        spaceId: spaceContext ? spaceContext.data.id : undefined,
      };
    }
    if (isChiefOfStaff) {
      return {
        type: 'SUPERVISOR',
        spaceId: spaceContext ? spaceContext.data.id : undefined,
        agent: value,
      };
    }
    throw new Error(`Unsupported AI intent for expert key: ${value.expertKey}`);
  }, [key, isAINode, isAppBuilder, isChiefOfStaff, spaceContext, value]);

  const launcherTabs: LauncherTabType[] = React.useMemo(() => {
    if (isAppBuilder) return ['TEMPLATES', 'AI_REPLAYS', 'AI_HISTORY'];
    if (isChiefOfStaff) return [...spaceHomeTabs, 'AI_HISTORY'];
    return [];
  }, [isAppBuilder, isChiefOfStaff]);

  const AgentType = React.useMemo(() => {
    if (isAppBuilder) return 'AI_BUILDER';
    if (isChiefOfStaff) return 'AI_SUPERVISOR';
    if (isAINode) return 'AI_NODE';
    return 'AI_COPILOT';
  }, [isAppBuilder, isChiefOfStaff, isAINode]);

  useEffect(() => {
    // 点击对话历史中的一条记录，打开历史数据
    const chatId = searchParams.get('chatId');
    if (chatId) {
      session.setChatId(chatId);
      const newSearchParams = new URLSearchParams(searchParams.toString());
      newSearchParams.delete('chatId');
      router.replace(`${window.location.pathname}?${newSearchParams.toString()}`, { scroll: false });
      chatRef.current?.setStage('chat');
    }
  }, [searchParams, router]);

  const isChat = chatRef.current?.stage === 'chat';

  if (isLoading) {
    return <>Loading Session</>;
  }
  return (
    <>
      <HeaderPageComponent
        header={
          (isAINode || isChat) && (
            <NodeHeaderTitle
              nodeId={key}
              nodeType="AI"
              icon={iconInfo}
              // TODO i18n
              name={name || 'no title'}
              description={description}
              permission={permission?.privilege}
              button={
                <div className="flex flex-row gap-1">
                  {/* New Chat Button */}
                  <Button
                    color={'neutral'}
                    variant={'plain'}
                    disabled={!isChat}
                    onClick={() => {
                      session.clear();
                      chatRef.current?.setStage('welcome');
                    }}
                    startDecorator={<AddOutlined color="var(--text-secondary)" />}
                  >
                    {t.ai.new_chat}
                  </Button>
                  {isAINode ? (
                    <NodeVOMenu value={value.node} detail={value.node} />
                  ) : (
                    <BMenu
                      items={[
                        [
                          {
                            label: t.global.copilot.history,
                            icon: <HistoryOutlined color={'var(--text-secondary)'} />,
                            onClick: () => {
                              spaceContext?.showUIDrawer({
                                type: 'ai-history',
                                props: {
                                  type: AgentType,
                                },
                              });
                            },
                          },
                        ],
                      ]}
                    />
                  )}
                </div>
              }
            />
          )
        }
      >
        <Box
          sx={{
            position: 'relative',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'flex-start',
            width: '100%',
            height: '100%',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              backgroundImage: 'url(/assets/home/<USER>',
              backgroundSize: 'cover',
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'center',
              opacity: 0.1,
              width: '100%',
              height: '100%',
              pointerEvents: 'none',
              zIndex: 0,
            },
          }}
        >
          <div
            className={classNames('z-10 flex-1 overflow-auto h-full', {
              'pt-[20vh]': !isChat && !isAINode,
              '-mt-[16vh]': !isChat && isAINode,
              'overflow-hidden': isAINode,
            })}
          >
            <AIWizardWithWelcome
              inputState={session}
              config={{
                allowContextMenu: ['attachment'],
              }}
              ref={chatRef}
              title={
                <Box display="flex" alignItems="center" gap={1}>
                  <NodeIcon value={iconInfo} size={40} title={name} />
                  <div className="truncate">{name}</div>
                </Box>
              }
              description={description}
              forceLocale={locale.lang}
              initAIIntent={initAIIntent}
              displayMode={'VIEW'}
              skillsetIcons={
                <SkillsetIcons
                  skillsets={skillsets.all}
                  onEditClick={() => {
                    setOpenSkillsModal(true);
                  }}
                />
              }
              customBottom={
                launcherTabs.length > 0 && (
                  <Box sx={{ mt: 4, width: '100%', display: 'flex', flexDirection: 'column' }}>
                    <SpaceLauncherDownArea
                      mode={'NO_TITLE'}
                      tabs={launcherTabs}
                      wizardDto={{
                        type: AgentType,
                        nodeId: key,
                      }}
                    />
                  </Box>
                )
              }
              // chatId={typeof chatId === 'string' ? chatId : undefined}
            />
          </div>
          {openSidebar && (
            <div className="py-6 pr-6 w-[400px] h-full">
              <div className="bg-[--bg-surface]  rounded-l-lg w-full h-full relative">
                <IconButton
                  sx={{
                    position: 'absolute',
                    top: '30px',
                    right: '30px',
                  }}
                >
                  <CloseOutlined />
                </IconButton>
              </div>
            </div>
          )}
        </Box>

        {/* Skills Selection Modal */}
        {isAINode && (
          <SkillsSelectModal
            locale={locale}
            api={nodeApi}
            open={openSkillsModal}
            onClose={() => setOpenSkillsModal(false)}
            value={allSkillsets} // 当前所有技能集的合集（已有的 + 用户临时选择的）
            disabledSkillIds={disabledSkillIds} // 禁用已拥有的技能ID
            onChange={async (newSkillsets) => {
              // 需要去掉 disabledSkillIds 中的所有技能
              const filteredSkillsets = newSkillsets
                .map((skillset) => {
                  if ('includes' in skillset && skillset.includes) {
                    const filteredIncludes = skillset.includes.filter((skillId) => !disabledSkillIds.includes(skillId));
                    return {
                      ...skillset,
                      includes: filteredIncludes,
                    };
                  }
                  return skillset;
                })
                .filter((skillset) => {
                  // 如果技能集有 includes 字段但为空数组，则移除该技能集
                  if ('includes' in skillset && skillset.includes) {
                    return skillset.includes.length > 0;
                  }
                  // 没有 includes 字段的技能集保留
                  return true;
                });

              session.setSkillsets(filteredSkillsets); // 更新会话中的技能集
            }}
          />
        )}
      </HeaderPageComponent>
    </>
  );
}
