import { Stack, Switch } from '@mui/joy';
import { buildToolSDKAIAccountApi, IToolSDKAIAccountApi, PackageConfigurations } from '@toolsdk.ai/sdk-ts/react';
import React, { useEffect, useState } from 'react';
import { BIKA_PRODUCTION_URL } from 'sharelib/app-env';
import { ILocaleContext } from '@bika/contents/i18n';
import { INodeResourceApi } from '@bika/types/node/context';
import { SkillsetSelectBO } from '@bika/types/skill/bo';
import { SkillsetVO } from '@bika/types/skill/vo';
import { useGlobalContext } from '@bika/types/website/context';
import { Button } from '@bika/ui/button-component';
import { AvatarImg } from '@bika/ui/components/avatar/index';
import { useUIFrameworkContext } from '@bika/ui/framework/context';
import { Typography } from '@bika/ui/texts';

interface ToolSDKSkillsContentProps {
  locale: ILocaleContext;
  api: INodeResourceApi;
  currentSkillset: SkillsetVO;
  value?: SkillsetSelectBO;
  onValue?: (value: SkillsetSelectBO) => void;
  onChange?: () => void;
  onClose: () => void;
  disabledSkillIds?: string[]; // Array of skill IDs that should be disabled
}

export function ToolSDKSkillsContent(props: ToolSDKSkillsContentProps) {
  const { locale, api, currentSkillset, value, onValue, onChange, onClose, disabledSkillIds = [] } = props;
  const { t } = locale;

  const uiFrameworkContext = useUIFrameworkContext();
  const globalContext = useGlobalContext();

  const [toolsdkApi, setToolsdkApi] = useState<IToolSDKAIAccountApi | null>(null);
  const [selectSkills, setSelectSkills] = useState<string[]>(() => {
    if (value && 'includes' in value) {
      return value.includes || [];
    }
    return [];
  });
  const [configurationInstanceId, setConfigurationInstanceId] = useState<string>(() => {
    if (value && 'configurationInstanceId' in value) {
      return value.configurationInstanceId || '';
    }
    return '';
  });

  // 实时更新技能选择和配置到父组件
  const updateSkillsetData = (newSkills?: string[], newConfigId?: string) => {
    const skills = newSkills !== undefined ? newSkills : selectSkills;
    const configId = newConfigId !== undefined ? newConfigId : configurationInstanceId;

    if (newSkills !== undefined) {
      setSelectSkills(newSkills);
    }
    if (newConfigId !== undefined) {
      setConfigurationInstanceId(newConfigId);
    }

    if (onValue) {
      onValue({
        key: currentSkillset.key,
        includes: skills,
        kind: currentSkillset.kind,
        configurationInstanceId: configId,
      } as SkillsetSelectBO);
    }
  };

  const { data } = api.automation.getToolSDKAIAccountToken();

  useEffect(() => {
    function initToolSDKAPI() {
      if (data) {
        const baseURL =
          globalContext.servers.toolSDKAIBaseUrl ||
          (uiFrameworkContext.hostname === BIKA_PRODUCTION_URL
            ? 'https://toolsdk.ai/api/openapi'
            : 'https://dev.toolsdk.ai/api/openapi');

        const publicApi = buildToolSDKAIAccountApi({ accountToken: data, baseURL });
        setToolsdkApi(publicApi);
      }
    }
    initToolSDKAPI();
  }, [data, globalContext.servers.toolSDKAIBaseUrl, uiFrameworkContext.hostname]);

  const skills = currentSkillset.skills || [];

  return (
    <Stack sx={{ flex: 1, height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 配置和技能列表 - 滚动区域 */}
      <Stack sx={{ flex: 1, overflow: 'auto' }}>
        {toolsdkApi && (
          <Stack px={1} mb={2}>
            <PackageConfigurations
              api={toolsdkApi.configuration}
              packageKey={currentSkillset.key}
              packageVersion={currentSkillset.version as string}
              value={configurationInstanceId}
              config={currentSkillset.configuration}
              onChange={(val) => {
                updateSkillsetData(undefined, val);
              }}
            />
          </Stack>
        )}

        {skills.map((skill) => {
          const isDisabled = disabledSkillIds.includes(skill.key);

          return (
            <Stack
              key={skill.name}
              direction="row"
              alignItems="center"
              sx={{
                borderRadius: 6,
                p: 1,
                mb: 0.5,
                cursor: isDisabled ? 'not-allowed' : 'pointer',
                opacity: isDisabled ? 0.5 : 1,
                ':hover': !isDisabled
                  ? {
                      backgroundColor: 'var(--bg-controls-hover)',
                    }
                  : {},
                ':active': !isDisabled
                  ? {
                      backgroundColor: 'var(--bg-controls-active)',
                    }
                  : {},
              }}
              onClick={() => {
                if (isDisabled) return;
                const isSelected = selectSkills.includes(skill.key);
                const newSkills = isSelected
                  ? selectSkills.filter((s) => s !== skill.key)
                  : [...selectSkills, skill.key];
                updateSkillsetData(newSkills);
              }}
            >
              <Stack sx={{ mr: 1 }}>
                <AvatarImg
                  shape={'SQUARE'}
                  customSize={32}
                  name={skill.name}
                  avatar={skill.logo || { type: 'PRESET', url: '/assets/ai/skill/skill.png' }}
                />
              </Stack>
              <Stack sx={{ flex: 1 }}>
                <Typography level="b2" textColor={'var(--textCommonPrimary)'}>
                  {skill.name}
                </Typography>
                <Typography level="b4" textColor="var(--textCommonSecondary)">
                  {skill.description}
                </Typography>
              </Stack>
              <Switch
                checked={selectSkills.includes(skill.key)}
                disabled={isDisabled}
                onChange={(e) => {
                  if (isDisabled) return;
                  const newSkills = e.target.checked
                    ? [...selectSkills, skill.key]
                    : selectSkills.filter((s) => s !== skill.key);
                  updateSkillsetData(newSkills);
                }}
                color="primary"
                sx={{ ml: 2 }}
              />
            </Stack>
          );
        })}
      </Stack>

      {/* 底部按钮 - 固定区域 */}
      <Stack
        direction={'row'}
        sx={{
          alignItems: 'center',
          justifyContent: 'space-between',
          height: 48,
          p: 1,
          borderTop: '1px solid var(--border-default)',
          flexShrink: 0,
        }}
      >
        <Stack direction={'row'} spacing={1} alignItems="center">
          <Button
            variant="soft"
            color="neutral"
            onClick={() => updateSkillsetData(skills.map((skill) => skill.key))}
            disabled={selectSkills.length === skills.length}
          >
            <Typography component="span" level="b4">
              {t.action.select_all}
            </Typography>
          </Button>

          <Button
            variant="soft"
            color="neutral"
            onClick={() => updateSkillsetData([])}
            disabled={selectSkills.length === 0}
          >
            <Typography component="span" level="b4">
              {t.action.unselect_all}
            </Typography>
          </Button>
        </Stack>
        <Stack direction={'row'} spacing={1}>
          <Button sx={{ mr: 1 }} color="neutral" variant="soft" onClick={onClose}>
            <Typography component="span" level="b4">
              {t.action.cancel}
            </Typography>
          </Button>
          <Button
            disabled={!currentSkillset}
            onClick={() => {
              onChange?.();
            }}
          >
            <Typography textColor={'var(--static)'} component="span" level="b4">
              {t.action.ok}
            </Typography>
          </Button>
        </Stack>
      </Stack>
    </Stack>
  );
}
