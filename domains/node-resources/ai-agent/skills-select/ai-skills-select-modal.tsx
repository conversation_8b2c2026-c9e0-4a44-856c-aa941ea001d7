import { Modal, Sheet } from '@mui/joy';
import ModalDialog from '@mui/joy/ModalDialog';
import React, { useEffect, useState } from 'react';
import { ILocaleContext } from '@bika/contents/i18n';
import { INodeResourceApi } from '@bika/types/node/context';
import { SkillsetSelectBO } from '@bika/types/skill/bo';
import { SkillsetVO } from '@bika/types/skill/vo';
import { SkillsSelectInput } from './ai-skills-select-input';

interface SkillsSelectModalProps {
  locale: ILocaleContext;
  api: INodeResourceApi;
  open: boolean;
  onClose: () => void;
  value: SkillsetSelectBO[];
  onChange?: (value: SkillsetSelectBO[]) => void;
  defaultSelectToolset?: string;
  disabledSkillIds?: string[]; // Array of skill IDs that should be disabled
}

export function SkillsSelectModal(props: SkillsSelectModalProps) {
  const { locale, open, api, onClose, value, defaultSelectToolset } = props;

  const [skillsets, setSkillsets] = useState<SkillsetVO[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [pageNo, setPageNo] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [currentQuery, setCurrentQuery] = useState<string>('');

  const selectedSkillset = defaultSelectToolset ? value?.find((i) => i.key === defaultSelectToolset) : undefined;

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const searchSkillsets = async (query?: string, reset = true) => {
    const isNewSearch = reset || query !== currentQuery;
    if (isNewSearch) {
      setSkillsets([]);
      setPageNo(1);
      setCurrentQuery(query || '');
      setHasMore(true);
    }

    const currentPage = isNewSearch ? 1 : pageNo;
    const searchQuery = query;
    const isFirstFeaturedLoad = !query && reset;

    const ret = await api.ai.searchSkillset({
      ...(isFirstFeaturedLoad ? { category: 'featured' } : {}),
      // category: 'featured',
      selectedSkillset,
      query: searchQuery,
      pageNo: currentPage,
      pageSize: 20,
    });

    const newSkillsets = ret.data || [];

    // 去重合并
    if (isNewSearch) {
      setSkillsets(newSkillsets);
    } else {
      setSkillsets((prev) => {
        const existingKeys = new Set(prev.map((s) => s.key));
        const uniqueNew = newSkillsets.filter((s) => !existingKeys.has(s.key));
        return [...prev, ...uniqueNew];
      });
    }

    // 判断是否还有更多数据
    // 如果是第一次按分类加载，一定认为有更多数据（后续会切换到非分类搜索）
    if (isFirstFeaturedLoad) {
      setHasMore(true);
    } else {
      setHasMore(!(ret.data.length < 20));
    }

    if (!isNewSearch) {
      setPageNo((prev) => prev + 1);
    }
  };

  const loadMore = async () => {
    if (loadingMore || !hasMore) return;

    setLoadingMore(true);
    try {
      await searchSkillsets(currentQuery, false);
    } finally {
      setLoadingMore(false);
    }
  };

  useEffect(() => {
    if (open) {
      setLoading(true);
      searchSkillsets().finally(() => {
        setLoading(false);
      });
    }
    if (!open) {
      setSkillsets([]);
      setPageNo(1);
      setCurrentQuery('');
      setHasMore(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  const renderBody = () => {
    if (!loading) {
      return (
        <SkillsSelectInput
          defaultSelectToolset={props.defaultSelectToolset}
          locale={locale}
          api={api}
          skillsets={skillsets}
          value={props.value}
          onChange={props.onChange}
          onClose={onClose}
          onSearch={(query) => searchSkillsets(query, true)}
          loadingMore={loadingMore}
          hasMore={hasMore}
          onLoadMore={loadMore}
          disabledSkillIds={props.disabledSkillIds}
        />
      );
    }
    return <div style={{ textAlign: 'center', padding: '20px' }}>Loading...</div>;
  };

  return (
    <Modal open={open} onClose={onClose}>
      <ModalDialog sx={{ p: 0 }}>
        <Sheet
          sx={{
            width: 800,
            height: 500,
            borderRadius: 10,
            display: 'flex',
            bgcolor: 'var(--bg-popup)',
          }}
        >
          {renderBody()}
        </Sheet>
      </ModalDialog>
    </Modal>
  );
}
