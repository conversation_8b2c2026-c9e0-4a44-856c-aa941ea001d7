import { Box, Stack, Switch } from '@mui/joy';
import { useState } from 'react';
import { ILocaleContext } from '@bika/contents/i18n';
import { INodeResourceApi } from '@bika/types/node/context';
import { SkillsetSelectBO } from '@bika/types/skill/bo';
import { SkillsetVO } from '@bika/types/skill/vo';
import { Button } from '@bika/ui/button-component';
import { AvatarImg } from '@bika/ui/components/avatar/index';
import { FormAppInputFieldArray } from '@bika/ui/shared/types-form/formapp-field-input-array';
import { Typography } from '@bika/ui/texts';
import { Tooltip } from '@bika/ui/tooltip';

interface ToolsetSkillsContentProps {
  locale: ILocaleContext;
  api: INodeResourceApi;
  currentSkillset: SkillsetVO;
  value?: SkillsetSelectBO;
  onValue?: (value: SkillsetSelectBO) => void;
  onChange?: () => void;
  onClose: () => void;
  disabledSkillIds?: string[]; // Array of skill IDs that should be disabled
}

export function ToolsetSkillsContent(props: ToolsetSkillsContentProps) {
  const { locale, currentSkillset, value, onValue, onChange, onClose, disabledSkillIds = [] } = props;
  const { t } = locale;

  const [selectSkills, setSelectSkills] = useState<string[]>(() => {
    if (value && 'includes' in value) {
      return value.includes || [];
    }
    return [];
  });

  const defaultData = value?.kind === 'preset' ? value.configuration : undefined;
  const [configurationInputData, setConfigurationInputData] = useState<Record<string, unknown>>(defaultData || {});
  const configuration = currentSkillset.configuration;

  const [needApprovals, setNeedApprovals] = useState<string[]>(() => {
    if (value && 'needApprovals' in value) {
      return value.needApprovals || [];
    }
    return [];
  });

  // 当配置、技能或 approval 选择变化时，实时更新到父组件
  const updateSkillset = (body: {
    newSkills?: string[];
    newNeedApprovals?: string[];
    newConfiguration?: Record<string, unknown>;
  }) => {
    const { newSkills, newNeedApprovals, newConfiguration } = body;
    if (newSkills) {
      setSelectSkills(newSkills);
    }
    if (newNeedApprovals !== undefined) {
      setNeedApprovals(newNeedApprovals);
    }
    if (newConfiguration) {
      setConfigurationInputData(newConfiguration);
    }
    if (onValue) {
      onValue({
        kind: currentSkillset.kind,
        key: currentSkillset.key,
        includes: newSkills || selectSkills,
        needApprovals: newNeedApprovals || needApprovals,
        configuration: newConfiguration || configurationInputData,
      } as SkillsetSelectBO);
    }
  };

  // // 当技能选择变化时，实时更新到父组件
  // const updateSkills = (newSkills: string[], newNeedApprovals?: string[]) => {
  //   setSelectSkills(newSkills);
  //   if (newNeedApprovals !== undefined) {
  //     setNeedApprovals(newNeedApprovals);
  //   }
  //   if (onValue) {
  //     onValue({
  //       key: currentSkillset.key,
  //       includes: newSkills,
  //       needApprovals: newNeedApprovals !== undefined ? newNeedApprovals : needApprovals,
  //       kind: currentSkillset.kind,
  //     } as SkillsetSelectBO);
  //   }
  // };

  // // 专门处理 needApprovals 变化的函数
  // const updateNeedApprovals = (newNeedApprovals: string[]) => {
  //   setNeedApprovals(newNeedApprovals);
  //   if (onValue) {
  //     onValue({
  //       key: currentSkillset.key,
  //       includes: selectSkills,
  //       needApprovals: newNeedApprovals,
  //       kind: currentSkillset.kind,
  //       configuration: configurationInputData,
  //     } as SkillsetSelectBO);
  //   }
  // };

  const skills = currentSkillset.skills || [];

  return (
    <Stack sx={{ flex: 1, overflow: 'auto' }}>
      <Box sx={{ my: 2, ml: 1 }}>
        <Typography level="h7" textColor="var(--textCommonPrimary)">
          {t.editor.select_skill}
        </Typography>
        <Typography level="body-sm" textColor="var(--textCommonSecondary)">
          {currentSkillset.name}: {currentSkillset.description}
        </Typography>
      </Box>

      {configuration && (
        <Stack px={1} mb={2}>
          <FormAppInputFieldArray
            inputData={configurationInputData}
            inputFields={configuration.inputFields || []}
            onChange={(data) => updateSkillset({ newConfiguration: data })}
          />
        </Stack>
      )}

      {/* 技能列表 */}
      <Stack sx={{ flex: 1, overflow: 'auto' }}>
        {skills.map((skill) => {
          const isDisabled = disabledSkillIds.includes(skill.key);
          console.log(disabledSkillIds, skill.key, isDisabled);
          return (
            <Stack
              key={skill.name}
              direction="row"
              alignItems="center"
              sx={{
                borderRadius: 6,
                p: 1,
                mb: 0.5,
                opacity: isDisabled ? 0.5 : 1,
                // cursor: 'pointer',
                ':hover': !isDisabled
                  ? {
                      backgroundColor: 'var(--bg-controls-hover)',
                    }
                  : {},
                ':active': !isDisabled
                  ? {
                      backgroundColor: 'var(--bg-controls-active)',
                    }
                  : {},
              }}
              // onClick={() => {
              //   const isSelected = selectSkills.includes(skill.key);
              //   const newSkills = isSelected ? selectSkills.filter((s) => s !== skill.key) : [...selectSkills, skill.key];

              //   // 如果技能被取消选择，同时从 needApprovals 中移除
              //   let newNeedApprovals = needApprovals;
              //   if (isSelected && needApprovals.includes(skill.key)) {
              //     newNeedApprovals = needApprovals.filter((s) => s !== skill.key);
              //   }

              //   updateSkills(newSkills, newNeedApprovals);
              // }}
            >
              <Stack sx={{ mr: 1 }}>
                <AvatarImg
                  shape={'SQUARE'}
                  customSize={32}
                  name={skill.name}
                  avatar={skill.logo || { type: 'PRESET', url: '/assets/ai/skill/skill.png' }}
                />
              </Stack>
              <Stack sx={{ flex: 1 }}>
                <Typography level="b2" textColor={'var(--textCommonPrimary)'}>
                  {skill.name}
                </Typography>
                <Tooltip title={skill.description} sx={{ zIndex: 11111 }}>
                  <Typography
                    level="b4"
                    textColor="var(--textCommonSecondary)"
                    sx={{
                      overflow: 'hidden',
                      display: '-webkit-box',
                      WebkitLineClamp: 1,
                      WebkitBoxOrient: 'vertical',
                    }}
                  >
                    {skill.description}
                  </Typography>
                </Tooltip>
              </Stack>
              <Stack gap={1} direction="row" alignItems="center">
                {selectSkills.includes(skill.key) && (
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Stack direction="column" alignItems="center" spacing={0.5}>
                      <Switch
                        variant="soft"
                        sx={{
                          '--Switch-trackBorderColor': 'transparent',
                          // '--Switch-thumbSize': '22px',
                          '--Switch-trackWidth': '80px',
                          // '--Switch-trackHeight': '31px',
                          ':hover': {
                            '--Switch-trackBorderColor': 'transparent',
                          },
                        }}
                        size="lg"
                        checked={needApprovals.includes(skill.key)}
                        onChange={(e) => {
                          if (isDisabled) return;
                          const newNeedApprovals = e.target.checked
                            ? [...needApprovals, skill.key]
                            : needApprovals.filter((s) => s !== skill.key);
                          updateSkillset({ newNeedApprovals });
                        }}
                        color="warning"
                        disabled={isDisabled || !selectSkills.includes(skill.key)}
                        slotProps={{
                          track: {
                            children: (
                              <>
                                <Typography
                                  textColor={'var(--static)'}
                                  sx={{ position: 'relative', left: '4px' }}
                                  component="span"
                                  level="b4"
                                >
                                  {needApprovals.includes(skill.key) && 'Approval'}
                                </Typography>

                                <Typography
                                  textColor={'var(--text-primary)'}
                                  sx={{ position: 'relative', left: '-4px' }}
                                  component="span"
                                  level="b4"
                                >
                                  {!needApprovals.includes(skill.key) && 'Approval'}
                                </Typography>
                              </>
                            ),
                          },
                        }}
                      />
                    </Stack>
                  </Stack>
                )}

                <Stack direction="column" alignItems="center" spacing={0.5}>
                  <Switch
                    variant="soft"
                    sx={{
                      '--Switch-trackBorderColor': 'transparent',
                      // '--Switch-thumbSize': '22px',
                      '--Switch-trackWidth': '65px',
                      // '--Switch-trackHeight': '31px',
                      ':hover': {
                        '--Switch-trackBorderColor': 'transparent',
                      },
                    }}
                    size="lg"
                    checked={selectSkills.includes(skill.key)}
                    disabled={isDisabled}
                    onChange={(e) => {
                      if (isDisabled) return;
                      const newSkills = e.target.checked
                        ? [...selectSkills, skill.key]
                        : selectSkills.filter((s) => s !== skill.key);

                      // 如果技能被取消选择，同时从 needApprovals 中移除
                      let newNeedApprovals = needApprovals;
                      if (!e.target.checked && needApprovals.includes(skill.key)) {
                        newNeedApprovals = needApprovals.filter((s) => s !== skill.key);
                      }

                      updateSkillset({ newSkills, newNeedApprovals });
                    }}
                    color="primary"
                    slotProps={{
                      track: {
                        children: (
                          <>
                            <Typography
                              textColor={'var(--static)'}
                              sx={{ position: 'relative', left: '4px' }}
                              component="span"
                              level="b4"
                            >
                              {selectSkills.includes(skill.key) && 'Enable'}
                            </Typography>

                            <Typography
                              textColor={'var(--text-primary)'}
                              sx={{ position: 'relative', left: '-4px' }}
                              component="span"
                              level="b4"
                            >
                              {!selectSkills.includes(skill.key) && 'Enable'}
                            </Typography>
                          </>
                        ),
                      },
                    }}
                  />
                </Stack>
              </Stack>
            </Stack>
          );
        })}
      </Stack>

      {/* 底部按钮 */}
      <Stack
        direction={'row'}
        sx={{
          alignItems: 'center',
          justifyContent: 'space-between',
          height: 48,
          p: 1,
        }}
      >
        <Stack direction={'row'} spacing={1} alignItems="center">
          <Button
            variant="soft"
            color="neutral"
            onClick={() =>
              updateSkillset({
                newSkills: skills.map((skill) => skill.key),
                newNeedApprovals: [],
              })
            }
            disabled={selectSkills.length === skills.length}
          >
            <Typography component="span" level="b4">
              {t.action.select_all}
            </Typography>
          </Button>

          <Button
            variant="soft"
            color="neutral"
            onClick={() => updateSkillset({ newSkills: [], newNeedApprovals: [] })}
            disabled={selectSkills.length === 0}
          >
            <Typography component="span" level="b4">
              {t.action.unselect_all}
            </Typography>
          </Button>
        </Stack>
        <Stack direction={'row'} spacing={1}>
          <Button sx={{ mr: 1 }} color="neutral" variant="soft" onClick={onClose}>
            <Typography component="span" level="b4">
              {t.action.cancel}
            </Typography>
          </Button>
          <Button
            disabled={!currentSkillset}
            onClick={() => {
              onChange?.();
            }}
          >
            <Typography textColor={'var(--static)'} component="span" level="b4">
              {t.action.ok}
            </Typography>
          </Button>
        </Stack>
      </Stack>
    </Stack>
  );
}
