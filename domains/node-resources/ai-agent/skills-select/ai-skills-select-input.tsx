import { Box, Stack, Input, CircularProgress } from '@mui/joy';
import React, { useMemo, useState, useRef, useEffect } from 'react';
import { ILocaleContext } from '@bika/contents/i18n';
import { INodeResourceApi } from '@bika/types/node/context';
import { SkillsetSelectBO, McpServerConfigSchema } from '@bika/types/skill/bo';
import { SkillsetVO } from '@bika/types/skill/vo';
import { AvatarImg } from '@bika/ui/components/avatar/index';
import CheckOutlined from '@bika/ui/icons/components/check_outlined';
import SearchOutlined from '@bika/ui/icons/components/search_outlined';
import { snackbarShow } from '@bika/ui/snackbar';
import { Typography } from '@bika/ui/texts';
import { Tooltip } from '@bika/ui/tooltip';
import { CustomSkillsContent } from './ai-skills-custom';
import { ToolSDKSkillsContent } from './ai-skills-toolsdk';
import { ToolsetSkillsContent } from './ai-skills-toolset';

interface SkillsSelectInputProps {
  locale: ILocaleContext;
  api: INodeResourceApi;
  skillsets: SkillsetVO[];
  value?: SkillsetSelectBO[];
  onChange?: (value: SkillsetSelectBO[]) => void;
  onClose: () => void;
  onSearch: (query?: string) => void;
  defaultSelectToolset?: string;
  loadingMore?: boolean;
  hasMore?: boolean;
  onLoadMore?: () => void;
  disabledSkillIds?: string[]; // Array of skill IDs that should be disabled
}

export function SkillsSelectInput(props: SkillsSelectInputProps) {
  const {
    locale,
    api,
    skillsets,
    value,
    onChange,
    onClose,
    onSearch,
    loadingMore,
    hasMore,
    onLoadMore,
    defaultSelectToolset,
    disabledSkillIds = [],
  } = props;
  const { t } = locale;

  const [selectedSkillset, setSelectedSkillset] = useState(
    defaultSelectToolset || (value && value.length && value[0].key) || '',
  );
  const [search, setSearch] = useState<string>('');
  const [skillsSelect, setSkillsSelect] = useState<SkillsetSelectBO[]>(value || []);

  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 监听滚动事件，实现滚动到底部自动加载更多
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return undefined;

    let isThrottled = false;

    const handleScroll = () => {
      if (isThrottled) return;

      const { scrollTop, scrollHeight, clientHeight } = container;
      const isNearBottom = scrollTop + clientHeight >= scrollHeight - 10; // 10px threshold

      if (isNearBottom && hasMore && !loadingMore && onLoadMore) {
        isThrottled = true;
        onLoadMore();
        // 节流，防止重复触发
        setTimeout(() => {
          isThrottled = false;
        }, 1000);
      }
    };

    container.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [hasMore, loadingMore, onLoadMore]);

  const currentSkillset = useMemo(
    () => skillsets.find((s) => s.key === selectedSkillset),
    [selectedSkillset, skillsets],
  );

  function renderSkillsContent() {
    if (!currentSkillset) {
      return (
        <Stack
          sx={{
            height: '100%',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography level="b2" textColor="var(--textCommonSecondary)">
            {t.editor.please_select_a_skillset}
          </Typography>
        </Stack>
      );
    }

    const currentValue = skillsSelect.find((s) => s.key === currentSkillset.key);

    const commonProps = {
      locale,
      api,
      currentSkillset,
      value: currentValue,
      disabledSkillIds,
      onValue: (skillset: SkillsetSelectBO) => {
        const newSkillsSelect = skillsSelect.filter((s) => s.key !== skillset.key);
        if ('includes' in skillset) {
          if (skillset.includes?.length) {
            newSkillsSelect.push(skillset);
          }
        } else {
          newSkillsSelect.push(skillset);
        }
        setSkillsSelect(newSkillsSelect);
      },
      onChange: () => {
        // 检查是否有 toolsdk 类型的技能集缺少配置
        for (const skillset of skillsSelect) {
          const skillVO = skillsets.find((s) => s.key === skillset.key);
          if (skillVO) {
            if (
              skillVO.kind === 'toolsdk' &&
              skillset.kind === 'toolsdk' &&
              skillVO.configuration &&
              !skillset.configurationInstanceId
            ) {
              setSelectedSkillset(skillset.key);
              snackbarShow({
                content: t.editor.please_select_configuration,
                color: 'warning',
              });
              return;
            }
          }
          if (skillset.kind === 'custom' && skillset.key === 'mcp') {
            try {
              McpServerConfigSchema.parse(skillset.serverConfig);
            } catch (error) {
              snackbarShow({
                // @ts-expect-error error message access
                content: error?.message || 'error',
                color: 'danger',
              });
              setSelectedSkillset(skillset.key);
              return;
            }
          }
        }

        onChange?.(skillsSelect);
        onClose();
      },
      onClose: () => {
        onClose();
      },
    };

    switch (currentSkillset.kind) {
      case 'toolsdk':
        return <ToolSDKSkillsContent key={currentSkillset.key} {...commonProps} />;
      case 'custom':
        return <CustomSkillsContent key={currentSkillset.key} {...commonProps} />;
      // case 'automation':
      //   return <AutomationSkillsContent {...commonProps} />;
      default:
        // 默认处理普通的 toolset
        return <ToolsetSkillsContent key={currentSkillset.key} {...commonProps} />;
    }
  }

  return (
    <>
      {/* 左侧栏 */}
      <Stack
        sx={{
          width: 240,
          p: 2,
          gap: 2,
          borderRight: '1px solid var(--border-default)',
        }}
      >
        <Typography level="h7">{t.editor.skillsets}</Typography>
        <Input
          startDecorator={<SearchOutlined />}
          placeholder={t.action.search}
          size="sm"
          variant="plain"
          value={search}
          onChange={(e) => {
            setSearch(e.target.value);
            onSearch(e.target.value);
          }}
          sx={{
            bgcolor: 'transparent',
            borderBottom: '1px solid var(--border-default)',
            mb: 1,
          }}
        />
        <Stack ref={scrollContainerRef} sx={{ flex: 1, overflow: 'auto' }}>
          {skillsets.map((item) => {
            const isSelected = skillsSelect.some((s) => s.key === item.key);

            return (
              <Stack
                key={item.name}
                direction={'row'}
                onClick={() => {
                  setSelectedSkillset(item.key);
                }}
                sx={{
                  borderRadius: 6,
                  mb: 0.5,
                  p: 1,
                  bgcolor: selectedSkillset === item.key ? 'var(--bg-controls)' : 'transparent',
                  cursor: 'pointer',
                  position: 'relative',
                }}
              >
                <Stack sx={{ mr: 1 }}>
                  <AvatarImg
                    shape={'SQUARE'}
                    customSize={32}
                    name={item.name}
                    avatar={item.logo || { type: 'PRESET', url: '/assets/ai/skillset/default.png' }}
                  />
                </Stack>
                <Stack sx={{ flex: 1, overflow: 'hidden' }}>
                  <Tooltip
                    title={
                      <Box>
                        <Typography level="title-sm" textColor="var(--textCommonPrimary)">
                          {item.name}
                        </Typography>
                        <Typography level="body-sm" textColor="var(--textCommonSecondary)">
                          {item.description}
                        </Typography>
                      </Box>
                    }
                    sx={{ zIndex: 11111 }}
                    arrow
                    placement="right-end"
                  >
                    <Stack sx={{ overflow: 'hidden' }}>
                      <Typography level="b4" noWrap>
                        {item.name}
                      </Typography>

                      <Typography level="b4" textColor="var(--textCommonSecondary)" noWrap>
                        {item.description}
                      </Typography>
                    </Stack>
                  </Tooltip>
                </Stack>
                {isSelected && (
                  <Stack sx={{ ml: 1, alignItems: 'center', justifyContent: 'center' }}>
                    <CheckOutlined color="var(--brand)" size={16} />
                  </Stack>
                )}
              </Stack>
            );
          })}

          {/* 加载更多指示器 */}
          {loadingMore && (
            <Stack sx={{ alignItems: 'center', p: 2 }}>
              <CircularProgress size="sm" />
              <Typography level="body-sm" textColor="var(--textCommonSecondary)" sx={{ mt: 1 }}>
                {t.pagination.loading}
              </Typography>
            </Stack>
          )}

          {/* 没有更多数据的提示 */}
          {!hasMore && skillsets.length > 0 && (
            <Stack sx={{ alignItems: 'center', p: 2 }}>
              <Typography level="body-sm" textColor="var(--textCommonSecondary)">
                {t.pagination.no_more}
              </Typography>
            </Stack>
          )}
        </Stack>
      </Stack>

      {/* 右侧内容 */}
      <Stack
        sx={{
          flex: 1,
          p: 1,
          bgcolor: 'var(--bg-popup)',
          position: 'relative',
        }}
      >
        {renderSkillsContent()}
      </Stack>
    </>
  );
}
