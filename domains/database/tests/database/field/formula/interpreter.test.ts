import { expect, describe, test } from 'vitest';
import { FormulaContext, IBasicValue, FormulaHelper } from '@bika/domains/database/shared';
import { DatabaseFieldWithId, RecordData } from '@bika/types/database/bo';
import { evaluate, mergeContext } from './mock_state';
import { FormulaUtil } from '../../../../server/fields/formula';

async function mockContext(data: RecordData): Promise<FormulaContext> {
  const fields: DatabaseFieldWithId[] = [
    {
      id: 'a',
      name: 'a',
      type: 'NUMBER',
      property: {},
    },
    {
      id: 'b',
      name: 'b',
      type: 'SINGLE_TEXT',
      property: {},
    },
    {
      id: 'b{',
      name: 'b{',
      type: 'SINGLE_TEXT',
      property: {},
    },
    {
      id: 'c',
      name: 'c',
      type: 'NUMBER',
      property: {},
    },
    {
      id: 'd',
      name: 'd',
      type: 'NUMBER',
      property: {},
    },
  ];
  return mergeContext(data, fields);
}

type TestData = [string, RecordData, IBasicValue];

async function testEvaluate(tests: TestData[]): Promise<void> {
  for (const [expr, data, expected] of tests) {
    const ctx = await mockContext(data);
    const actual = evaluate(expr, ctx);
    expect(actual).toEqual(expected);
  }
}

describe('Formula Interpreter', async () => {
  test('mix 1 or 2 operator', async () => {
    const tests: TestData[] = [
      ['-1-1', { a: 0, b: '456', c: 1 }, -2],
      ['{a}-1 - 2', { a: 0, b: '456', c: 1 }, -3],
      ['-2 - 2 - -2', { a: 0, b: '456', c: 1 }, -2],
      ['+2 + 2 + +2', { a: 0, b: '456', c: 1 }, 6],
    ];
    await testEvaluate(tests);
  });

  test('expression escape', async () => {
    const tests: TestData[] = [
      ['“\\”” & {b} & “\\””', { a: 0, b: '456', c: 1 }, '”456”'],
      ['“\\”” & {b\\{} & “\\””', { a: 0, 'b{': 456, c: 1 }, '”456”'],
    ];
    await testEvaluate(tests);
  });

  test('expression priority', async () => {
    const tests: TestData[] = [
      [
        '1 + 2 * 3',
        {
          a: 1,
          b: '456',
          c: 2,
          d: 3,
        },
        7,
      ],
      [
        '1 + (1 + 3) * 2',
        {
          a: 1,
          b: '456',
          c: 2,
          d: 3,
        },
        9,
      ],
      [
        '2 * (2 + 3) - 10',
        {
          a: 1,
          b: '456',
          c: 2,
          d: 3,
        },
        0,
      ],
      [
        "'Courses平均成绩' & '=' & ({a}+{c}+{d}) / 3",
        {
          a: 1,
          b: '456',
          c: 2,
          d: 3,
        },
        'Courses平均成绩=2',
      ],
      ['1 + 2 + 3 + 5 - 1 * 2 * 3 / 4 % 5 * 323 % 1', { a: 0, b: '456', c: 1 }, 10.5],
      ['1 * 2 * 3 + 4 + 5', { a: 0, b: '456', c: 1 }, 15],
      ['IF(1 > 2, 3, 5)', { a: 0, b: '456', c: 1 }, 5],
      ['IF（1 > 2， 3， 5）', { a: 0, b: '456', c: 1 }, 5],
      ['1 + 2 * 3 * 4', { a: 0, b: '456', c: 1 }, 25],
      ['1 + 2 * 3 - 4 * 5 % 6', { a: 0, b: '456', c: 1 }, 5],
      ['1 + {c} * 3 - 4 * {a} % 6', { a: 5, b: '456', c: 2 }, 5],
      ['(1 + 2 * 3 - 4 * 5 % 6) & "123"', { a: 0, b: '456', c: 1 }, '5123'],
      ['1 + 2 * 3 - 4', { a: 0, b: '456', c: 1 }, 3],
      ['1 + 2 * 3 * 4 + "x"', { a: 0, b: '456', c: 1 }, '25x'],
      ['1 + 2 * 3 * 4 + “x”', { a: 0, b: '456', c: 1 }, '25x'],
      ['5 + 1 % 10 + "123"', { a: 0, b: '456', c: 1 }, '6123'],
      ['1 + 2 * 3 - 4 * 5 % 6 & "123"', { a: 0, b: '456', c: 1 }, '5123'],
    ];
    await testEvaluate(tests);
  });

  test('expression transform', () => {
    const fields: DatabaseFieldWithId[] = [
      {
        id: 'a',
        name: 'A',
        type: 'NUMBER',
        property: {},
      },
      {
        id: 'b',
        name: 'B',
        type: 'SINGLE_TEXT',
        property: {},
      },
      {
        id: 'c',
        name: 'C',
        type: 'NUMBER',
        property: {},
      },
    ];

    expect(FormulaHelper.expressionTransform('a + b + c', { fields }, 'name')).toBe('A + B + C');
    expect(FormulaHelper.expressionTransform('a + b + c', { fields }, 'id')).toBe('a + b + c');
    expect(FormulaHelper.expressionTransform('A + B + C', { fields }, 'name')).toBe('A + B + C');
    expect(FormulaHelper.expressionTransform('A + B + C', { fields }, 'id')).toBe('a + b + c');
    expect(FormulaHelper.expressionTransform('{a} + {b} + {c}', { fields }, 'id')).toBe('{a} + {b} + {c}');
    expect(FormulaHelper.expressionTransform('{a} + {b} + {c}', { fields }, 'name')).toBe('{A} + {B} + {C}');
    expect(FormulaHelper.expressionTransform('{A} + {B} + {C}', { fields }, 'id')).toBe('{a} + {b} + {c}');
    expect(FormulaHelper.expressionTransform('{A} + {B} + {C}', { fields }, 'name')).toBe('{A} + {B} + {C}');
  });

  test('parse expressionTemplate contain Token.PureValue', () => {
    const fields: DatabaseFieldWithId[] = [
      {
        templateId: 'tA',
        id: 'a',
        name: {
          en: 'A',
          'zh-CN': 'AA',
        },
        type: 'NUMBER',
        property: {},
      },
      {
        templateId: 'tB',
        id: 'b',
        name: {
          en: 'B',
          'zh-CN': 'BB',
        },
        type: 'SINGLE_TEXT',
        property: {},
      },
      {
        templateId: 'tC',
        id: 'c',
        name: {
          en: 'C',
          'zh-CN': 'CC',
        },
        type: 'NUMBER',
        property: {},
      },
    ];

    expect(FormulaUtil.parseExpressionTemplate('tA + B + tC', fields)).toBe('a + B + c');
  });

  test('parse expressionTemplate contain Token.Value', () => {
    const fields: DatabaseFieldWithId[] = [
      {
        templateId: 'tA',
        id: 'a',
        name: {
          en: 'A',
          'zh-CN': 'AA',
        },
        type: 'NUMBER',
        property: {},
      },
      {
        templateId: 'tB',
        id: 'b',
        name: {
          en: 'B',
          'zh-CN': 'BB',
        },
        type: 'SINGLE_TEXT',
        property: {},
      },
      {
        templateId: 'tC',
        id: 'c',
        name: {
          en: 'C',
          'zh-CN': 'CC',
        },
        type: 'NUMBER',
        property: {},
      },
    ];

    expect(FormulaUtil.parseExpressionTemplate('{tA} + B + {tC}', fields)).toBe('{a} + B + {c}');
  });
});

describe('Formula Interpreter Topological Sort', () => {
  test('basic', () => {
    const fields: DatabaseFieldWithId[] = [
      {
        id: 'a',
        name: 'a',
        type: 'NUMBER',
        property: {
          precision: 0,
        },
      },
      {
        id: 'b',
        name: 'b',
        type: 'FORMULA',
        property: {
          expression: 'c',
        },
      },
      {
        id: 'c',
        name: 'c',
        type: 'FORMULA',
        property: {
          expression: 'a',
        },
      },
    ];

    expect(FormulaHelper.topologicalSort(fields).map((field) => field.id)).toEqual(['a', 'c', 'b']);
  });

  test('multiple dependencies', () => {
    const fields: DatabaseFieldWithId[] = [
      {
        id: 'a',
        name: 'a',
        type: 'FORMULA',
        property: {
          expression: 'd + c + b',
        },
      },
      {
        id: 'b',
        name: 'b',
        type: 'FORMULA',
        property: {
          expression: '',
        },
      },
      {
        id: 'c',
        name: 'c',
        type: 'FORMULA',
        property: {
          expression: 'd + b',
        },
      },
      {
        id: 'd',
        name: 'd',
        type: 'FORMULA',
        property: {
          expression: 'b',
        },
      },
    ];

    expect(FormulaHelper.topologicalSort(fields).map((field) => field.id)).toEqual(['b', 'd', 'c', 'a']);
  });

  test('cyclic dependency', () => {
    const fields: DatabaseFieldWithId[] = [
      {
        id: 'a',
        name: 'a',
        type: 'FORMULA',
        property: {
          expression: 'b',
        },
      },
      {
        id: 'b',
        name: 'b',
        type: 'FORMULA',
        property: {
          expression: 'c',
        },
      },
      {
        id: 'c',
        name: 'c',
        type: 'FORMULA',
        property: {
          expression: 'a',
        },
      },
    ];

    expect(() => FormulaHelper.topologicalSort(fields)).toThrowError(
      'A cyclic dependency has been detected among these formula fields.',
    );
  });
});
