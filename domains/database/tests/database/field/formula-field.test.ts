import { describe, test, expect } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { AttachmentCellData, DefaultNumberFieldProperty } from '@bika/types/database/bo';
import { DatabaseSO } from '../../../server/database-so';

/**
 * Test Case Collection: 创建公式字段
 */
describe('Formula Field - Create Test', () => {
  /**
   * Test Case: 创建一个公式字段, 不依赖其他字段, 纯展示
   */
  test('create formula field without depend field', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const databaseNode = await rootFolder.createChildSimple(
      user,
      {
        resourceType: 'DATABASE',
        name: 'Formula Test',
      },
      {
        createDefaultRecords: true,
      },
    );
    const database = await databaseNode.toResourceSO<DatabaseSO>();
    // 创建一个公式字段
    const formulaField = await database.createField(user, {
      name: 'Formula',
      type: 'FORMULA',
      property: {
        expression: '1 + 1',
      },
    });
    // 所有行记录
    const records = await database.getRecordsAsPage();
    expect(records.length).toEqual(3);
    for (const record of records) {
      expect(record.getCellData(formulaField.id)).toEqual(2);
      expect(record.getCellValue(formulaField.id)).toEqual('2');
    }
  });

  /**
   * Test Case: 创建一个公式字段, 依赖了多语言字段的公式
   */
  test('create formula field with i18n name in expression', async () => {
    const { user, member, space } = await MockContext.initUserContext();
    const templateFolder = await space.installTemplateById(user, 'beginner-playground');
    const databaseNode = await templateFolder.findChildNodeByTemplateId('res_database_example');
    const database = await databaseNode!.toResourceSO<DatabaseSO>();
    const singleSelectField = database.getFieldByFieldKey('field_task');
    const statusField = database.getFieldByFieldKey('field_status');

    // 创建一个公式字段, 依赖上面两个字段, 使用多语言字段的名称
    const formulaField = await database.createField(user, {
      name: 'formula',
      type: 'FORMULA',
      property: {
        expression: `{${singleSelectField.id}} & " - " & {${statusField.id}}`, // 使用多语言字段名称
      },
    });

    expect(database.getFields()).toHaveLength(4);

    // 查看记录是否正确初始化
    const records = await database.getRecordsAsPage();
    expect(records.length).toEqual(10);
    expect(records[0].getCellData(formulaField.id)).toEqual('Prepare for the meeting - Pending');
    expect(records[0].getCellValue(formulaField.id)).toEqual('Prepare for the meeting - Pending');
    expect(records[1].getCellData(formulaField.id)).toEqual('Review the project plan - In Progress');
    expect(records[1].getCellValue(formulaField.id)).toEqual('Review the project plan - In Progress');

    // 创建一行新纪录, 看是否能正确同步计算出结果
    const newRecord = await database.createRecord(user, member, {
      [singleSelectField.id]: 'New Record',
      [statusField.id]: ['Started'],
    });
    expect(newRecord.getCellData(formulaField.id)).toEqual('New Record - Started');
    expect(newRecord.getCellValue(formulaField.id)).toEqual('New Record - Started');
  });

  /**
   * Test Case: 公式字段 依赖 数字字段
   */
  test('create formula field depend on number field', async () => {
    // 1. 准备上下文
    const { user, member, rootFolder } = await MockContext.initUserContext();
    // 2. 准备表格数据, 创建一个表格
    const databaseNode = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'Formula Test',
    });
    const database = await databaseNode.toResourceSO<DatabaseSO>();
    // 3. 增加一个数字字段
    const numberField = await database.createField(user, {
      name: 'Number',
      type: 'NUMBER',
      property: DefaultNumberFieldProperty, // 默认属性
    });
    // 4. 创建一行记录
    let record = await database.createRecord(user, member, {
      [numberField.id]: 123,
    });
    // 5. 增加一个公式字段, 依赖数字字段
    const formulaField = await database.createField(user, {
      name: 'Formula',
      type: 'FORMULA',
      property: {
        expression: `${numberField.id} * 2`, // 公式表达式
      },
    });
    // 6. 获取记录
    record = await database.getRecord(record.id);
    expect(record.getCellData(formulaField.id)).toEqual(246);
    expect(record.getCellValue(formulaField.id)).toEqual('246');
  });

  /**
   * Test Case: 公式字段 依赖 单选字段
   */
  test('create formula field depend on single select field', async () => {
    // 1. 准备上下文
    const { user, member, rootFolder } = await MockContext.initUserContext();
    // 2. 准备表格数据, 创建一个默认表格, 自带三行空记录
    const databaseNode = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'Formula Test',
    });
    const database = await databaseNode.toResourceSO<DatabaseSO>();
    // 3. 增加一个单选字段
    const singleSelectField = await database.createField(user, {
      name: 'Single Select',
      type: 'SINGLE_SELECT',
      property: {
        options: [
          {
            id: 'opt-1',
            name: 'Option 1',
          },
          {
            id: 'opt-2',
            name: 'Option 2',
          },
          {
            id: 'opt-3',
            name: 'Option 3',
          },
        ],
      },
    });
    // 4. 创建一行记录
    let record = await database.createRecord(user, member, {
      [singleSelectField.id]: ['opt-2'],
    });
    // 5. 增加一个公式字段, 依赖单选字段
    const formulaField = await database.createField(user, {
      name: 'Formula',
      type: 'FORMULA',
      property: {
        expression: `${singleSelectField.id} & " Selected"`, // 公式表达式
      },
    });
    // 6. 获取记录
    record = await database.getRecord(record.id);
    expect(record.getCellData(formulaField.id)).toEqual('Option 2 Selected');
    expect(record.getCellValue(formulaField.id)).toEqual('Option 2 Selected');
  });

  /**
   * Test Case: 公式字段 依赖 多选字段
   */
  test('create formula field depend on multi select field', async () => {
    // 1. 准备上下文
    const { user, member, rootFolder } = await MockContext.initUserContext();

    // 2. 准备表格数据, 创建一个默认表格, 自带三行空记录
    const databaseNode = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'Formula Test',
    });

    const database = await databaseNode.toResourceSO<DatabaseSO>();

    // 3. 增加一个有多语言的多选字段
    const i18nMultipleSelectField = await database.createField(user, {
      name: 'Multiple Select',
      type: 'MULTI_SELECT',
      property: {
        options: [
          {
            id: 'i18n-opt-1',
            name: {
              en: 'i18n Option 1',
              'zh-CN': '选项 1',
              'zh-TW': '選項 1',
              ja: 'オプション 1',
            },
          },
          {
            id: 'i18n-opt-2',
            name: {
              en: 'i18n Option 2',
              'zh-CN': '选项 2',
              'zh-TW': '選項 2',
              ja: 'オプション 2',
            },
          },
          {
            id: 'i18n-opt-3',
            name: {
              en: 'i18n Option 3',
              'zh-CN': '选项 3',
              'zh-TW': '選項 3',
              ja: 'オプション 3',
            },
          },
        ],
      },
    });

    // 增加一个没有多语言的多选字段
    const multipleSelectField = await database.createField(user, {
      name: 'Multiple Select No I18n',
      type: 'MULTI_SELECT',
      property: {
        options: [
          {
            id: 'opt-1',
            name: 'Option 1',
          },
          {
            id: 'opt-2',
            name: 'Option 2',
          },
          {
            id: 'opt-3',
            name: 'Option 3',
          },
        ],
      },
    });

    // 创建一行记录
    let record = await database.createRecord(user, member, {
      [i18nMultipleSelectField.id]: ['i18n-opt-1', 'i18n-opt-3'],
      [multipleSelectField.id]: ['opt-1', 'opt-2'],
    });

    // 增加一个公式字段, 依赖数字字段
    const formulaField = await database.createField(user, {
      name: 'Formula',
      type: 'FORMULA',
      property: {
        expression: `${multipleSelectField.id} & " - " & ${i18nMultipleSelectField.id}`,
      },
    });

    // // 获取记录
    record = await database.getRecord(record.id);
    expect(record).toBeDefined();
    expect(record.getCellData(formulaField.id)).toEqual('Option 1, Option 2 - i18n Option 1, i18n Option 3');
    expect(record.getCellValue(formulaField.id)).toEqual('Option 1, Option 2 - i18n Option 1, i18n Option 3');
  });

  /**
   * Test Case: 公式字段 依赖 附件字段
   */
  test('create formula field depend on attachment field', async () => {
    // 1. 准备上下文
    const { user, member, rootFolder } = await MockContext.initUserContext();

    // 2. 准备表格数据, 创建一个默认表格, 自带三行空记录
    const databaseNode = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'Formula Test',
    });

    const database = await databaseNode.toResourceSO<DatabaseSO>();

    // 3. 增加一个附件字段
    const attachmentField = await database.createField(user, {
      name: 'attach field',
      type: 'ATTACHMENT',
    });

    // 准备两个附件
    const attachment1 = await MockContext.createMockAttachmentPng(user);
    const attachment2 = await MockContext.createMockAttachment(user);
    const attach1 = attachment1.toVO();
    const attach2 = attachment2.toVO();
    const attachments: AttachmentCellData[] = [
      {
        id: attach1.id,
        mimeType: attach1.mimeType,
        name: 'test1',
        bucket: attach1.bucket,
        size: attach1.size,
        path: attach1.path,
      },
      {
        id: attach2.id,
        mimeType: attach2.mimeType,
        name: 'test2',
        bucket: attach2.bucket,
        size: attach2.size,
        path: attach2.path,
      },
    ];

    // 创建一行记录
    let record = await database.createRecord(user, member, {
      [attachmentField.id]: attachments,
    });

    // 增加一个公式字段, 依赖附件字段
    const formulaField = await database.createField(user, {
      name: 'Formula',
      type: 'FORMULA',
      property: {
        expression: `${attachmentField.id}`, // 公式表达式
      },
    });

    // 获取记录
    record = await database.getRecord(record.id);
    expect(record.getCellData(formulaField.id)).toEqual(['test1', 'test2']);
    expect(record.getCellValue(formulaField.id)).toEqual(['test1', 'test2']);
  });

  /**
   * Test Case: 创建一个公式字段, 依赖引用字段
   */
  test('create formula field depend on lookup field', async () => {
    // 1. 准备上下文
    const { user, member, rootFolder } = await MockContext.initUserContext();
    // 2. 准备B表
    const databaseNodeB = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'B',
      fields: [
        {
          name: 'Primary',
          type: 'SINGLE_TEXT',
        },
        {
          name: 'Number',
          type: 'NUMBER',
          property: DefaultNumberFieldProperty, // 默认属性
        },
      ],
    });
    const B = await databaseNodeB.toResourceSO<DatabaseSO>();
    const primaryFieldOfB = B.getPrimaryField();
    const numberFieldOfB = B.getFieldByFieldKey('Number');
    // 3. 准备C表
    const databaseNodeC = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'C',
      fields: [
        {
          name: 'Primary',
          type: 'SINGLE_TEXT',
        },
        {
          name: 'Number',
          type: 'NUMBER',
          property: DefaultNumberFieldProperty, // 默认属性
        },
      ],
    });
    const C = await databaseNodeC.toResourceSO<DatabaseSO>();
    const primaryFieldOfC = C.getPrimaryField();
    const numberFieldOfC = C.getFieldByFieldKey('Number');
    // 4. 准备A表
    const databaseNodeA = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'A',
      fields: [
        {
          name: 'Primary',
          type: 'SINGLE_TEXT',
        },
      ],
    });
    const A = await databaseNodeA.toResourceSO<DatabaseSO>();
    // 5. A Link B, Lookup B的Number字段(原始值引用)
    const ALinkB = await A.createField(user, {
      name: 'Link to B',
      type: 'LINK',
      property: {
        foreignDatabaseId: B.id,
      },
    });
    const ALookupBNumber = await A.createField(user, {
      name: 'Lookup B Number',
      type: 'LOOKUP',
      property: {
        relatedLinkFieldId: ALinkB.id,
        lookupTargetFieldId: numberFieldOfB.id,
      },
    });
    // 6. A Link C, Lookup C的Number字段(总和)
    const ALinkC = await A.createField(user, {
      name: 'Link to C',
      type: 'LINK',
      property: {
        foreignDatabaseId: C.id,
      },
    });
    const ALookupCNumber = await A.createField(user, {
      name: 'Lookup C Total Number',
      type: 'LOOKUP',
      property: {
        relatedLinkFieldId: ALinkC.id,
        lookupTargetFieldId: numberFieldOfC.id,
        lookUpLimit: 'ALL',
        rollUpType: 'SUM', // 聚合类型为总和
      },
    });
    // B表增加一行记录
    const b1 = await B.createRecord(user, member, {
      [primaryFieldOfB.id]: 'B1',
      [numberFieldOfB.id]: 300,
    });
    // C表增加一行记录
    const c1 = await C.createRecord(user, member, {
      [primaryFieldOfC.id]: 'C1',
      [numberFieldOfC.id]: 30,
    });
    const c2 = await C.createRecord(user, member, {
      [primaryFieldOfC.id]: 'C2',
      [numberFieldOfC.id]: 40,
    });
    // A表增加一行记录, 关联B1和C1/C2
    const a1 = await A.createRecord(user, member, {
      [ALinkB.id]: [b1.id],
      [ALinkC.id]: [c1.id, c2.id],
    });
    // 7. A表增加一个公式字段
    const formulaField = await A.createField(user, {
      name: 'Formula',
      type: 'FORMULA',
      property: {
        expression: `${ALookupBNumber.id} * ${ALookupCNumber.id}`,
      },
    });

    // 8. 检查结果
    const row1OfA = await A.getRecord(a1.id);
    const calcFormulaResult = 300 * (30 + 40);
    expect(row1OfA.getCellData(ALinkB.id)).toEqual([b1.id]);
    expect(row1OfA.getCellValue(ALinkB.id)).toEqual(['B1']);
    expect(row1OfA.getCellData(ALinkC.id)).toEqual([c1.id, c2.id]);
    expect(row1OfA.getCellValue(ALinkC.id)).toEqual(['C1', 'C2']);
    expect(row1OfA.getCellData(ALookupBNumber.id)).toEqual([300]);
    expect(row1OfA.getCellData(ALookupCNumber.id)).toEqual(70);
    expect(row1OfA.getCellData(formulaField.id)).toEqual(calcFormulaResult);
    expect(row1OfA.getCellValue(formulaField.id)).toEqual(`${calcFormulaResult}`);
  });
});

/**
 * Test Case Collection: 其他字段转换为公式字段
 */
describe('Formula Field - Convert Test', () => {
  /**
   * Test Case: 文本转换为公式字段, 造成循环引用
   * A(formula) -> B(formula)
   */
  test('convert to formula cause circular reference', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();

    // 2. 准备表格A
    const databaseNodeA = await rootFolder.createChildSimple(
      user,
      {
        resourceType: 'DATABASE',
        name: 'A',
      },
      {
        createDefaultRecords: true,
      },
    );

    const databaseA = await databaseNodeA.toResourceSO<DatabaseSO>();

    // 3. 准备表格B
    const databaseNodeB = await rootFolder.createChildSimple(
      user,
      {
        resourceType: 'DATABASE',
        name: 'B',
      },
      {
        createDefaultRecords: true,
      },
    );
    const databaseB = await databaseNodeB.toResourceSO<DatabaseSO>();

    // 4. A表增加关联B
    const linkFieldToB = await databaseA.createField(user, {
      name: 'Link to B',
      type: 'LINK',
      property: {
        foreignDatabaseId: databaseB.id,
      },
    });
    await databaseA.reloadFields();

    // 5. B表的主字段改成公式字段, 依赖双向关联字段A
    await databaseB.reloadFields();
    const primaryFieldB = databaseB.getPrimaryField();
    // B表与A表双向关联的字段(A)
    const linkFieldToA = databaseB.getFieldByFieldKey('A');
    await primaryFieldB.update(user, {
      name: 'B Formula',
      type: 'FORMULA',
      property: {
        expression: `{${linkFieldToA.id}}`,
      },
    });

    // 6. A表的主字段改成公式字段, 依赖双向关联字段B, 造成循环引用
    await databaseA.reloadFields();
    const primaryFieldA = databaseA.getPrimaryField();

    // 目前字段的依赖关系
    const directedGraph = await primaryFieldA.toDirectedGraph();
    const fieldAdjacencyTable = directedGraph.getFieldAdjacencyTable();

    // 当前字段被依赖的字段 = B表(A字段)
    const dependentFieldIds = fieldAdjacencyTable.getDependents(primaryFieldA.id);
    console.log(
      `depend graph: ${primaryFieldA.name}(Table: ${databaseA.name}) -> ${linkFieldToA.name}(Table: ${databaseB.name})`,
    );
    expect(dependentFieldIds).toEqual([linkFieldToA.id]);
    const dependentFields = fieldAdjacencyTable.getDependentFields(primaryFieldA.id);
    expect(dependentFields.length).toEqual(1);
    expect(dependentFields[0].id).toEqual(linkFieldToA.id);

    // 修改主字段成公式字段, 引用关联字段, 造成循环引用, 期望报错
    await expect(
      primaryFieldA.update(user, {
        name: 'A Formula',
        type: 'FORMULA',
        property: {
          expression: `{${linkFieldToB.id}}`,
        },
      }),
    ).rejects.toThrowError();
  });

  /**
   * Test Case: 其他字段转换为公式字段, 触发每行单元格值计算
   */
  test('convert field to formula field', async () => {
    // 1. 准备上下文
    const { user, member, rootFolder } = await MockContext.initUserContext();
    // 2. 准备表格数据, 创建一个默认表格
    const databaseNode = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'Formula Convert Test',
    });
    const database = await databaseNode.toResourceSO<DatabaseSO>();
    // 3. 增加一个单选字段
    const singleSelectField = await database.createField(user, {
      name: 'Single Select',
      type: 'SINGLE_SELECT',
      property: {
        options: [
          {
            id: 'opt-1',
            name: 'Option 1',
          },
          {
            id: 'opt-2',
            name: 'Option 2',
          },
          {
            id: 'opt-3',
            name: 'Option 3',
          },
        ],
      },
    });
    // 4. 创建3行记录
    const records = await database.createRecords(user, member, [
      {
        [singleSelectField.id]: ['opt-1'],
      },
      {
        [singleSelectField.id]: ['opt-2'],
      },
      {
        [singleSelectField.id]: ['opt-3'],
      },
    ]);
    expect(records.length).toEqual(3);
    // 5. 首字段转换为公式字段, 依赖单选字段
    const primaryField = database.getPrimaryField();
    await primaryField.update(user, {
      name: 'Formula',
      type: 'FORMULA',
      property: {
        expression: `${singleSelectField.id} & " Selected"`, // 公式表达式
      },
    });
    // 6. 获取记录
    // 第一行
    const row1 = await database.getRecord(records[0].id);
    expect(row1.getCellData(primaryField.id)).toEqual('Option 1 Selected');
    expect(row1.getCellValue(primaryField.id)).toEqual('Option 1 Selected');
    expect(row1.getCellData(singleSelectField.id)).toEqual(['opt-1']);
    expect(row1.getCellValue(singleSelectField.id)).toEqual(['Option 1']);
    // 第二行
    const row2 = await database.getRecord(records[1].id);
    expect(row2.getCellData(primaryField.id)).toEqual('Option 2 Selected');
    expect(row2.getCellValue(primaryField.id)).toEqual('Option 2 Selected');
    expect(row2.getCellData(singleSelectField.id)).toEqual(['opt-2']);
    expect(row2.getCellValue(singleSelectField.id)).toEqual(['Option 2']);
    // 第三行
    const row3 = await database.getRecord(records[2].id);
    expect(row3.getCellData(primaryField.id)).toEqual('Option 3 Selected');
    expect(row3.getCellValue(primaryField.id)).toEqual('Option 3 Selected');
    expect(row3.getCellData(singleSelectField.id)).toEqual(['opt-3']);
    expect(row3.getCellValue(singleSelectField.id)).toEqual(['Option 3']);
  });
});

/**
 * Test Case Collection: 公式字段不依赖其他字段, 但会在创建记录时自动更新, 比如使用了RECORD_ID()函数
 */
describe('Formula Field - no dependent field', () => {
  /**
   * Test Case: 创建一个公式字段, 使用RECORD_ID()函数, 不依赖其他字段
   */
  test('create formula field with RECORD_ID function', async () => {
    const { user, member, rootFolder } = await MockContext.initUserContext();
    // 创建一个表格
    const databaseNode = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'Formula Test',
      fields: [
        {
          name: 'Primary',
          type: 'SINGLE_TEXT',
        },
      ],
    });
    const database = await databaseNode.toResourceSO<DatabaseSO>();
    // 创建一个公式字段, 使用RECORD_ID()函数
    const formulaField = await database.createField(user, {
      name: 'Record ID',
      type: 'FORMULA',
      property: {
        expression: 'RECORD_ID()', // 使用RECORD_ID函数
      },
    });
    // 创建一行记录
    const primaryField = database.getPrimaryField();
    const record = await database.createRecord(user, member, {
      [primaryField.id]: '123',
    });
    // 获取记录
    const updatedRecord = await database.getRecord(record.id);
    expect(updatedRecord.getCellData(primaryField.id)).toEqual('123');
    expect(updatedRecord.getCellValue(primaryField.id)).toEqual('123');
    expect(updatedRecord.getCellData(formulaField.id)).toEqual(record.id);
    expect(updatedRecord.getCellValue(formulaField.id)).toEqual(record.id);
  });
});
