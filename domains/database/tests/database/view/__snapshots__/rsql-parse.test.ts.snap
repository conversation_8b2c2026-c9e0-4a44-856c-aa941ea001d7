// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`RSQL grammar semantic Tests > parse basic operators 1`] = `
{
  "left": {
    "left": {
      "left": {
        "left": {
          "left": {
            "left": {
              "selector": "date",
              "type": "SELECTOR",
            },
            "operator": "==",
            "right": {
              "type": "VALUE",
              "value": "2023-10-01 10:01:23",
            },
            "type": "COMPARISON",
          },
          "operator": ";",
          "right": {
            "left": {
              "selector": "date",
              "type": "SELECTOR",
            },
            "operator": "!=",
            "right": {
              "type": "VALUE",
              "value": "2023-10-02",
            },
            "type": "COMPARISON",
          },
          "type": "LOGIC",
        },
        "operator": ";",
        "right": {
          "left": {
            "selector": "date",
            "type": "SELECTOR",
          },
          "operator": ">=",
          "right": {
            "type": "VALUE",
            "value": "2023-10-03",
          },
          "type": "COMPARISON",
        },
        "type": "LOGIC",
      },
      "operator": ";",
      "right": {
        "left": {
          "selector": "date",
          "type": "SELECTOR",
        },
        "operator": "<=",
        "right": {
          "type": "VALUE",
          "value": "2023-10-04",
        },
        "type": "COMPARISON",
      },
      "type": "LOGIC",
    },
    "operator": ";",
    "right": {
      "left": {
        "selector": "date",
        "type": "SELECTOR",
      },
      "operator": ">",
      "right": {
        "type": "VALUE",
        "value": "2023-10-05",
      },
      "type": "COMPARISON",
    },
    "type": "LOGIC",
  },
  "operator": ";",
  "right": {
    "left": {
      "selector": "date",
      "type": "SELECTOR",
    },
    "operator": "<",
    "right": {
      "type": "VALUE",
      "value": "2023-10-06",
    },
    "type": "COMPARISON",
  },
  "type": "LOGIC",
}
`;

exports[`RSQL grammar semantic Tests > parse custom operators 1`] = `
{
  "left": {
    "left": {
      "left": {
        "left": {
          "selector": "field",
          "type": "SELECTOR",
        },
        "operator": "=c=",
        "right": {
          "type": "VALUE",
          "value": "value",
        },
        "type": "COMPARISON",
      },
      "operator": ";",
      "right": {
        "left": {
          "selector": "field",
          "type": "SELECTOR",
        },
        "operator": "=nc=",
        "right": {
          "type": "VALUE",
          "value": "value",
        },
        "type": "COMPARISON",
      },
      "type": "LOGIC",
    },
    "operator": ";",
    "right": {
      "left": {
        "selector": "field",
        "type": "SELECTOR",
      },
      "operator": "=c=",
      "right": {
        "type": "VALUE",
        "value": [
          "value1",
          "value2",
        ],
      },
      "type": "COMPARISON",
    },
    "type": "LOGIC",
  },
  "operator": ";",
  "right": {
    "left": {
      "selector": "field",
      "type": "SELECTOR",
    },
    "operator": "=nc=",
    "right": {
      "type": "VALUE",
      "value": [
        "value3",
        "value4",
      ],
    },
    "type": "COMPARISON",
  },
  "type": "LOGIC",
}
`;

exports[`RSQL grammar semantic Tests parse basic operators 1`] = `
{
  "left": {
    "left": {
      "left": {
        "left": {
          "left": {
            "left": {
              "selector": "date",
              "toString": [Function],
              "type": "SELECTOR",
            },
            "operator": "==",
            "right": {
              "toString": [Function],
              "type": "VALUE",
              "value": "2023-10-01 10:01:23",
            },
            "toString": [Function],
            "type": "COMPARISON",
          },
          "operator": ";",
          "right": {
            "left": {
              "selector": "date",
              "toString": [Function],
              "type": "SELECTOR",
            },
            "operator": "!=",
            "right": {
              "toString": [Function],
              "type": "VALUE",
              "value": "2023-10-02",
            },
            "toString": [Function],
            "type": "COMPARISON",
          },
          "toString": [Function],
          "type": "LOGIC",
        },
        "operator": ";",
        "right": {
          "left": {
            "selector": "date",
            "toString": [Function],
            "type": "SELECTOR",
          },
          "operator": ">=",
          "right": {
            "toString": [Function],
            "type": "VALUE",
            "value": "2023-10-03",
          },
          "toString": [Function],
          "type": "COMPARISON",
        },
        "toString": [Function],
        "type": "LOGIC",
      },
      "operator": ";",
      "right": {
        "left": {
          "selector": "date",
          "toString": [Function],
          "type": "SELECTOR",
        },
        "operator": "<=",
        "right": {
          "toString": [Function],
          "type": "VALUE",
          "value": "2023-10-04",
        },
        "toString": [Function],
        "type": "COMPARISON",
      },
      "toString": [Function],
      "type": "LOGIC",
    },
    "operator": ";",
    "right": {
      "left": {
        "selector": "date",
        "toString": [Function],
        "type": "SELECTOR",
      },
      "operator": ">",
      "right": {
        "toString": [Function],
        "type": "VALUE",
        "value": "2023-10-05",
      },
      "toString": [Function],
      "type": "COMPARISON",
    },
    "toString": [Function],
    "type": "LOGIC",
  },
  "operator": ";",
  "right": {
    "left": {
      "selector": "date",
      "toString": [Function],
      "type": "SELECTOR",
    },
    "operator": "<",
    "right": {
      "toString": [Function],
      "type": "VALUE",
      "value": "2023-10-06",
    },
    "toString": [Function],
    "type": "COMPARISON",
  },
  "toString": [Function],
  "type": "LOGIC",
}
`;

exports[`RSQL grammar semantic Tests parse custom operators 1`] = `
{
  "left": {
    "left": {
      "left": {
        "left": {
          "selector": "field",
          "toString": [Function],
          "type": "SELECTOR",
        },
        "operator": "=c=",
        "right": {
          "toString": [Function],
          "type": "VALUE",
          "value": "value",
        },
        "toString": [Function],
        "type": "COMPARISON",
      },
      "operator": ";",
      "right": {
        "left": {
          "selector": "field",
          "toString": [Function],
          "type": "SELECTOR",
        },
        "operator": "=nc=",
        "right": {
          "toString": [Function],
          "type": "VALUE",
          "value": "value",
        },
        "toString": [Function],
        "type": "COMPARISON",
      },
      "toString": [Function],
      "type": "LOGIC",
    },
    "operator": ";",
    "right": {
      "left": {
        "selector": "field",
        "toString": [Function],
        "type": "SELECTOR",
      },
      "operator": "=c=",
      "right": {
        "toString": [Function],
        "type": "VALUE",
        "value": [
          "value1",
          "value2",
        ],
      },
      "toString": [Function],
      "type": "COMPARISON",
    },
    "toString": [Function],
    "type": "LOGIC",
  },
  "operator": ";",
  "right": {
    "left": {
      "selector": "field",
      "toString": [Function],
      "type": "SELECTOR",
    },
    "operator": "=nc=",
    "right": {
      "toString": [Function],
      "type": "VALUE",
      "value": [
        "value3",
        "value4",
      ],
    },
    "toString": [Function],
    "type": "COMPARISON",
  },
  "toString": [Function],
  "type": "LOGIC",
}
`;
