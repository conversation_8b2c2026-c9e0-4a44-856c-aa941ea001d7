import { parse } from '@rsql/parser';
import { describe, expect, test } from 'vitest';

/**
 * RSQL 语法解析测试
 */
describe('RSQL grammar semantic Tests', () => {
  /**
   * 标准 RSQL 语法解析测试
   * 库里一些标准的操作符测试
   */
  test('parse basic operators', () => {
    const rsql =
      'date=="2023-10-01 10:01:23";date!=2023-10-02;date>=2023-10-03;date<=2023-10-04;date>2023-10-05;date<2023-10-06';
    // expect(() => parse(rsql)).not.toThrowError();
    const ast = parse(rsql);
    expect(ast).toMatchSnapshot();
  });
  /**
   * 自定义操作符解析测试
   * 比如前后缀都有一个=符号代表自定义符号, 比如=c=和=nc=
   */
  test('parse custom operators', () => {
    // =c= -> Contains
    // =nc= -> DoesNotContain
    const rsql = 'field=c=value;field=nc=value;field=c=(value1,value2);field=nc=(value3,value4)';
    const ast = parse(rsql);
    expect(ast).toMatchSnapshot();
    // expect(() => parse(rsql)).not.toThrowError();
    // console.log(JSON.stringify(parse(rsql), null, 2));
  });
});
