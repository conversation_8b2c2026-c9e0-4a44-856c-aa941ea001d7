import { describe, expect, test } from 'vitest';
import { AuthSO } from '@bika/domains/auth/server';
import { DatabaseField } from '@bika/types/database/bo';
import { DatabaseSO } from '../../server/database-so';
import { FormulaFieldSO } from '../../server/fields/formula-field';
import { LookupFieldSO } from '../../server/fields/lookup-field';
import { NumberFieldSO } from '../../server/fields/number-field';
import { SingleTextFieldSO } from '../../server/fields/text-field';

/**
 * Database Contains Formula Fields
 */
const name = 'Test Database Contains Formula Fields';
const basicFields: DatabaseField[] = [
  {
    templateId: 'name',
    name: 'Name',
    type: 'SINGLE_TEXT',
  },
  {
    templateId: 'age',
    name: 'Age',
    type: 'NUMBER',
    property: {},
  },
  {
    templateId: 'formula',
    name: 'Formula',
    type: 'FORMULA',
    property: {
      expression: '{Name} + {Age}',
    },
  },
];

describe('Create Database By Template', async () => {
  const { root, user } = await init();

  test('Missing field', async () => {
    const fields: DatabaseField[] = [
      {
        templateId: 'name',
        name: 'Name',
        type: 'SINGLE_TEXT',
      },
      {
        templateId: 'age',
        name: 'Age',
        type: 'NUMBER',
        property: {},
      },
      {
        templateId: 'formula',
        name: 'Formula',
        type: 'FORMULA',
        property: {
          expression: '{Name} + {Age} + {Missing}',
        },
      },
    ];

    await expect(root.createChildSimple(user, { name, resourceType: 'DATABASE', fields })).rejects.toThrow(
      'Error in expression of `Formula` field. The `{Missing}` field is not found.',
    );
  });

  test('Cyclic dependency', async () => {
    const fields: DatabaseField[] = [
      {
        templateId: 'formula',
        name: 'Formula',
        type: 'FORMULA',
        property: {
          expression: '{Formula2}',
        },
      },
      {
        templateId: 'formula2',
        name: 'Formula2',
        type: 'FORMULA',
        property: {
          expression: '{Formula}',
        },
      },
    ];

    await expect(root.createChildSimple(user, { name, resourceType: 'DATABASE', fields })).rejects.toThrow(
      'A cyclic dependency has been detected among these formula fields.',
    );
  });
});

describe('Create Field', async () => {
  const { root, user, member } = await init();

  test('Create a new Formula Field', async () => {
    const db = await (
      await root.createChildSimple(user, { name, resourceType: 'DATABASE', fields: basicFields })
    ).toResourceSO<DatabaseSO>();

    // Create record
    let record = await db.createRecord(user, member, { name: 'John', age: 20 });
    expect(record.getCellData('formula')).toBe('John20');

    // Add new formula field
    await db.createField(user, {
      templateId: 'formula2',
      name: 'Formula2',
      type: 'FORMULA',
      property: {
        expression: '{Name} + {Age}',
      },
    });

    // Get the old record
    record = await db.getRecord(record.id);
    expect(record.getCellData('formula2')).toBe('John20');
  });
});

describe('Update Field', async () => {
  const { root, user, member } = await init();

  test('Dependent field name is updated', async () => {
    const db = await (
      await root.createChildSimple(user, { name, resourceType: 'DATABASE', fields: basicFields })
    ).toResourceSO<DatabaseSO>();

    // Create record
    let record = await db.createRecord(user, member, { name: 'John', age: 20 });
    expect(record.getCellData('formula')).toBe('John20');

    // Update the name of the name field
    const nameField = db.findFieldByFieldKey('name') as SingleTextFieldSO;
    await nameField.update(user, {
      name: 'Name Updated',
      type: nameField.type,
      property: nameField.property,
    });

    // Create record again
    record = await db.createRecord(user, member, { name: 'John', age: 20 });
    expect(record.getCellData('formula')).toBe('John20');
  });
});

describe('Create Record', async () => {
  const { root, user, member } = await init();

  test('Basic', async () => {
    const db = await (
      await root.createChildSimple(user, { name, resourceType: 'DATABASE', fields: basicFields })
    ).toResourceSO<DatabaseSO>();

    const record = await db.createRecord(user, member, { name: 'John', age: 20 });
    expect(record.getCellData('formula')).toBe('John20');
  });

  test('Multiple Formula Fields with dependency', async () => {
    const fields: DatabaseField[] = [
      {
        templateId: 'name',
        name: 'Name',
        type: 'SINGLE_TEXT',
      },
      {
        templateId: 'age',
        name: 'Age',
        type: 'NUMBER',
        property: {},
      },
      {
        templateId: 'formula_before',
        name: 'Formula Before',
        type: 'FORMULA',
        property: {
          expressionTemplate: '"Formula Before: " + {formula}',
        },
      },
      {
        templateId: 'formula',
        name: 'Formula',
        type: 'FORMULA',
        property: {
          expression: '{Name} + {Age}',
        },
      },
      {
        templateId: 'formula_after',
        name: 'Formula After',
        type: 'FORMULA',
        property: {
          expressionTemplate: '"Formula After: " + {formula}',
        },
      },
    ];
    const db = await (
      await root.createChildSimple(user, { name, resourceType: 'DATABASE', fields })
    ).toResourceSO<DatabaseSO>();

    const record = await db.createRecord(user, member, { name: 'John', age: 20 });
    expect(record.getCellData('formula_before')).toBe('Formula Before: John20');
    expect(record.getCellData('formula')).toBe('John20');
    expect(record.getCellData('formula_after')).toBe('Formula After: John20');
  });

  test('Mixed Formula Fields and Lookup FIelds with dependency', async () => {
    // Init link db
    const linkDb = await (
      await root.createChildSimple(user, {
        name: 'Link DB',
        resourceType: 'DATABASE',
        fields: [
          {
            templateId: 'link_text',
            name: 'Link Text',
            type: 'SINGLE_TEXT',
          },
          {
            templateId: 'link_number',
            name: 'Link Number',
            type: 'NUMBER',
            property: {},
          },
        ],
      })
    ).toResourceSO<DatabaseSO>();
    const linkNumberField = linkDb.getFieldByFieldKey('Link Number') as NumberFieldSO;

    // Create records in link db
    const linkRecord1 = await linkDb.createRecord(user, member, { link_text: 'link A', link_number: 20 });
    const linkRecord2 = await linkDb.createRecord(user, member, { link_text: 'Link B', link_number: 30 });

    // Init main db
    const mainDb = await (
      await root.createChildSimple(user, {
        name: 'Main DB',
        resourceType: 'DATABASE',
        fields: [
          {
            templateId: 'name',
            name: 'Name',
            type: 'SINGLE_TEXT',
          },
          {
            templateId: 'formula',
            name: 'Formula',
            type: 'FORMULA',
            property: {
              expression: '0',
              // 在下方重新引用
              // expressionTemplate: "{link} + ' ' + {lookup}",
            },
          },
        ],
      })
    ).toResourceSO<DatabaseSO>();

    const nameField = mainDb.getFieldByFieldKey('Name') as SingleTextFieldSO;
    let formulaField = mainDb.getFieldByFieldKey('Formula') as FormulaFieldSO;
    // 创建一个关联字段
    const linkField = await mainDb.createField(user, {
      templateId: 'link',
      name: 'Link',
      type: 'LINK',
      property: {
        foreignDatabaseId: linkDb.id,
      },
    });

    // 创建引用字段
    const lookupField = (await mainDb.createField(user, {
      templateId: 'lookup',
      name: 'Lookup',
      type: 'LOOKUP',
      property: {
        relatedLinkFieldId: linkField.id,
        lookupTargetFieldId: linkNumberField.id,
      },
    })) as LookupFieldSO;

    // 更新公式, 引用 link 和 lookup 字段
    formulaField = (await formulaField.update(user, {
      name: formulaField.name,
      type: formulaField.type,
      property: {
        expression: `{${linkField.id}} & ' ' & {${lookupField.id}}`,
      },
    })) as FormulaFieldSO;
    await mainDb.reloadFields();

    // Create records
    // Record 1
    let record1 = await mainDb.createRecord(user, member, { name: 'A' });
    // Record 2
    let record2 = await mainDb.createRecord(user, member, { name: 'B' });
    // 批量更新
    await mainDb.updateRecords(user, [
      {
        recordId: record1.id,
        cells: {
          [linkField.id]: [linkRecord1.id],
        },
      },
      {
        recordId: record2.id,
        cells: {
          [linkField.id]: [linkRecord1.id, linkRecord2.id],
        },
      },
    ]);
    await mainDb.reloadFields();

    record1 = await mainDb.getRecord(record1.id);
    const cells1 = (await record1.toRenderVO()).cells;
    expect(cells1[nameField.id].data).toBe('A');
    expect(cells1[nameField.id].value).toBe('A');
    expect(cells1[formulaField.id].data).toBe('link A 20');
    expect(cells1[formulaField.id].value).toBe('link A 20');
    expect(cells1[lookupField.id].data).toEqual([20]);
    expect(cells1[lookupField.id].value).toEqual(['20']);
    expect(cells1[linkField.id].data).toEqual([linkRecord1.id]);
    expect(cells1[linkField.id].value).toEqual(['link A']);

    record2 = await mainDb.getRecord(record2.id);
    const cells2 = (await record2.toRenderVO()).cells;
    expect(cells2[nameField.id].data).toBe('B');
    expect(cells2[nameField.id].value).toBe('B');
    // 有点疑问
    expect(cells2[formulaField.id].data).toBe('link A, Link B 20, 30');
    expect(cells2[formulaField.id].value).toBe('link A, Link B 20, 30');
    expect(cells2[lookupField.id].data).toEqual([20, 30]);
    expect(cells2[lookupField.id].value).toEqual(['20', '30']);
    expect(cells2[linkField.id].data).toEqual([linkRecord1.id, linkRecord2.id]);
    expect(cells2[linkField.id].value).toEqual(['link A', 'Link B']);
  });
});

describe('Update Record', async () => {
  const { root, user, member } = await init();

  test('Basic', async () => {
    const db = await (
      await root.createChildSimple(user, { name, resourceType: 'DATABASE', fields: basicFields })
    ).toResourceSO<DatabaseSO>();

    // Create record
    let record = await db.createRecord(user, member, { name: 'John', age: 20 });
    expect(record.getCellData('formula')).toBe('John20');

    // Update record
    await db.updateRecords(user, [
      {
        recordId: record.id,
        cells: {
          name: record.getCellData('name') ?? null,
          age: 30,
        },
      },
    ]);
    await db.reloadFields();
    record = await db.getRecord(record.id);
    expect(record.getCellData('formula')).toBe('John30');
  });
});

describe('Update Record Cell', async () => {
  const { root, user, member } = await init();

  test('Basic', async () => {
    const db = await (
      await root.createChildSimple(user, { name, resourceType: 'DATABASE', fields: basicFields })
    ).toResourceSO<DatabaseSO>();

    // Create record
    let record = await db.createRecord(user, member, { name: 'John', age: 20 });
    expect(record.getCellData('formula')).toBe('John20');

    // Update cell
    await db.updateRecords(user, [
      {
        recordId: record.id,
        cells: {
          age: 30,
        },
      },
    ]);
    await db.reloadFields();
    record = await db.getRecord(record.id);
    expect(record.getCellData('formula')).toBe('John30');
  });
});

async function init() {
  const { user } = await AuthSO.quickLogin();
  const space = await user.createSpace({ name: user.id });

  expect(user).toBeDefined();
  expect(space).toBeDefined();

  const root = await space.getRootFolder();
  const member = await user.getMember(space.id);
  return { root, user, member };
}
