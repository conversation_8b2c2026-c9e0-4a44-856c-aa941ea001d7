import { Draggable } from '@hello-pangea/dnd';
import classNames from 'classnames';
import React from 'react';
import { AttachmentVO } from '@bika/types/attachment/vo';
import DragOutlined from '@bika/ui/icons/components/drag_outlined';
import { directDownload } from '@bika/ui/preview-attachment/attachment-renderer';
import styles from './attachment.module.css';
import { FileOperate } from './file-operate';
import { AttachmentVOExtends, UploadStatus } from './interface';
import { useDraggableInPortal } from './use-draggableIn-portal';
import { getAttachmentDisplayPath } from './utils/get-attachment-display-path';

export const AttachmentItem: React.FC<
  AttachmentVOExtends & {
    index: number;
    setSaveFiles: React.Dispatch<React.SetStateAction<AttachmentVO[]>>;
    disabled?: boolean;
    handlePreviewAttachment: (index: number) => void;
  }
> = (props) => {
  const { id, index, bucket, name, setSaveFiles, path: token, disabled, handlePreviewAttachment } = props;
  const handleDeleteItem = () => {
    setSaveFiles((pre) => [...pre.slice(0, index), ...pre.slice(index + 1)]);
  };

  const renderDraggable = useDraggableInPortal();

  const downloadAttachment = async () => {
    if (!props.links?.downloadUrl && !props.originFile) {
      console.log('no downloadUrl');
      return;
    }

    const downloadUrl = props.links?.downloadUrl || URL.createObjectURL(props.originFile!);
    await directDownload(downloadUrl, name || '');
  };

  return (
    <Draggable key={id} draggableId={id} index={index}>
      {renderDraggable((provided: any, snapshot: any) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          style={{ ...provided.draggableProps.style }}
          className={classNames(
            'flex items-center p-2 rounded bg-[--bg-controls] cursor-pointer h-[40px]',
            styles.attachmentBox,
          )}
          onClick={() => handlePreviewAttachment(index)}
        >
          {!disabled && (
            <div className={'cursor-pointer mr-2'} {...provided.dragHandleProps}>
              <DragOutlined />
            </div>
          )}
          <div>
            <img
              src={getAttachmentDisplayPath(props)}
              loading="lazy"
              alt={name}
              className={'w-[24px] h-[24px] object-cover flex-shrink-0'}
            />
          </div>
          <p className={'mr-2 ml-2 line-clamp-1 flex-1 text-b2 !select-none'}>{name}</p>
          {
            <FileOperate
              uploadStatus={{ status: UploadStatus.SUCCESS }}
              handleDeleteItem={handleDeleteItem}
              disabled={disabled}
              downloadAttachment={downloadAttachment}
            />
          }
        </div>
      ))}
    </Draggable>
  );
};
