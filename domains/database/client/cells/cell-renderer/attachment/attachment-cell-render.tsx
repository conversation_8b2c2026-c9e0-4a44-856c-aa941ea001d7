import classNames from 'classnames';
import type React from 'react';
import { useLocale } from '@bika/contents/i18n';
import type { AttachmentVO } from '@bika/types/attachment/vo';
import type { CustomCellRendererProps } from '@bika/ui/database/types';
import { FileType, isWhatFileType, renderFileIconUrl } from '@bika/ui/file';
import { Box } from '@bika/ui/layouts';
import { triggerPreviewAttachment } from '@bika/ui/preview-attachment/preivew-attachment';
import { Tooltip } from '@bika/ui/tooltip';

interface AttachmentCellRenderProps
  extends Pick<CustomCellRendererProps<any, AttachmentVO[]>, 'value' | 'data' | 'colDef'> {
  editable?: boolean;
  className?: string;
  listContainerClassName?: string;
}

// TODO 移除 coldef 的依赖, 从 value 中获取
export const AttachmentCellRender: React.FC<AttachmentCellRenderProps> = ({
  // valueFormatted,
  value: _values,
  className,
  listContainerClassName,
  data,
  colDef,
  editable = true,
}) => {
  const { t } = useLocale();

  const handlePreviewAttachment = (index: number) => {
    // Map savedAttachments to kkfile format
    const attachmentsForPreview = (_values ?? []).map((attachment) => {
      const links = (attachment as any).links;
      return {
        name: attachment.name || 'attachment',
        contentType: attachment.mimeType,
        url: links?.previewUrl || links?.downloadUrl || '',
        variant: 'kkfile' as const,
      };
    });

    triggerPreviewAttachment({ index, attachments: attachmentsForPreview, t });
  };

  return (
    <Box
      maxWidth={'800px'}
      width={'100%'}
      display={'flex'}
      className={classNames(className, 'overflow-hidden')}
      sx={{
        verticalAlign: 'middle',
        paddingLeft: 'var(--bika-ag-cell-padding-horizontal)',
        paddingRight: 'var(--bika-ag-cell-padding-horizontal)',
      }}
    >
      <div
        style={{
          paddingTop: 'var(--bika-ag-cell-padding-top)',
          paddingBottom: 'var(--bika-ag-cell-padding-bottom)',
        }}
        className={classNames(
          listContainerClassName,
          'flex space-x-1 items-center  overflow-x-scroll pb-3 flex flex-wrap gap-y-[4px]',
        )}
      >
        {Array.isArray(_values) &&
          _values.map((v, index) => {
            const attach = (_values as AttachmentVO[])[index];
            const fileType = isWhatFileType({ name: attach.name, type: attach.mimeType });

            const links = (v as any).links;
            return (
              <div
                key={index}
                className={'relative h-[22px] w-[22px] flex-shrink-0 cursor-pointer'}
                onClick={() => handlePreviewAttachment(index)}
              >
                <Tooltip title={attach.name} placement="top">
                  <img
                    alt=""
                    // fill={true}
                    loading="lazy"
                    src={
                      fileType === FileType.Image
                        ? links?.thumbnailUrl
                        : renderFileIconUrl({ name: attach.name, type: attach.mimeType })
                    }
                    // objectFit="cover"
                    className={'border-[1px] border-[--border-default] h-[22px] w-[22px]'}
                  />
                </Tooltip>
              </div>
            );
          })}
      </div>
    </Box>
  );
};
