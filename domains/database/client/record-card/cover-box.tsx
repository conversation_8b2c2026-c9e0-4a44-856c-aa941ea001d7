import Image from 'next/image';
import { useState } from 'react';
import { useLocale } from '@bika/contents/i18n';
import type { AttachmentCellData } from '@bika/types/database/bo';
import type { AttachmentCellValue } from '@bika/types/database/vo';
import { FileType, isWhatFileType, renderFileIconUrl } from '@bika/ui/file';
import { Box, Stack } from '@bika/ui/layouts';
import { triggerPreviewAttachment } from '@bika/ui/preview-attachment/preivew-attachment';

interface CoverBoxProps {
  data: AttachmentCellData[];
  value: AttachmentCellValue[];
  stretch: 'cover' | 'contain';
}

export const CoverBox = (props: CoverBoxProps) => {
  const { data, value, stretch } = props;

  const [activeIndex, setActiveIndex] = useState(0);
  const { t } = useLocale();

  const attach = data[activeIndex];
  const links = value[activeIndex];
  const fileType = isWhatFileType({ name: attach.name, type: attach.mimeType });
  const src = (
    fileType === FileType.Image ? links?.previewUrl : renderFileIconUrl({ name: attach.name, type: attach.mimeType })
  ) as string;
  const dotWidth = data.length * 16 - 8;

  const handlePreviewAttachment = (index: number) => {
    // Map savedAttachments to kkfile format
    const attachmentsForPreview = data.map((attachment, idx) => {
      const links = value[idx];
      return {
        name: attachment.name || 'attachment',
        contentType: attachment.mimeType,
        url: links?.previewUrl || links?.downloadUrl || '',
        variant: 'kkfile' as const,
      };
    });

    triggerPreviewAttachment({
      index,
      attachments: attachmentsForPreview,
      t,
    });
  };

  return (
    <Box sx={{ width: '100%', height: '100%' }}>
      <Box
        sx={{ width: '100%', height: '100%', position: 'relative' }}
        onClick={(e) => {
          e.stopPropagation();
          handlePreviewAttachment(activeIndex);
        }}
      >
        <Image alt="cover" fill={true} src={src} objectFit={stretch} />
      </Box>
      <Stack
        direction="row"
        spacing={1}
        width={dotWidth}
        height={16}
        sx={{
          margin: '-24px auto 0',
        }}
      >
        {data.map((item, index) => (
          <Box
            key={item.id}
            width={8}
            height={8}
            bgcolor={index === activeIndex ? 'var(--brand)' : 'var(--bg-surface)'}
            borderRadius="4px"
            border="1px solid var(--brand)"
            sx={{
              opacity: 0.8,
            }}
            onMouseOver={() => setActiveIndex(index)}
          />
        ))}
      </Stack>
    </Box>
  );
};
