import { CellRange, ColDef } from '@ag-grid-community/core';
import { produce } from 'immer';
import type { <PERSON>V<PERSON><PERSON>, ViewFilter, ViewGroupArray, ViewSortArray } from '@bika/types/database/bo';
import { MemberCellValueSchema, type RecordRenderVO, type ViewFieldVO } from '@bika/types/database/vo';
import type { GridApi, IRowNode, SuppressKeyboardEventParams } from '@bika/ui/database/types';
import { ensureArray } from '@bika/ui/utils';
import { LINEBREAK_PLACEHOLDER } from '../../../shared/constant';
import { AttachmentVOExtends } from '../../cells/cell-editor/attachment/interface';
import { ILookupVOWithRecId } from '../../cells/cell-editor/types';

export type RecordRenderVORow = RecordRenderVO & {
  index: number;
};

export const getTargetRows = (
  checkedRows: IRowNode<RecordRenderVORow>[],
  currentRowIndex?: number, // start form 1
  cellRange?: {
    start?: number;
    end?: number;
  },
): number[] => {
  const checkedIndex = checkedRows.map((r) => r.rowIndex) as number[];

  if (!currentRowIndex) {
    return checkedIndex;
  }

  if (cellRange?.start == null || cellRange?.end == null) {
    return [currentRowIndex - 1];
  }
  let _start = cellRange.start;
  let _end = cellRange.end;

  if (_start > _end) {
    [_start, _end] = [_end, _start];
  }
  if (checkedIndex.includes(currentRowIndex - 1)) {
    return checkedIndex;
  }

  const array = Array.from({ length: _end - _start + 1 }, (_, i) => _start + i);
  return array;
};

export function convertToString(value: string | string[]): string {
  if (Array.isArray(value)) {
    return value.join('');
  }
  return value;
}

// 外部函数
export const checkedExistedRowGroup = (rowGroup: ViewGroupArray, columns: ViewFieldVO[]) => {
  const existsRowGroupId = columns.map((_col) => _col.id);
  return rowGroup.filter((_rowGroup) => existsRowGroupId.includes(_rowGroup.fieldId ?? '')) as ViewGroupArray;
};

// 外部函数
export const checkedExistedFilter = (filter: ViewFilter, columns: ViewFieldVO[]): ViewFilter =>
  produce<ViewFilter>(filter, (draft) => {
    draft.conds = filter.conds?.filter((condition) => {
      const existsRowGroup = columns.find((_col) => _col.id === condition.fieldId);
      if (!existsRowGroup) return false;
      const isSameType = condition.fieldType === existsRowGroup.type;
      return isSameType;
    });
  });

// 外部函数
export const checkedExistedSortArray = (filter: ViewSortArray, columns: ViewFieldVO[]) => {
  const existsRowGroupId = columns.map((_col) => _col.id);
  return filter.filter((_item) => existsRowGroupId.includes(_item.fieldId ?? '')) as ViewSortArray;
};

export const getTargetRowIds = (
  api: GridApi<RecordRenderVO>,
  checkedRows: IRowNode<RecordRenderVORow>[],
  currentRowIndex?: number, // start form 1
  cellRange?: {
    start?: number;
    end?: number;
  },
): string[] => {
  if (!currentRowIndex) {
    return checkedRows.map((r) => r.id ?? '').filter(Boolean) as string[];
  }

  const targetRows = getTargetRows(checkedRows, currentRowIndex, cellRange);

  const recordIds = targetRows
    .map((r) => {
      const row = api?.getDisplayedRowAtIndex(r);
      if (row?.group) {
        return null;
      }
      return api?.getDisplayedRowAtIndex(r)?.data?.id;
    })
    .filter(Boolean) as string[];

  return recordIds;
};

/**
 * 处理单元格的删除按键事件
 */
const handleClearKeyEvent = (_params: SuppressKeyboardEventParams<RecordRenderVO>): boolean => {
  if (_params.api.getEditingCells().length > 0) {
    return false;
  }
  console.log('ignore clear key event');
  return true;
};

/**
 * 默认的键盘事件处理函数
 */
export const defaultSuppressKeyboardEvent = (params: SuppressKeyboardEventParams<RecordRenderVO>) => {
  const { key } = params.event;
  if (key === 'Backspace' || key === 'Delete') {
    // 如果按下了'Backspace'或'Delete', 并且选了单元格, 则不让网格处理
    return handleClearKeyEvent(params);
  }
  return false;
};

/**
 * Handles keyboard events for number cells
 * Automatically starts editing when a number key is pressed
 */
export const suppressRattingKeyboardEvent = (params: SuppressKeyboardEventParams<RecordRenderVO>) => {
  const { key } = params.event;
  if (params.api.getEditingCells().length > 0) {
    return false;
  }
  if (key === 'Backspace' || key === 'Delete') {
    // 如果按下了'Backspace'或'Delete', 并且选了单元格, 则不让网格处理
    return handleClearKeyEvent(params);
  }
  const isNumber = key >= '0' && key <= '9'; // Check for number keys
  if (isNumber) {
    if (params.node?.rowIndex == null) {
      return false;
    }
    const cellDefs = params.api.getEditingCells();
    if (cellDefs.length === 0) {
      params.api.startEditingCell({
        rowIndex: params.node.rowIndex ?? 0,
        colKey: params.column.getColId(),
        key,
      });
      return true; // Suppress default behavior
    }
  }
  return false;
};

/**
 * Handles keyboard events for number cells
 * Automatically starts editing when a number key is pressed
 */
export const suppressNumberCellKeyboardEvent = (params: SuppressKeyboardEventParams<RecordRenderVO>) => {
  // console.log('suppressNumberCellKeyboardEvent params', params);
  const { key } = params.event;
  if (key === 'Backspace' || key === 'Delete') {
    // 如果按下了'Backspace'或'Delete', 并且选了单元格, 则不让网格处理
    return handleClearKeyEvent(params);
  }
  // console.log('suppressNumberCellKeyboardEvent key', key);
  const isNumber = key >= '0' && key <= '9'; // Check for number keys
  if (isNumber) {
    if (params.node?.rowIndex == null) {
      return false;
    }
    const gridApi = params.api;
    const cellPosition = gridApi.getEditingCells();
    if (cellPosition.length === 0) {
      gridApi.startEditingCell({
        rowIndex: params.node.rowIndex ?? 0,
        colKey: params.column.getColId(),
        key: 'Backspace',
      });
      return true; // Suppress default behavior
    }
  }
  return false;
};

/**
 * Handles keyboard events for text cells
 * Automatically starts editing when an alphanumeric key is pressed
 * @param suppressEnterKey - true = 是否不支持回车键
 * @param onClearSelectedCells - 回调函数, 当按下'Backspace'或'Delete'时,并且选了单元格,用于清除选中的单元格
 */
export const getTextCellKeyboardEvent =
  (suppressEnterKey: boolean) => (params: SuppressKeyboardEventParams<RecordRenderVO>) => {
    const { key, ctrlKey, metaKey, shiftKey } = params.event;
    if (params.api.getEditingCells().length > 0) {
      if (key === 'Enter' && !shiftKey) {
        return suppressEnterKey;
      }
      return false;
    }

    if (key === 'Enter' && !shiftKey) {
      return suppressEnterKey;
    }

    if (key === 'Backspace' || key === 'Delete') {
      // 如果按下了'Backspace'或'Delete', 并且选了单元格, 则不让网格处理
      return handleClearKeyEvent(params);
    }

    // 检查是否字母数字按键 (a-z, A-Z, 0-9)
    const isAlphanumeric = /^[a-zA-Z0-9]$/.test(key);

    // console.log('suppressTextCellKeyboardEvent isAlphanumeric', isAlphanumeric);
    // 如果是复制粘贴等操作
    if (isAlphanumeric && !ctrlKey && !metaKey) {
      if (params.node?.rowIndex == null) {
        return false;
      }

      const cellDefs = params.api.getEditingCells();
      // console.log('suppressTextCellKeyboardEvent cellDefs', cellDefs);
      if (cellDefs.length === 0) {
        // console.log('suppressTextCellKeyboardEvent params', params);
        params.api.startEditingCell({
          rowIndex: params.node.rowIndex,
          colKey: params.column.getColId(),
          key,
        });
        return true; // Suppress default behavior
      }
    }
    return false;
  };

// 重要: 选中范围的开始和结束行索引, 分两种情况, 容易导致所选行数变负数
// 1. 如果从下往上选中, 则 selectedStartRowIndex > selectedEndRowIndex
// 2. 如果从上往下选中, 则 selectedStartRowIndex < selectedEndRowIndex
export const calculatedCellRange = (
  selectedStartRowIndex: number,
  selectedEndRowIndex: number,
): { startRowIndex: number; endRowIndex: number } => {
  if (selectedStartRowIndex > selectedEndRowIndex) {
    return { startRowIndex: selectedEndRowIndex, endRowIndex: selectedStartRowIndex };
  }
  return { startRowIndex: selectedStartRowIndex, endRowIndex: selectedEndRowIndex };
};

export const getCellRangeIndex = (cellRange: CellRange): { startRowIndex: number; endRowIndex: number } => {
  const selectedStartRowIndex = cellRange.startRow?.rowIndex;
  const selectedEndRowIndex = cellRange.endRow?.rowIndex;
  console.log(`Selected cell range rows, start index: ${selectedStartRowIndex} - end index: ${selectedEndRowIndex}`);
  if (selectedStartRowIndex === undefined || selectedEndRowIndex === undefined) {
    return { startRowIndex: 0, endRowIndex: 0 };
  }
  return calculatedCellRange(selectedStartRowIndex, selectedEndRowIndex);
};

/**
 * 粘贴数据转换成对应列的规范数据
 * @param value 粘贴板字符串
 * @param colDef 列定义
 * @returns 单元格值
 */
export const convertClipboardDataToCellValue = (value: string, colDef: ColDef): CellValue => {
  // 处理空值
  if (value === '') return null;

  // 根据列定义进行类型转换
  if (colDef.type === 'SINGLE_SELECT') {
    const strArray = value.split(',').map((v) => v.trim());
    const array = ensureArray(strArray);
    return array.length > 1 ? array.slice(0, 1) : array;
  }
  if (colDef.type === 'MULTI_SELECT') {
    const strArray = value.split(',').map((v) => v.trim());
    return ensureArray(strArray);
  }
  if (colDef.type === 'SINGLE_TEXT') {
    // Apply the same logic as valueParser for SINGLE_TEXT
    if (typeof value === 'string') {
      return value.replaceAll(LINEBREAK_PLACEHOLDER, '\n');
    }
    return value;
  }
  if (colDef.type === 'LONG_TEXT') {
    // Apply the same logic as valueParser for LONG_TEXT: convert LINEBREAK_PLACEHOLDER back to \n
    if (typeof value === 'string') {
      return value.replaceAll(LINEBREAK_PLACEHOLDER, '\n');
    }
    return value;
  }
  if (colDef.type === 'ATTACHMENT') {
    // 去掉里面的links
    const result: AttachmentVOExtends[] = JSON.parse(value);
    const attachments = result.map((item) => {
      const { links, ...rest } = item;
      return rest;
    });
    return attachments;
  }
  if (colDef.type === 'MEMBER') {
    const result = MemberCellValueSchema.array().safeParse(JSON.parse(value));
    if (result.success) {
      return result.data.map((r) => r.id);
    }
    return [];
  }
  if (colDef.type === 'LINK' || colDef.type === 'ONE_WAY_LINK') {
    if (!value) {
      return null;
    }
    if (Array.isArray(value) && value.length === 0) {
      return [];
    }
    try {
      // setting value from cell input lookup
      const newLookupValue: ILookupVOWithRecId = JSON.parse(value);
      return newLookupValue.dataList?.map((item) => item.value);
    } catch (_e) {
      return [];
    }
  }
  return value;
};
