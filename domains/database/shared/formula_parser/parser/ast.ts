/* eslint-disable max-classes-per-file */
import { iStringMatch } from 'basenext/i18n/i-string';
import { BasicValueType, DatabaseFieldWithId } from '@bika/types/database/bo';
import { FieldTypeHelper } from '../../fields/field-type-helper/factory';
import { formatErrorMessage } from '../errors';
import { Token, TokenType } from '../lexer';

// reserved keyword: values are used to rollup the lookup field
const ROLLUP_KEY_WORDS = 'values';

export enum AstNodeType {
  /**
    BinaryOperatorNode（二元运算符节点）
    作用：表示需要两个操作数的运算，例如加减乘除、逻辑比较等。
    示例代码：a + b 或 x > 5
    AST结构：
      1. 包含 operator 属性（如 +, -, &&）。
      2. 两个子节点：left（左操作数）和 right（右操作数）。
    典型场景：解析数学表达式或逻辑表达式时生成。
   */
  BinaryOperatorNode = 'BinaryOperatorNode',
  /**
    UnaryOperatorNode（一元运算符节点）
    作用：表示仅需一个操作数的运算，例如取负（-a）、逻辑非（!flag）。
    示例代码：-3 或 !isValid
    AST结构：
      1. 包含 operator 属性（如 -, !）。
      2. 一个子节点：argument（被操作的对象）。
    典型场景：处理单操作数的表达式。
   */
  UnaryOperatorNode = 'UnaryOperatorNode',
  /**
    ValueOperandNode（值操作数节点）
    作用：表示对变量或标识符的引用，例如读取变量值。
    示例代码：fieldId
    AST结构：
      1. 包含 name 属性（变量名或标识符路径）。
      2. 无子节点（叶子节点）。
    典型场景：解析变量、属性访问时生成。
   */
  ValueOperandNode = 'ValueOperandNode',
  /**
    PureValueOperandNode（纯值操作数节点）
    作用：表示不可变的字面量值（如常量），与变量引用区分。
    示例代码：true、null 或枚举值。
    AST结构：
      1.包含 value 属性（如 true, null, 42）。
      2.无子节点（叶子节点）。
    与 ValueOperandNode 的区别：ValueOperandNode 是变量引用，可能变化；PureValueOperandNode 是固定值。
   */
  PureValueOperandNode = 'PureValueOperandNode',
  /**
    CallOperandNode（调用操作数节点）
    作用：表示函数或方法的调用。
    示例代码：sum(a, b) 或 console.log("hello")
    AST结构：
      1. 包含 callee 属性（被调用的函数名或表达式，如 sum）。
      2. 包含 arguments 列表（参数节点，如 a, b）。
    典型场景：解析函数调用时生成。
   */
  CallOperandNode = 'CallOperandNode',
  /**
    StringOperandNode（字符串操作数节点）
    作用：表示字符串字面量。
    示例代码："hello" 或 'world'
    AST结构：
      1. 包含 value 属性（字符串内容，如 "hello"）。
      2. 无子节点（叶子节点）。
    典型场景：处理字符串常量时生成。
   */
  StringOperandNode = 'StringOperandNode',
  /**
    NumberOperandNode（数字操作数节点）
    作用：表示数字字面量（整数或浮点数）。
    示例代码：42 或 3.14
    AST结构：
      1. 包含 value 属性（数字内容，如 42）。
      2. 无子节点（叶子节点）。
    典型场景：处理数字常量时生成。
   */
  NumberOperandNode = 'NumberOperandNode',
}

/**
 * 抽象语法树（Abstract Syntax Tree，AST）数据结构
 * 表示源代码的抽象结构，通常用于编译器或解释器中。
 */
export abstract class AstNode {
  readonly token: Token;

  readonly name!: AstNodeType;

  valueType!: BasicValueType;

  innerValueType?: BasicValueType;

  constructor(token: Token) {
    this.token = token;
  }

  get numNodes(): number {
    return 1;
  }

  toString() {
    return `AstNode: ${this.token}::${this.name}`;
  }
}

export class BinaryOperatorNode extends AstNode {
  readonly left: AstNode;

  readonly right: AstNode;

  override readonly name = AstNodeType.BinaryOperatorNode;

  constructor(left: AstNode, token: Token, right: AstNode) {
    super(token);

    this.left = left;
    this.right = right;

    // TokenType.And, TokenType.Or, TokenType.Add,
    // TokenType.Times, TokenType.Div, TokenType.Minus,
    // TokenType.Mod, TokenType.Concat,
    switch (token.type) {
      // Addition, subtraction, multiplication and division operator symbols are calculated correctly only when both sides are numbers.
      case TokenType.Add: {
        const isNumberType = ({ valueType, innerValueType, token: _token }: AstNode) =>
          valueType === BasicValueType.Number ||
          innerValueType === BasicValueType.Number ||
          _token.value.toUpperCase() === 'BLANK';
        if ([left, right].every(isNumberType)) {
          this.valueType = BasicValueType.Number;
          return;
        }

        this.valueType = BasicValueType.String;
        return;
      }

      case TokenType.Minus:
      case TokenType.Times:
      case TokenType.Mod:
      case TokenType.Div: {
        this.valueType = BasicValueType.Number;
        return;
      }

      case TokenType.Or:
      case TokenType.And:
      case TokenType.Equal:
      case TokenType.NotEqual:
      case TokenType.Greater:
      case TokenType.GreaterEqual:
      case TokenType.Less:
      case TokenType.LessEqual: {
        this.valueType = BasicValueType.Boolean;
        return;
      }

      case TokenType.Concat: {
        this.valueType = BasicValueType.String;
        return;
      }

      default: {
        throw new TypeError(
          formatErrorMessage('Unknown operational character: {type}', {
            type: token.type,
          }),
        );
      }
    }
  }

  override get numNodes(): number {
    return 1 + this.left.numNodes + this.right.numNodes;
  }
}

export class UnaryOperatorNode extends AstNode {
  readonly child: AstNode;

  override readonly name = AstNodeType.UnaryOperatorNode;

  override readonly valueType: BasicValueType;

  constructor(child: AstNode, token: Token) {
    super(token);
    this.child = child;
    switch (token.type) {
      case TokenType.Minus:
        this.valueType = BasicValueType.Number;
        break;
      case TokenType.Not:
        this.valueType = BasicValueType.Boolean;
        break;
      case TokenType.Add:
        this.valueType = child.valueType;
        break;
      default:
        throw new Error(`unreachable ${token.value}`);
    }
  }

  override get numNodes(): number {
    return 1 + this.child.numNodes;
  }
}

/**
 * 变量操作数节点
 * 作用：表示对变量或标识符的引用，例如读取变量值。
 */
export abstract class ValueOperandNodeBase extends AstNode {
  readonly value: string;

  // override valueType!: BasicValueType;

  // override readonly name!: AstNodeType;

  field!: DatabaseFieldWithId;

  context!: { fields: DatabaseFieldWithId[] };

  protected constructor(token: Token) {
    super(token);
    this.value = token.value.replace(/\\(.)/g, '$1');
  }

  protected init(fieldIdValue: string, context: { fields: DatabaseFieldWithId[] }, hostField?: DatabaseFieldWithId) {
    this.context = context;
    // 转义
    const fieldKey = fieldIdValue.replace(/\\(.)/g, '$1');
    if (fieldKey === ROLLUP_KEY_WORDS && hostField) {
      this.field = hostField;
      this.valueType = BasicValueType.Array;
    } else {
      // 兼容 fieldMap 以 name 为 key 的情况
      const currField = this.context.fields.find((f) => f.id === fieldKey || iStringMatch(f.name, fieldKey));
      if (!currField) {
        throw new Error(
          formatErrorMessage('Invalid column or function name: {fieldId}', {
            fieldId: fieldKey,
          }),
        );
      }

      this.field = currField;
      const helper = FieldTypeHelper.create(currField, context);
      this.valueType = helper.basicValueType();
    }

    const helper = FieldTypeHelper.create(this.field, context);
    this.innerValueType = helper.innerBasicValueType();
  }
}

export class ValueOperandNode extends ValueOperandNodeBase {
  override readonly name = AstNodeType.ValueOperandNode;

  // override readonly field!: FieldSO;

  constructor(token: Token, context: { fields: DatabaseFieldWithId[] }, hostField?: DatabaseFieldWithId) {
    super(token);
    const fieldId = token.value.slice(1, -1);
    this.init(fieldId, context, hostField);
  }
}

export class PureValueOperandNode extends ValueOperandNodeBase {
  override readonly name = AstNodeType.PureValueOperandNode;

  // override readonly field!: FieldSO;

  constructor(token: Token, context: { fields: DatabaseFieldWithId[] }, hostField?: DatabaseFieldWithId) {
    super(token);
    const fieldId = token.value;
    this.init(fieldId, context, hostField);
  }
}

export class CallOperandNode extends AstNode {
  readonly value: string;

  override readonly name = AstNodeType.CallOperandNode;

  readonly params: AstNode[] = [];

  constructor(token: Token) {
    super(token);
    this.value = token.value;
  }

  override get numNodes(): number {
    return this.params.reduce((num, node) => num + node.numNodes, 1);
  }
}

export class NumberOperandNode extends AstNode {
  readonly value: string;

  override readonly name = AstNodeType.NumberOperandNode;

  override valueType = BasicValueType.Number;

  constructor(token: Token) {
    super(token);
    this.value = token.value;
  }
}

export class StringOperandNode extends AstNode {
  readonly value: string;

  override readonly name = AstNodeType.StringOperandNode;

  override valueType = BasicValueType.String;

  constructor(token: Token) {
    super(token);

    let tokenValue = token.value;
    const terminatorMap = new Map([
      [/\\n/g, '\n'], // newline
      [/\\r/g, '\r'], // newline
      [/\\t/g, '\t'], // tab
    ]);

    terminatorMap.forEach((v, k) => {
      tokenValue = tokenValue.replace(k, v);
    });
    tokenValue = tokenValue.replace(/\\(.)/g, '$1');
    this.value = tokenValue;
  }
}
