import type { DatabaseFieldWithId, DatabaseFormulaField } from '@bika/types/database/bo';
import { iStringMatch, iStringParse, Locale } from '@bika/types/system';
import { FormulaExprLexer, TokenType } from './lexer';
import { AstNode, FormulaExprParser, FormulaParserContext } from './parser';

type FormulaExprResult =
  | {
      success: true;
      ast: AstNode;
    }
  | {
      success: false;
      error: Error;
    };

/**
 * 公式工具助手
 */
export class FormulaHelper {
  /**
   * Parse an expression to AST
   *
   * @param expr expression
   * @param ctx parser context
   * @param fieldVariableTo 变量名转换成 id 还是 name
   * @returns AST or error
   */
  public static parse(
    expr: string,
    ctx: FormulaParserContext,
    fieldVariableTo?: 'id' | 'name',
    options?: { locale?: Locale },
  ): FormulaExprResult {
    if (expr.trim().length === 0) {
      // 表达式不允许为空字符串
      return { success: false, error: new Error(`The formula can't be empty`) };
    }
    const expression = this.expressionTransform(expr, { fields: ctx.fields }, fieldVariableTo || 'id', options);
    // console.log('Transformed expression:', expression);
    const parser = new FormulaExprParser(new FormulaExprLexer(expression), ctx);
    const ast = parser.parse();
    return { success: true, ast };
  }

  /**
   * 将以字段名称作为变量名的表达式转换为以字段 ID 或者 Name 作为变量名的表达式
   * 前端使用字段名作为变量名, 后端使用字段 ID 作为变量名
   * 前端调用它来辨识表达式并确认是否可用
   * @param expression 公式表达式
   * @param fields 字段列表
   * @param fieldVariableTo 变量名转换成 id 还是 name
   */
  public static expressionTransform(
    expression: string,
    {
      fields,
    }: {
      fields: DatabaseFieldWithId[];
    },
    fieldVariableTo: 'id' | 'name', // 公式里的变量名转换成 id 还是 name
    options?: { locale?: Locale }, // fieldVariableTo = name 时, 需要 locale 来处理展示值
  ): string {
    if (!expression) {
      return expression;
    }

    const lexer = new FormulaExprLexer(expression);
    if (!lexer) {
      return '';
    }

    return lexer.fullMatches.reduce<string>((acc, token) => {
      let tokenValue = token.value;

      function getPureTokenValue() {
        const value = tokenValue.replace(/\\(.)/g, '$1');
        // console.log(`pureTokenValue: [${value}]`);
        // const field = fieldMap[pureTokenValue];
        const field = fields.find((f) => f.id === value || iStringMatch(f.name, value));
        // Convert the variable name according to the to parameter
        if (!field) {
          return tokenValue;
        }
        if (fieldVariableTo === 'id') {
          return field.id;
        }

        const name = iStringParse(field.name, options?.locale).replace(/[{}\\]/g, '\\$&');
        // When name contains illegal parameters, {} wrapping is required
        if (/[/+\-|=*/><()（）!&%'"“”‘’^`~,，\s]/.test(name)) {
          return `{${name}}`;
        }
        return name;
      }

      function getTokenValue() {
        const value = tokenValue.slice(1, -1).replace(/\\(.)/g, '$1');
        // console.log(`TokenValue: [${value}]`);
        // const field = fieldMap[pureTokenValue];
        const field = fields.find((f) => f.id === value || iStringMatch(f.name, value));
        if (!field) {
          return tokenValue;
        }

        if (fieldVariableTo === 'id') {
          return `{${field.id}}`;
        }

        // Convert the variable name according to the 'to' parameter
        const fieldName = iStringParse(field.name, options?.locale);
        return `{${fieldName.replace(/[{}\\]/g, '\\$&')}}`;
      }

      if (token.type === TokenType.PureValue) {
        tokenValue = getPureTokenValue();
      }

      if (token.type === TokenType.Value) {
        tokenValue = getTokenValue();
      }

      return acc + tokenValue;
    }, '');
  }

  /**
   * Topological sort the fields
   *
   * @param fields Array of DatabaseFieldWithId
   * @returns Sorted fields
   * @throws Error if a cyclic dependency has been detected among these formula fields
   * @deprecated 待重构, 使用其他方法
   */
  public static topologicalSort<T extends DatabaseFieldWithId>(fields: T[]): T[] {
    // Convert to map
    const fieldMap = Object.fromEntries(fields.map((fld) => [fld.id, fld]));

    // Init graph
    const graph: Map<string, { id: string; deps: string[] }> = new Map();
    for (const field of fields) {
      const deps: string[] = [];

      // Only formula field has dependencies
      if (field.type === 'FORMULA') {
        let expr = (field as DatabaseFieldWithId<DatabaseFormulaField>).property?.expression;
        if (typeof expr !== 'string') {
          throw new Error(`The expression of \`${iStringParse(field.name)}\` field must be a string.`);
        }
        // TODO: The expressionTransform() should be remove
        expr = this.expressionTransform(expr, { fields }, 'id');

        const fieldName = iStringParse(field.name);

        // Get the dependent fields
        const { valueMap } = new FormulaExprLexer(expr);
        for (const depId of valueMap.keys()) {
          if (!fieldMap[depId]) {
            throw new Error(`Error in expression of \`${fieldName}\` field. The \`{${depId}}\` field is not found.`);
          }

          deps.push(depId);
        }
      }

      graph.set(field.id, { id: field.id, deps });
    }

    // Store the sorted fields
    const result: T[] = [];
    const visited: Set<string> = new Set();
    const visiting: Set<string> = new Set();

    // DFS
    function visit(node: { id: string; deps: string[] }) {
      if (visited.has(node.id)) return;
      if (visiting.has(node.id)) {
        throw new Error('A cyclic dependency has been detected among these formula fields.');
      }

      visiting.add(node.id);
      for (const depId of node.deps) {
        visit(graph.get(depId)!);
      }
      visiting.delete(node.id);

      visited.add(node.id);
      result.push(fieldMap[node.id]);
    }

    // Start DFS
    for (const node of graph.values()) {
      visit(node);
    }

    return result;
  }
}
