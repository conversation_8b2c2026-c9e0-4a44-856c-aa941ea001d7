import { ExpressionNode } from '@rsql/ast';
import { parse } from '@rsql/parser';
import { UserSO } from '@bika/domains/user/server';
import { ViewFilter } from '@bika/types/database/bo';
import { CustomAstNodeVisitor } from './ast-node-visitor';
import { AstNodeParser } from './ast-parser';
import { DatabaseSO } from '../../database-so';

export class AstFilter {
  private readonly user: UserSO;

  private database: DatabaseSO;

  constructor(user: UserSO, database: DatabaseSO) {
    this.user = user;
    this.database = database;
  }

  /**
   * 替换字符串中所有 {...} 包裹的字段名为映射后的 ID
   * @param input 原始字符串
   * @param replacer 回调函数，接收字段名（去掉大括号），返回替换后的字符串
   * @returns 替换后的字符串
   */
  private replaceFieldExpressions(input: string, replacer: (fieldName: string) => string): string {
    return input.replace(/\{([^{}]+?)\}/g, (_, fieldName) => {
      const trimmedFieldName = fieldName.trim();
      const replaced = replacer(trimmedFieldName);
      return replaced;
    });
  }

  private safeReplace(filterAsRSql: string): string {
    const rsql = this.replaceFieldExpressions(filterAsRSql, (fieldName) => {
      const field = this.database.getFieldByFieldKey(fieldName);
      return field.id;
    });
    // console.debug('RSQL after field replacement:', rsql);
    return rsql;
  }

  private safeParseRSql(filterAsRSql: string): ExpressionNode {
    try {
      const rsql = this.safeReplace(filterAsRSql);
      return parse(rsql);
    } catch (error) {
      console.error('Error parsing RSQL:', error);
      throw new Error(`Failed to parse filter: ${filterAsRSql}, please check the grammar.`);
    }
  }

  toViewFilter(filterAsRSql: string, options?: { timeZone?: string }): ViewFilter {
    const astNode = this.safeParseRSql(filterAsRSql);
    const parser = new AstNodeParser(astNode);
    const visitor = new CustomAstNodeVisitor(this.user, this.database, options);
    return parser.accept(visitor);
  }
}
