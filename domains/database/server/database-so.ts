import { isNotBlank } from 'basenext/utils/string';
import Excel from 'exceljs';
import _ from 'lodash';
import { generateNanoID } from 'basenext/utils/nano-id';
import {
  DefaultDatumDatabaseFieldsWhenEmptyFields,
  FixedTaskDatabaseFields,
} from '@bika/contents/config/server/database/default-fields';
import { errors } from '@bika/contents/config/server/error/errors';
import { ServerError } from '@bika/contents/config/server/error/server_error';
import { getServerLocaleContext } from '@bika/contents/i18n/server';
import { FormulaHelper } from '@bika/domains/database/shared';
import { EventSO } from '@bika/domains/event/server/event/event-so';
import { FormSO } from '@bika/domains/form/server/form-so';
import { ExcelImporter, ExcelWorkbookWrapper } from '@bika/domains/integration/server/handlers/excel-importer-handler';
import { MirrorSO } from '@bika/domains/mirror/server/mirror-so';
import { TemplateFolderSO } from '@bika/domains/node/server/folder-so';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { IRelationIdOpts, NodeResourceSO } from '@bika/domains/node/server/types';
import { isArrayOfType, isNullOrUndefined } from '@bika/domains/shared/shared';
import { SpaceAttachmentSO } from '@bika/domains/space/server/space-attachment-so';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { OnlineSessionSO } from '@bika/domains/system/server/online-session-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import {
  DatabaseFieldType,
  DatabaseRecordModel,
  DatabaseType,
  db,
  mongoose,
  MongoTransactionCB,
  newDistributedOperation,
  Prisma,
  PrismaPromise,
} from '@bika/server-orm';
import { TrashBO } from '@bika/types/change/bo';
import {
  RecordData,
  ViewFilter,
  View,
  DatabaseField,
  Database,
  DatabaseTypes,
  DatabaseRecord,
  DatabaseLinkField,
  DatabaseFieldWithId,
  DocCellDataSchema,
  CellValue,
  DocCellData,
  BasicValueType,
} from '@bika/types/database/bo';
import { defaultDatabaseFieldCreateBO } from '@bika/types/database/default';
import {
  DatabaseCreateDTO,
  DatabaseUpdateDTO,
  DatabaseViewCreateDTO,
  RecordBulkUpdates,
} from '@bika/types/database/dto';
import {
  BaseDatabaseVO,
  CellValueLabelVO,
  CONST_PREFIX_FIELD,
  CONST_PREFIX_RECORD,
  DatabaseVO,
} from '@bika/types/database/vo';
import { ExportBOOptions, NodeResourceType, NodeStateBO, ToBoOptions, ToTemplateOptions } from '@bika/types/node/bo';
import { NodeRenderOpts } from '@bika/types/node/vo';
import { DateTimeSO, iString, iStringParse, Locale, LocaleType } from '@bika/types/system';
import {
  SSEEventDatabaseImportComplete,
  SSEEventDatabaseImportProgress,
  SSEEventDatabaseImportStart,
} from '@bika/types/user/bo';
import { CellValueConvertorFactory } from './cells/cellvalue-convertor/factory';
import { LinkCellSO } from './cells/link-cell-so';
import { CellKey } from './cells/types';
import { DatabaseLazyFix } from './database-lazyfix';
import { FieldBOProcessorFactory } from './fields/bo/factory';
import { FieldBOProcessor } from './fields/bo/types';
import { ModifiedTimeFieldSO } from './fields/date-time-field';
import { FieldAdjacencyTable } from './fields/field-adjacency-table';
import { FieldDirectedGraph } from './fields/field-directed-graph';
import { FieldSOFactory } from './fields/field-factory';
import { FieldSO } from './fields/field-so';
import { FormulaFieldSO } from './fields/formula-field';
import { LinkFieldSO, OneWayLinkFieldSO } from './fields/link-field';
import { LookupFieldSO } from './fields/lookup-field';
import { MemberFieldSO } from './fields/member-field';
import {
  BatchRecordChange,
  CreateRecordContext,
  DatabaseFieldModel,
  FieldCellChange,
  RecordChange,
  RecordChanges,
  UpdateFieldRecordCell,
  UpdateFieldRecordCellMap,
  UpdateRecordContext,
} from './fields/types';
import { ModifiedByFieldSO } from './fields/user-field';
import { FieldUtils } from './fields/utils';
import { RecordSO } from './record-so';
import {
  databaseInclude,
  DatabaseModel,
  CreateRecordOptions,
  ViewInsertOrder,
  FieldCellModelMap,
  RecordCellModelMap,
} from './types';
import { DatabaseViewModel, DefaultViewName } from './views';
import { FilterSO } from './views/filter-so';
import { SortSO } from './views/sort-so';
import { ViewSO } from './views/view-so';
import { ViewSorter } from './views/view-sorter';
import { TrashSO } from '../../change/server/trash-so';
import { DocSO } from '../../doc/server/doc-so';
import { MemberSO } from '../../unit/server';

export class DatabaseSO extends NodeResourceSO {
  private readonly _model: DatabaseModel;

  private _views: ViewSO[] = [];

  private _space: SpaceSO | null = null;

  private _fieldDirectedGraph: FieldDirectedGraph | null = null;

  private constructor(model: DatabaseModel) {
    super();
    this._model = model;
  }

  get resourceType(): NodeResourceType {
    return 'DATABASE';
  }

  get model(): DatabaseModel {
    return this._model;
  }

  get id(): string {
    return this.model.id;
  }

  get templateId(): string | undefined {
    return this.model.templateId || undefined;
  }

  get name(): iString {
    return this.model.name as iString;
  }

  getName(locale: LocaleType = 'en'): string {
    return iStringParse(this.name, locale);
  }

  get description(): iString | undefined {
    return (this.model.description as iString) || undefined;
  }

  get type() {
    return this.model.type;
  }

  get spaceId(): string {
    return this.model.node.spaceId;
  }

  get parentId(): string | null {
    return this.model.node.parentId;
  }

  get viewModels(): DatabaseViewModel[] {
    return this.model.views;
  }

  get fieldModels(): DatabaseFieldModel[] {
    return this.model.fields;
  }

  async getSpace(): Promise<SpaceSO> {
    if (!this._space) {
      this._space = await SpaceSO.init(this.spaceId);
    }
    return this._space;
  }

  toNodeSO(): NodeSO {
    return NodeSO.initWithModel(this.model.node);
  }

  async getForms(): Promise<FormSO[]> {
    return FormSO.findByDatabaseId(this.id);
  }

  async getMirrors(): Promise<MirrorSO[]> {
    return MirrorSO.findByDatabaseId(this.id);
  }

  override async toBO(opts?: ToBoOptions): Promise<Database> {
    const views = await this.getViews();
    const recordBOS: DatabaseRecord[] = [];
    if (opts?.withRecords) {
      // 如果需要记录, 则获取记录
      await this.getRecordsAsStream(
        async (records) => {
          recordBOS.push(...records.map((r) => r.toBO()));
        },
        { pageSize: 1000 },
      );
    }
    return {
      id: this.id,
      templateId: this.templateId,
      resourceType: 'DATABASE',
      icon: this.toNodeSO().icon,
      name: this.name,
      description: this.description,
      databaseType: this.type as DatabaseTypes,
      fields: this.getFields().map((f) => f.toBO() as DatabaseField),
      views: views.map((v) => v.toBO()),
      records: recordBOS,
    };
  }

  override async setTemplateId() {
    const operations: PrismaPromise<Prisma.BatchPayload>[] = this.setTemplateIdWithIdOperation();
    const fields = this.getFields();
    for (const field of fields) {
      operations.push(...field.getPublishOperations());
    }
    const views = await this.getViews();
    for (const view of views) {
      operations.push(...view.getPublishOperations());
    }
    return operations;
  }

  override async toTemplate(opts?: ToTemplateOptions) {
    const database = _.omit(await this.toBO({ withRecords: false }), 'id');
    if (!database.templateId) {
      database.templateId = this.id;
    }
    const fields = this.getFields();
    const fieldBOList: DatabaseField[] = [];
    for (const field of fields) {
      fieldBOList.push(field.toTemplate(opts));
    }
    const views = await this.getViews();
    const viewBOList: View[] = [];
    for (const view of views) {
      viewBOList.push(view.toTemplate(opts));
    }
    if (!opts?.withRecords) {
      return { ...database, fields: fieldBOList, views: viewBOList };
    }
    const records: DatabaseRecord[] = [];
    await this.getRecordsAsStream(async (recordSOList) => {
      const recordTemplates = await Promise.all(recordSOList.map((r) => r.toTemplate(opts)));
      records.push(...recordTemplates);
    });
    return {
      ...database,
      fields: fieldBOList,
      views: viewBOList,
      records,
    };
  }

  override async export(opts?: ExportBOOptions): Promise<Database> {
    if (opts?.withRecords) {
      // check record limitation
      const recordCount = await this.getRecordsCount();
      if (recordCount > 50000) {
        const { t } = getServerLocaleContext(opts?.locale || 'en');
        throw new Error(t('error.export.record_limit', { databaseName: iStringParse(this.name, opts?.locale) }));
      }
    }
    const database = await this.toBO(opts);
    const fieldBOList: DatabaseFieldWithId[] = [];
    const fields = this.getFields();
    const getInstanceId =
      opts?.getInstanceId ||
      ((id: string) => {
        if (id === this.id) {
          return this.id;
        }
        return undefined;
      });
    for (const field of fields) {
      fieldBOList.push(field.exportBO({ ...opts, getInstanceId }));
    }
    // todo handle view and records
    const records = database.records ? await this.handleDocValue(database.records) : undefined;
    return { ...database, fields: fieldBOList as DatabaseField[], records };
  }

  private async handleDocValue(records: DatabaseRecord[]): Promise<DatabaseRecord[]> {
    const recordBOList: DatabaseRecord[] = [];
    const docIds: string[] = [];
    // 遍历所有记录, 收集所有文档的docId
    for (const record of records) {
      _.forEach(record.data, (value) => {
        const parsed = DocCellDataSchema.safeParse(value);
        if (parsed.success) {
          docIds.push(parsed.data.docId);
        }
      });
    }
    // 获取所有文档的json字符串,填入到record.values中
    const docs = await DocSO.getDocumentJsonStringByIds(_.uniq(docIds));
    for (const record of records) {
      const cloneRecord = _.cloneDeep(record);
      _.forEach(record.data, (value, key) => {
        const parsed = DocCellDataSchema.safeParse(value);
        if (parsed.success) {
          const doc = docs[parsed.data.docId];
          // 将文档转换成的josn 结构存入values, 因为record.data中保存的name是空字符串,所以也需要将名字重新填入data中
          if (doc && cloneRecord.values) {
            cloneRecord.values[key] = doc.value;
            cloneRecord.data[key] = { docId: parsed.data.docId, name: doc.name };
          }
        }
      });
      recordBOList.push(cloneRecord);
    }
    return recordBOList;
  }

  async getForm(formId: string): Promise<FormSO> {
    const forms = await this.getForms();
    const form = forms.find((i) => i.id === formId);
    if (!form) {
      throw new Error('Form not belong to database.');
    }
    return form;
  }

  async getMirror(mirrorId: string): Promise<MirrorSO> {
    const mirrors = await this.getMirrors();
    const mirror = mirrors.find((i) => i.id === mirrorId);
    if (!mirror) {
      throw new Error('Mirror not belong to database.');
    }
    return mirror;
  }

  /**
   * 获取视图插入的前后视图ID,只需要提供前面的视图ID即可
   * @param afterViewId 期望在哪个视图ID之后插入, 如果为空, 则默认插入最后面
   */
  async getViewInsertOrder(afterViewId?: string): Promise<ViewInsertOrder> {
    if (!afterViewId) {
      // 默认在最后面
      const lastView = await this.lastView();
      if (lastView) {
        return { preViewId: lastView.id, nextViewId: undefined };
      }
      return { preViewId: undefined, nextViewId: undefined };
    }
    const nextView = await ViewSO.findOneByPreViewId(this.id, afterViewId);
    if (nextView) {
      return { preViewId: afterViewId, nextViewId: nextView.id };
    }
    return { preViewId: afterViewId, nextViewId: undefined };
  }

  /**
   * 添加一个视图
   * @param user user
   * @param view create view dto
   */
  async createView(user: UserSO, view: DatabaseViewCreateDTO): Promise<ViewSO> {
    return ViewSO.createView(user.id, this.spaceId, view);
  }

  /**
   * 获取视图数量
   */
  async getViewsCount(): Promise<number> {
    return ViewSO.countByDatabaseId(this.id);
  }

  /**
   * 懒加载获取视图
   * @returns view
   */
  async getViews(): Promise<ViewSO[]> {
    if (this._views.length === 0) {
      await this.reloadViews();
    }
    return this._views;
  }

  /**
   * 硬加载视图
   */
  private async loadViews(): Promise<ViewSO[]> {
    const viewModels = await ViewSO.listByDatabaseId(this.id);
    return viewModels.map((viewModel) => new ViewSO(this, viewModel));
  }

  /**
   * 重新加载视图
   */
  async reloadViews() {
    const views = await this.loadViews();
    // 视图排序
    const sorter = ViewSorter.init(views);
    this._views = sorter.sort();
  }

  /**
   * get view by view id
   * @param viewId view id
   */
  async getView(viewId: string): Promise<ViewSO> {
    const viewModel = await ViewSO.findOneById(this.id, viewId);
    if (!viewModel) {
      throw new ServerError(errors.database.view_not_found, { viewId });
    }
    return new ViewSO(this, viewModel);
  }

  /**
   * get view by id or template id
   */
  async getViewByViewKey({ viewId, viewTemplateId }: { viewId?: string; viewTemplateId?: string }): Promise<ViewSO> {
    if (isNotBlank(viewId)) {
      return this.getView(viewId!);
    }
    if (isNotBlank(viewTemplateId)) {
      const viewModel = await ViewSO.findOneByTemplateId(this.id, viewTemplateId!);
      if (!viewModel) {
        throw new Error('View not found');
      }
      return new ViewSO(this, viewModel);
    }
    throw new Error('View not found');
  }

  /**
   * 获取第一个View
   * @returns view so
   */
  async firstView(): Promise<ViewSO> {
    const views = await this.getViews();
    return views[0];
  }

  /**
   * 获取最后一个视图
   * @returns view or null
   */
  async lastView(): Promise<ViewSO | null> {
    const views = await this.getViews();
    if (views.length === 0) {
      return null;
    }
    return views[views.length - 1];
  }

  /**
   * 检查是否新字段名重复
   * @param fieldName iString
   */
  checkFieldNameDuplicate(fieldName: iString): void {
    const duplicate = this.getFields().some((f) => _.isEqual(f.name, fieldName));
    if (duplicate) {
      throw new ServerError(errors.database.field_name_duplicate);
    }
  }

  /**
   *  检查创建字段前的条件
   * 例如: 检查字段名是否重复, 字段类型是否支持等
   * @param field  字段BO
   */
  checkBeforeCreateField(field: DatabaseField): void {
    // 检查是否为空
    if (typeof field.name === 'object') {
      if (Object.keys(field.name).length === 0) {
        throw new ServerError(errors.database.field_name_empty);
      }
      for (const value of Object.values(field.name)) {
        if (typeof value === 'string' && value.trim().length === 0) {
          throw new ServerError(errors.database.field_name_empty);
        }
      }
    }
    if (typeof field.name === 'string' && field.name.trim().length === 0) {
      throw new ServerError(errors.database.field_name_empty);
    }
    // 检查是否重名
    this.checkFieldNameDuplicate(field.name);
  }

  checkFieldNameUpdateDuplicate(fieldId: string, fieldName: iString): void {
    // 不可以设置为空字符串
    if (typeof fieldName === 'object') {
      if (Object.keys(fieldName).length === 0) {
        throw new ServerError(errors.database.field_name_empty);
      }
      for (const value of Object.values(fieldName)) {
        if (typeof value === 'string' && value.trim().length === 0) {
          throw new ServerError(errors.database.field_name_empty);
        }
      }
    }
    if (typeof fieldName === 'string' && fieldName.trim().length === 0) {
      throw new ServerError(errors.database.field_name_empty);
    }
    const duplicate = this.getFields().some((f) => f.id !== fieldId && _.isEqual(f.name, fieldName));
    if (duplicate) {
      throw new ServerError(errors.database.field_name_duplicate);
    }
  }

  /**
   * 当有其他表格创建了双向关联字段关联到当前表格时
   * 当前表格需要创建一个关联字段, 依赖对方表格的关联字段ID
   * @param linkDatabase 关联表
   * @param linkFieldId 关联表的关联字段ID
   * @returns BO
   */
  createNewLinkFieldBO(linkDatabase: DatabaseSO, linkFieldId: string): DatabaseLinkField {
    const relateField: DatabaseLinkField = {
      id: generateNanoID(CONST_PREFIX_FIELD),
      name: this.generateLinkFieldName(linkDatabase.getName()),
      type: 'LINK',
      property: {
        foreignDatabaseId: linkDatabase.id,
        brotherFieldId: linkFieldId,
      },
    };
    return relateField;
  }

  /**
   * 创建一个字段名时, 如果已经存在, 则在字段名后面加上数字
   * @param fieldName 字段名
   * @returns 新的字段名
   */
  private generateLinkFieldName(fieldName: string): string {
    const fields = this.getFields();
    const fieldNames = fields.map((field) => field.name);

    if (!fieldNames.includes(fieldName)) {
      return fieldName;
    }

    // 一直找到没重复的名字为止
    for (let i = 1; ; i += 1) {
      const name = `${fieldName} ${i}`;
      if (!fieldNames.includes(name)) {
        return name;
      }
    }
  }

  async getLinkDatabaseByFieldId(fieldId: string): Promise<DatabaseSO | null> {
    const field = this.findFieldByFieldKey(fieldId);
    if (!field) {
      return null;
    }
    if (field.type === 'LINK' || field.type === 'ONE_WAY_LINK') {
      const linkField = field as LinkFieldSO | OneWayLinkFieldSO;
      return linkField.getForeignDatabase();
    }
    return null;
  }

  async getLinkDatabaseByDatabaseId(databaseId: string): Promise<DatabaseSO | null> {
    const linkFields = this.getFields().filter((f) => f.type === 'LINK' || f.type === 'ONE_WAY_LINK');
    if (linkFields.length === 0) {
      return null;
    }
    for (const field of linkFields) {
      if (field.property && 'foreignDatabaseId' in field.property && field.property.foreignDatabaseId === databaseId) {
        return DatabaseSO.init(databaseId);
      }
    }
    return null;
  }

  /**
   * 获取表格里的关联字段类型的关联表键值对
   */
  async getLinkedDatabaseMap(): Promise<{ [fieldId: string]: DatabaseSO }> {
    const foreignDatabaseMap = this.getFields().reduce<{ [fieldId: string]: string }>((acc, field) => {
      if (
        (field.type === 'LINK' || field.type === 'ONE_WAY_LINK') &&
        field.property &&
        'foreignDatabaseId' in field.property &&
        field.property.foreignDatabaseId
      ) {
        return {
          ...acc,
          [field.id]: field.property.foreignDatabaseId,
        };
      }
      return acc;
    }, {});
    const foreignDatabaseIds = Object.values(foreignDatabaseMap);
    if (foreignDatabaseIds.length === 0) {
      return {};
    }
    // 批量查询出来
    const databases = await DatabaseSO.getDatabases(this.spaceId, foreignDatabaseIds);
    return Object.entries(foreignDatabaseMap).reduce<{ [fieldId: string]: DatabaseSO }>(
      (acc, [fieldId, databaseId]) => {
        const database = databases.find((d) => d.id === databaseId);
        if (database) {
          return {
            ...acc,
            [fieldId]: database,
          };
        }
        return acc;
      },
      {},
    );
  }

  async getDirectedGraph(): Promise<FieldDirectedGraph> {
    if (this._fieldDirectedGraph) {
      // 已经存在, 直接返回
      return this._fieldDirectedGraph;
    }
    const fields = this.getFields();
    const adjacencyTable = new FieldAdjacencyTable();
    for (const field of fields) {
      if (adjacencyTable.hasField(field.id)) {
        // 已经存在, 不需要重复添加
        continue;
      }
      // console.debug(`分析字段依赖关系: ${field.getName()}`);
      await field.collectRelations(adjacencyTable, new Set<string>());
    }
    this._fieldDirectedGraph = new FieldDirectedGraph(adjacencyTable);
    return this._fieldDirectedGraph;
  }

  /**
   * 按依赖顺序填充记录单元格值
   */
  async fillRecordModels(
    user: UserSO | null,
    orderedUpdatedFields: FieldSO[],
    recordModels: DatabaseRecordModel[],
    options?: { session?: mongoose.ClientSession },
  ): Promise<DatabaseRecordModel[]> {
    console.debug(`按照依赖顺序填充字段单元格值`);
    const newRecordModels: DatabaseRecordModel[] = [];
    for (const recordModel of recordModels) {
      let newRecordModel: DatabaseRecordModel = { ...recordModel };
      // 按照依赖关系顺序填充加载单元格值, 不可并行
      for (const orderedUpdatedField of orderedUpdatedFields) {
        newRecordModel = await orderedUpdatedField.reloadCellModel(user, newRecordModel, {
          session: options?.session,
        });
      }
      newRecordModels.push(newRecordModel);
    }
    return newRecordModels;
  }

  /**
   * 检查创建记录的输入值
   * @param recordDataList 创建记录输入值
   */
  private async checkRecordInputData(recordDataList: RecordData[]): Promise<void> {
    // 并行验证校验每一行输入值
    const fields = this.getFields();
    await Promise.all(
      recordDataList.map(async (recordData) => {
        const fieldKeys = Object.keys(recordData);
        await Promise.all(
          fields.map(async (field) => {
            const fieldKey = fieldKeys.find((key) => field.isMatchKey(key));
            const input = fieldKey ? recordData[fieldKey] : undefined;
            await field.validateInputCellValue(input);
          }),
        );
      }),
    );
  }

  async createRecord(
    user: UserSO | null,
    member: MemberSO | null,
    recordData: RecordData,
    options?: CreateRecordOptions,
  ): Promise<RecordSO> {
    const records = await this.createRecords(user, member, [recordData], options);
    return records[0];
  }

  /**
   * 批量创建记录
   */
  async createRecords(
    user: UserSO | null,
    member: MemberSO | null,
    recordDataList: RecordData[],
    options?: CreateRecordOptions,
  ): Promise<RecordSO[]> {
    const { formId } = options || {};
    // 创建记录的上下文
    const context: CreateRecordContext = { user, member, now: new Date() };
    // 创建记录的操作集合
    const distributedOperation = newDistributedOperation();
    // 并行验证校验每一行输入值
    await this.checkRecordInputData(recordDataList);
    // 需要创建的行数据模型
    const recordModelsToCreate: DatabaseRecordModel[] = [];
    // 需要创建的单元格数据的列
    const updatedFieldMap: Record<string, FieldSO> = {};
    // 表字段列表
    const fields = this.getFields();
    // 遍历请求创建的记录数据, 初始化单元格数据
    for (const recordData of recordDataList) {
      // 这里的循环不产生IO, 提高效率
      // 一行里每个字段对应的单元格值
      const fieldCellModelMap: FieldCellModelMap = {};
      const fieldKeys = Object.keys(recordData);
      for (const field of fields) {
        // 无需输入值的字段类型
        if (field.isAutoInitializeCellOnCreation()) {
          // 创建记录时自动初始化单元格值(审计字段类型/自动编号), 不接受输入
          updatedFieldMap[field.id] = field;
          fieldCellModelMap[field.id] = {
            bo: field.toBO(),
            cell: field.initializeCellModel(context),
          };
          continue;
        }
        // 匹配字段键
        const fieldKey = fieldKeys.find((key) => field.isMatchKey(key));
        // 无匹配字段键或值为空时的处理
        if (!fieldKey || isNullOrUndefined(recordData[fieldKey])) {
          // 没有此字段
          if (field.hasDefaultValue()) {
            // 存在默认值设置, 填充默认值
            updatedFieldMap[field.id] = field;
            fieldCellModelMap[field.id] = {
              bo: field.toBO(),
              cell: field.fillDefaultCellModel(context),
            };
          } else if (field.isPrimary()) {
            // 没配置默认值, 但是主字段必须参与更新
            updatedFieldMap[field.id] = field;
          }
          continue;
        }

        // 有输入值, 构建单元格数据
        const inputCellData = recordData[fieldKey];
        updatedFieldMap[field.id] = field;
        fieldCellModelMap[field.id] = {
          bo: field.toBO(),
          cell: field.buildCellModel({ input: inputCellData, context }),
        };
      }
      // 行记录数据
      const recordModel = RecordSO.buildNewRecordModel(this.spaceId, this.id, fieldCellModelMap, context);
      recordModelsToCreate.push(recordModel);
    }

    // 需要修改的字段属性, 比较是否发生了改变
    for (const field of Object.values(updatedFieldMap)) {
      if (field.hasPropertyChanged) {
        distributedOperation.prismaOperations.push(field.getUpdatePropertyOperation());
      }
    }

    // 表字段的依赖图(这里必须要执行, 单元格值的依赖关系初始化非常重要)
    const graph = await this.getDirectedGraph();
    // 表字段依赖关系
    const fieldAdjacencyTable = graph.getFieldAdjacencyTable();
    // 按序更新的列表
    const orderedUpdatedFields = graph.sortFieldsWithDependencies(Object.values(updatedFieldMap));
    console.debug(`批量创建记录->按序更新的字段列表: ${orderedUpdatedFields.map((f) => f.getName()).join(', ')}`);

    // 批量处理附件引用
    const { operations: attachmentAdjustRefOperations, mongoSession: attachmentRefSession } =
      await SpaceAttachmentSO.buildCreateRecordAttachmentSession(user, this, recordModelsToCreate);
    distributedOperation.prismaOperations.push(...attachmentAdjustRefOperations);
    distributedOperation.mongoOperations.push(attachmentRefSession);

    // 执行插入
    await db.mongo.transaction(
      async (session) => {
        // console.debug(`创建的记录: ${JSON.stringify(recordModelsToCreate)}`);
        // 填充是保证依赖顺序首次将单元格值初始化, 至于引用字段的单元格值依赖双向关联表的单元格值, 在填充不需要处理
        const filledRecordModels = await this.fillRecordModels(user, orderedUpdatedFields, recordModelsToCreate, {
          session,
        });
        // console.debug(`依赖顺序填充后的记录: ${JSON.stringify(filledRecordModels)}`);
        // 批量创建记录
        const createdRecordModels = await db.mongo
          .databaseRecord(this.spaceId)
          .insertMany(filledRecordModels, { session });
        const createdRecordIds = createdRecordModels.map((record) => record.id);
        // 触发每一列单元格数据的依赖更新
        for (const toUpdatedField of orderedUpdatedFields) {
          if (FieldUtils.isOnlyLinkFieldSO(toUpdatedField)) {
            // 刷新表引用列的记录, 主要是针对引用字段值刷新,因为下面不会再处理引用字段
            await toUpdatedField.reloadRelateLookupCells(user, createdRecordIds, { session });
          }
          // 依赖此字段的字段列表, 可能在外表
          const dependentFields = fieldAdjacencyTable.getDependentFields(toUpdatedField.id);
          // console.debug(
          //   `触发字段[${toUpdatedField.getName()}]依赖链路更新: ${dependentFields.map((f) => f.getName()).join(', ')}`,
          // );
          const updatedRecordModels = await db.mongo
            .databaseRecord(this.spaceId)
            .find({
              databaseId: this.id,
              id: { $in: createdRecordIds },
            })
            .session(session || null);
          for (const dependentField of dependentFields) {
            console.debug(`===========> 开始: ${dependentField.getName()} <===========`);
            await dependentField.updateCells(user, toUpdatedField.toBO(), updatedRecordModels, { session });
            console.debug(`===========> 结束: ${dependentField.getName()} <===========`);
          }
        }

        // 执行mongo操作
        for (const mongoSession of distributedOperation.mongoOperations) {
          await mongoSession(session);
        }
        // 执行db操作
        await db.prisma.$transaction(distributedOperation.prismaOperations);
      },
      // 事务配置
      {
        readPreference: 'primary',
        readConcern: { level: 'majority' },
        writeConcern: { w: 'majority' },
      },
    );

    const newRecordIds = recordModelsToCreate.map((record) => record.id);
    const recordModels = await db.mongo
      .databaseRecord(this.spaceId)
      .find({ id: { $in: newRecordIds } })
      .sort({ _id: 1 });
    await this.reloadFields();
    const space = await this.getSpace();
    const records = recordModels.map((recordModel) => RecordSO.initWithModel(space, this, recordModel));

    // 触发事件
    EventSO.database.onRecordCreated(this, records, { member: member ?? undefined, formId });
    return records;
  }

  async createRecordsDBOperation(
    records: DatabaseRecordModel[],
    params: { user: UserSO | null; member?: MemberSO; formId?: string },
  ) {
    const { user, member, formId } = params;
    const recordDAO = db.mongo.databaseRecord(this.spaceId);
    // 初始化所需索引
    await recordDAO.init();

    // 附件引用存储
    const { operations: attachmentAdjustRefOperation, mongoSession: attachmentRefSession } =
      await SpaceAttachmentSO.buildCreateRecordAttachmentSession(user, this, records);

    await db.mongo.transaction(async (session) => {
      // 先执行mongo事务
      await recordDAO.insertMany(records, { session });
      await attachmentRefSession(session);
      // 执行附件引用计数变更，失败将导致mongo事务回滚
      if (attachmentAdjustRefOperation.length > 0) {
        await db.prisma.$transaction(attachmentAdjustRefOperation);
      }
    });

    const space = await this.getSpace();
    const recordSOs: RecordSO[] = records.map((recordPO) => RecordSO.initWithModel(space, this, recordPO));

    // 触发事件
    EventSO.database.onRecordCreated(this, recordSOs, {
      member,
      formId,
    });
    return recordSOs;
  }

  /**
   * 批量更新数据表数据，通过 record ids
   * @deprecated 待重构,跟`bulkUpdateByFilter`合并
   */
  async bulkUpdateByRecordIds(user: UserSO | null, recordIds: string[], cellsMap: RecordData): Promise<RecordSO[]> {
    if (recordIds.length === 0) {
      return [];
    }
    const recordBulkUpdates: RecordBulkUpdates = recordIds.map((recordId) => ({
      recordId,
      cells: cellsMap,
    }));
    return this.updateRecords(user, recordBulkUpdates);
  }

  /**
   * 批量更新数据表数据，通过 filter
   * @deprecated 待重构,跟上面的方法`bulkUpdateByRecordIds`合并
   */
  async bulkUpdateByFilter(user: UserSO | null, filter: ViewFilter, cells: RecordData): Promise<RecordSO[]> {
    const filterSO = new FilterSO(this, filter);

    const filterQuery = await filterSO.buildQuery();

    const updatedRecords: RecordSO[] = [];

    await this.getRecordsAsStream(
      async (records) => {
        const recordBulkUpdates: RecordBulkUpdates = records.map((record) => ({
          recordId: record.id,
          cells,
        }));
        const updated = await this.updateRecords(user, recordBulkUpdates);
        updatedRecords.push(...updated);
      },
      { filterQuery },
    );

    return updatedRecords;
  }

  /**
   * 检查批量更新记录的输入值
   */
  private async checkRecordUpdateInput(bulkUpdateRecords: RecordBulkUpdates): Promise<void> {
    // 并行检查每一行输入值
    await Promise.all(
      bulkUpdateRecords.map(async ({ cells }) => {
        // 检查每一行的单元格值
        const fieldKeys = Object.keys(cells);
        await Promise.all(
          fieldKeys.map(async (fieldKey) => {
            const field = this.getFieldByFieldKey(fieldKey);
            const updateCellValue = cells[fieldKey];
            await field.validateInputCellValue(updateCellValue);
          }),
        );
      }),
    );
  }

  /**
   * 将批量更新记录请求转换为按列更新的单元格数据
   * 每一行更新的列都会被提取出来, 形成一个按列分组的对象
   * 例如:
   * 输入:
   * [
   *   { recordId: 'record1', cells: { columnA: 'value1', columnB: 'value2' } },
   *   { recordId: 'record2', cells: { columnA: 'value3', columnC: 'value4' } },
   * ]
   * 输出:
   * {
   *   columnA: [{ recordId: 'record1', cellValue: 'value1' }, { recordId: 'record2', cellValue: 'value3' }],
   *   columnB: [{ recordId: 'record1', cellValue: 'value2' }],
   *   columnC: [{ recordId: 'record2', cellValue: 'value4' }],
   * }
   * @param bulkUpdateRecords 批量更新记录请求
   */
  private flattenBulkUpdateRecords(bulkUpdateRecords: RecordCellModelMap): UpdateFieldRecordCellMap {
    // 步骤 1: 收集所有列名和记录名
    const columnSet = new Set<string>();
    const recordSet = new Map<string, FieldCellModelMap>();

    for (const [recordId, fieldCellMap] of Object.entries(bulkUpdateRecords)) {
      // 存储记录对应的数据
      recordSet.set(recordId, fieldCellMap);

      // 收集所有列名
      Object.keys(fieldCellMap).forEach((column) => columnSet.add(column));
    }

    // 步骤 2: 将列名转换为数组
    const columns = Array.from(columnSet);

    // 步骤 3: 构建结果数组
    const entries = columns.map((column) => {
      const values: UpdateFieldRecordCell[] = [];

      // 对每个记录填充该列的值
      for (const [recordName, fieldCellMap] of recordSet) {
        if (fieldCellMap[column] !== undefined) {
          // 在API视角, 不可能会有undefined传入, 只会null或其他值
          values.push({
            recordId: recordName,
            cell: fieldCellMap[column].cell,
          });
        }
      }

      return [column, values];
    });
    return Object.fromEntries(entries);
  }

  /**
   * 合并两个RecordChanges列表
   * @param changesA 第一个变更列表
   * @param changesB 第二个变更列表（在changesA之后发生）
   * @returns 合并后的新RecordChanges列表
   */
  private mergeRecordChanges(changesA: RecordChanges, changesB: RecordChanges): RecordChanges {
    // 创建按recordId分组的映射表
    const changesMap = new Map<string, RecordChange>();

    // 添加第一个列表的所有变更
    changesA.forEach((change) => {
      changesMap.set(change.recordId, {
        recordId: change.recordId,
        cellChanges: [...change.cellChanges], // 深拷贝
      });
    });

    // 合并第二个列表的变更
    changesB.forEach((changeB) => {
      const existingChange = changesMap.get(changeB.recordId);

      if (existingChange) {
        // 已有记录：合并字段变更
        const mergedCellChanges: FieldCellChange[] = [];
        const seenFields = new Set<string>();

        // 添加第一个列表的所有变更
        existingChange.cellChanges.forEach((change) => {
          mergedCellChanges.push({ ...change });
          seenFields.add(change.field.id);
        });

        // 处理第二个列表的变更（直接覆盖相同字段）
        changeB.cellChanges.forEach((cellChange) => {
          if (seenFields.has(cellChange.field.id)) {
            // 字段已存在：更新to值
            const existing = mergedCellChanges.find((c) => c.field.id === cellChange.field.id)!;
            existing.to = cellChange.to;
          } else {
            // 新增字段：添加变更
            mergedCellChanges.push({ ...cellChange });
            seenFields.add(cellChange.field.id);
          }
        });

        // 更新合并后的变更
        changesMap.set(changeB.recordId, {
          recordId: changeB.recordId,
          cellChanges: mergedCellChanges,
        });
      } else {
        // 新增记录：直接添加
        changesMap.set(changeB.recordId, {
          ...changeB,
          cellChanges: [...changeB.cellChanges], // 深拷贝
        });
      }
    });

    return Array.from(changesMap.values());
  }

  /**
   * 批量更新记录
   * 行数量较大时, 会有一定的性能问题, 得分批处理
   * 批量更新时不可以并行处理, 依赖更新时容易导致数据不一致
   */
  async updateRecords(user: UserSO | null, bulkUpdateRecords: RecordBulkUpdates): Promise<RecordSO[]> {
    // 只有站内成员可以修改
    const member = (await user?.getMember(this.spaceId)) || null;
    // 跨库操作集合
    const distributedOperation = newDistributedOperation();
    // 检查输入值, 有些值需要必填不可清空
    await this.checkRecordUpdateInput(bulkUpdateRecords);
    // 初始化上下文
    const context: UpdateRecordContext = { user, member, now: new Date() };
    // 表格字段
    const fields = this.getFields();
    // 克隆一份
    const sourceFields = fields.map((f) => FieldSOFactory.newWithModel(_.cloneDeep(f.model)));
    // 查询所有要更改的记录
    const recordIds = bulkUpdateRecords.map((item) => item.recordId);
    const records = await this.getRecords(recordIds);
    const previousRecords = records.reduce<Record<string, DatabaseRecordModel>>((acc, record) => {
      acc[record.id] = record.model;
      return acc;
    }, {});
    // 处理输入值, 过程会导致字段属性变更(单多选)
    // 有些字段的单元格值是自动计算, 不接受输入值, 会自动忽略掉
    const recordCellModelMap: RecordCellModelMap = {};
    for (const { recordId, cells } of bulkUpdateRecords) {
      const fieldCellModelMap: FieldCellModelMap = {};
      const fieldKeys = Object.keys(cells);
      // 初始化更新后的单元格数据
      for (const fieldKey of fieldKeys) {
        // 查找对应字段对象
        const field = fields.find((f) => f.isMatchKey(fieldKey));
        if (!field) {
          throw new Error(`update field cell not found: ${fieldKey}`);
        }
        // 输入值
        const inputCellValue = cells[fieldKey];
        if (isNullOrUndefined(inputCellValue)) {
          // 输入值为空, 代表清空
          fieldCellModelMap[field.id] = {
            bo: field.toBO(),
            cell: {},
          };
          continue;
        }
        // 构建单元格数据
        const cellModel = field.buildUpdateCellModel({ input: inputCellValue, context });
        if (!cellModel) {
          // 单元格值不需要更新, 跳过
          continue;
        }
        // 更新单元格数据
        fieldCellModelMap[field.id] = {
          bo: field.toBO(),
          cell: cellModel,
        };
      }
      if (Object.keys(fieldCellModelMap).length > 0) {
        recordCellModelMap[recordId] = fieldCellModelMap;
      }
    }

    // 每个字段对应的更新单元格数据
    const updateFieldRecordCellMap = this.flattenBulkUpdateRecords(recordCellModelMap);
    // 按列更新数据
    const toUpdateFieldIds = Object.keys(updateFieldRecordCellMap);
    // 审计字段参与自动更新
    const toUpdateFields = fields.filter((f) => toUpdateFieldIds.includes(f.id));
    toUpdateFields.push(...fields.filter((f) => f.type === 'MODIFIED_BY' || f.type === 'MODIFIED_TIME'));

    // 需要修改的字段属性, 比较是否发生了改变
    const previousFields: DatabaseFieldWithId[] = [];
    for (const field of toUpdateFields) {
      if (field.hasPropertyChanged) {
        distributedOperation.prismaOperations.push(field.getUpdatePropertyOperation());
        const sourceField = sourceFields.find((f) => f.id === field.id);
        if (sourceField) {
          // 记录之前的字段属性
          previousFields.push(sourceField.toBO());
        }
      }
    }

    // 表字段的依赖图(这里必须要执行, 单元格值的依赖关系初始化非常重要)
    const graph = await this.getDirectedGraph();
    // 表字段依赖关系
    const fieldAdjacencyTable = graph.getFieldAdjacencyTable();
    // 按序更新的字段列表
    // const orderedUpdatedFields = graph.sortFieldsWithDependencies(updateFields);
    console.debug(`批量更新记录->按序更新的字段列表: ${toUpdateFields.map((f) => f.getName()).join(', ')}`);

    // 执行批量更新记录操作
    let changes: RecordChanges = [];
    // 协作成员单元格变更通知
    const mentionedMemberFields: MemberFieldSO[] = [];
    await db.mongo.transaction(
      async (session) => {
        // 更新被依赖的字段单元格
        let updatedRecordModels: DatabaseRecordModel[] = [];
        for (const toUpdatedField of toUpdateFields) {
          // 先更新本表记录
          if (toUpdatedField.type === 'MODIFIED_BY' || toUpdatedField.type === 'MODIFIED_TIME') {
            updatedRecordModels = await db.mongo
              .databaseRecord(this.spaceId)
              .find({ id: { $in: recordIds } })
              .session(session);
          } else {
            const updateFieldRecordCells = updateFieldRecordCellMap[toUpdatedField.id];
            const fieldRecordChanges = await toUpdatedField.bulkUpdateCells(user, updateFieldRecordCells, { session });
            changes = this.mergeRecordChanges(changes, fieldRecordChanges);
            const updatedRecordIds = fieldRecordChanges.map((change) => change.recordId);
            // 已更新好的记录
            updatedRecordModels = await db.mongo
              .databaseRecord(this.spaceId)
              .find({ id: { $in: updatedRecordIds } })
              .session(session);
          }

          if (updatedRecordModels.length === 0) {
            // 没有改动, 无需更新依赖
            continue;
          }

          // 依赖此字段的字段列表, 可能在外表
          const dependentFields = fieldAdjacencyTable.getDependentFields(toUpdatedField.id);
          console.debug(
            `触发字段[${toUpdatedField.getName()}]依赖链路更新: ${dependentFields.map((f) => f.getName()).join(', ')}`,
          );
          for (const dependentField of dependentFields) {
            console.debug(`===========> 开始: ${dependentField.getName()} <===========`);
            await dependentField.updateCells(user, toUpdatedField.toBO(), updatedRecordModels, { session });
            console.debug(`===========> 结束: ${dependentField.getName()} <===========`);
          }
        }

        // 执行mongo操作
        for (const mongoSession of distributedOperation.mongoOperations) {
          await mongoSession(session);
        }
        // 执行db操作
        await db.prisma.$transaction(distributedOperation.prismaOperations);
      },
      // 事务配置
      {
        readPreference: 'primary',
        readConcern: { level: 'majority' },
        writeConcern: { w: 'majority' },
      },
    );
    // 重载表对象
    const reloadDatabase = await DatabaseSO.init(this.id);
    // 记录变更详情
    const recordChanges: BatchRecordChange = { changes, previousFields, previousRecords };
    // console.debug(`批量更新记录变更详情: ${JSON.stringify(recordChanges)}`);
    // 通知事件
    EventSO.database.onRecordsUpdated(user, reloadDatabase, recordChanges);

    // 假如有新成员被提及, 触发通知
    await Promise.all(mentionedMemberFields.map((memberField) => memberField.handleCellMentionNotify(user, changes)));

    return reloadDatabase.getRecords(recordIds);
  }

  /**
   * 批量删除记录
   * @param user 用户
   * @param recordIds 记录ID列表
   */
  async deleteRecords(user: UserSO, recordIds: string[]): Promise<void> {
    // 查找记录
    const recordsToDelete = await this.getRecords(Array.from(new Set(recordIds)));
    const safeRecordIds = recordsToDelete.map((record) => record.id);
    const distributedOperation = newDistributedOperation();
    // 附件引用计算
    const { operations: prismaOperations, mongoSession: attachmentRefSession } =
      await SpaceAttachmentSO.buildDeleteRecordAttachmentSession(user, this, recordsToDelete);
    distributedOperation.prismaOperations.push(...prismaOperations);
    distributedOperation.mongoOperations.push(attachmentRefSession);
    // 记录回收站
    const trashBO: TrashBO = {
      trashType: 'RECORD',
      bo: {
        databaseId: this.id,
        databaseName: this.name,
        records: recordsToDelete.map((record) => record.toBO()),
      },
    };
    const trashCreateInput = await TrashSO.boToCreateInput(this.spaceId, user, trashBO);
    distributedOperation.prismaOperations.push(db.prisma.trash.create({ data: trashCreateInput }));

    // 关联字段的关联记录
    const linkFields = this.getOnlyLinkFields();

    await db.mongo.transaction(
      async (session) => {
        // 以字段作为主要标准去批量刷新记录
        for (const linkField of linkFields) {
          // 每一行里关联字段移除的记录
          const recordDeleteMap: Record<string, string[]> = {};
          for (const record of recordsToDelete) {
            const cell = record.getCellSO(linkField.id) as LinkCellSO;
            const cellData = cell.getData();
            if (!cellData || cellData.length === 0) {
              // 没有关联记录, 无需处理关联表数据, 跳过
              continue;
            }
            // 删除记录关联表数据
            // await RecordLinkSO.deleteLinkRecords(linkField.id, record.id, cellData, { session });
            // 收集关联记录ID
            for (const recordId of cellData) {
              if (!recordDeleteMap[recordId]) {
                recordDeleteMap[recordId] = [record.id];
              } else {
                recordDeleteMap[recordId].push(record.id);
              }
            }
          }
          if (Object.keys(recordDeleteMap).length === 0) {
            // 没有需要删除的关联记录, 跳过
            continue;
          }
          // 更新关联表记录的关联字段的单元格
          const brotherField = await linkField.getBrotherField();
          if (brotherField) {
            // 更新字段对应的关联表记录里的ID
            // console.debug(`双向关联字段: ${brotherField.name} 删除关联记录:`, JSON.stringify(recordDeleteMap));
            await brotherField.removeLinkRecord(user, recordDeleteMap, { session });
          }
        }

        //  执行批量删除记录
        await db.mongo.databaseRecord(this.spaceId).deleteMany(
          {
            databaseId: this.id,
            id: { $in: safeRecordIds },
          },
          { session },
        );

        // 其他Mongo操作
        for (const mongoSession of distributedOperation.mongoOperations) {
          await mongoSession(session);
        }
        // 执行db操作
        await db.prisma.$transaction(distributedOperation.prismaOperations);
      },
      // 事务配置
      {
        readPreference: 'primary',
        readConcern: { level: 'majority' },
        writeConcern: { w: 'majority' },
      },
    );

    // 放入回收站
    await db.log.write({
      kind: 'SPACE_TRASH_LOG',
      trashid: trashCreateInput.id,
      spaceid: this.spaceId,
      data: JSON.stringify(trashBO),
    });

    // 触发事件
    EventSO.database.onRecordsDeleted(this, recordsToDelete);
  }

  /**
   * 初始化
   */
  static async init(databaseId: string): Promise<DatabaseSO> {
    const database = await this.getDatabaseById(databaseId);
    if (!database) {
      throw new ServerError(errors.database.not_found, { id: databaseId });
    }
    return database;
  }

  /**
   * 初始化
   */
  static initWithModel(model: DatabaseModel): DatabaseSO {
    return new DatabaseSO(model);
  }

  static async getDatabaseById(databaseId: string): Promise<DatabaseSO | null> {
    const databasePO = await db.prisma.database.findUnique({
      where: {
        id: databaseId,
      },
      include: databaseInclude,
    });
    if (!databasePO) {
      return null;
    }
    databasePO.fields = await DatabaseLazyFix.fixFields(databasePO.fields);
    return this.initWithModel(databasePO);
  }

  /**
   * 批量根据ID查询得到表格对象
   * @param spaceId space id
   * @param databaseIds database id list
   */
  static async getDatabases(spaceId: string, databaseIds: string[]): Promise<DatabaseSO[]> {
    if (databaseIds.length === 0) {
      return [];
    }
    const databasePOs = await db.prisma.database.findMany({
      where: {
        id: {
          in: databaseIds,
        },
        node: {
          spaceId,
        },
      },
      include: databaseInclude,
    });
    return databasePOs.map((databasePO) => this.initWithModel(databasePO));
  }

  static async findDatabase(param: {
    templateNodeId?: string;
    databaseId?: string;
    databaseTemplateId?: string;
  }): Promise<DatabaseSO> {
    const { templateNodeId, databaseId, databaseTemplateId } = param;
    return TemplateFolderSO.findNodeResourceByKey<DatabaseSO>(
      {
        nodeId: databaseId,
        nodeTemplateId: databaseTemplateId,
        templateNodeId,
      },
      'database',
    );
  }

  /**
   * 获取指定记录
   */
  async getRecord(recordId: string, session?: mongoose.ClientSession): Promise<RecordSO> {
    const recordPO = await db.mongo
      .databaseRecord(this.spaceId)
      .findOne({
        databaseId: this.id,
        id: recordId,
      })
      .session(session ?? null);
    if (!recordPO) {
      throw new Error(`Record not found: ${recordId}`);
    }
    const space = await this.getSpace();
    return RecordSO.initWithModel(space, this, recordPO);
  }

  /**
   * 批量查询指定记录
   * 可选在事务里查询
   */
  async getRecords(recordIds: string[], session?: mongoose.ClientSession): Promise<RecordSO[]> {
    const recordPOs = await db.mongo
      .databaseRecord(this.spaceId)
      .find({
        databaseId: this.id,
        id: { $in: recordIds },
      })
      .session(session ?? null);
    const space = await this.getSpace();
    return recordPOs.map((recordPO) => RecordSO.initWithModel(space, this, recordPO));
  }

  /**
   * 获取指定记录数的主字段展示值
   * @param recordIds 记录ID列表
   * @param session 可选的事务
   */
  async getPrimaryFieldValueOfRecords(
    recordIds: string[],
    options?: { session?: mongoose.ClientSession; locale?: Locale },
  ): Promise<(string | null)[]> {
    if (recordIds.length === 0) {
      return [];
    }
    // 获取记录
    const records = await this.getRecords(recordIds, options?.session);
    // 按输入的顺序初始化单元格渲染值
    const primaryValues: (string | null)[] = recordIds.map((recordId) => {
      const record = records.find((r) => r.id === recordId);
      if (!record) {
        throw new Error(`input record id ${recordId} not found`);
      }
      // 主字段值
      const cellValue = record.getPrimaryCellValue({ locale: options?.locale });
      if (cellValue && isArrayOfType(cellValue, (v) => typeof v === 'string')) {
        return cellValue.join(', ');
      }
      return cellValue as string | null;
    });
    return primaryValues;
  }

  /**
   * 分批处理指定视图的记录
   */
  async processViewRecordsByPage<T extends unknown[] | void>(
    { viewId, viewTemplateId }: { viewId?: string; viewTemplateId?: string },
    processor: (records: RecordSO[]) => Promise<T>,
    options?: {
      pageSize?: number;
    },
  ): Promise<T> {
    const { pageSize = 1000 } = options ?? {};
    const view = await this.getViewByViewKey({ viewId, viewTemplateId });
    view.setEndRow(pageSize);

    const results = [] as unknown as T;
    let records: RecordSO[] = [];

    do {
      records = await view.getRecords();
      const result = await processor(records);
      if (result && Array.isArray(result)) {
        (results as unknown[]).push(...result);
      }
      view.setStartRow(view.getEndRow());
      view.setEndRow(view.getEndRow() + pageSize);
    } while (records.length === pageSize);

    return results;
  }

  /**
   * 获取指定偏移量的记录
   * skip没传递, 就获取第一个记录
   * limit没传递, 就获取100条记录
   * tips: 不合适大数据量的分页, 适合小数据量的分页
   */
  async getRecordsAsPage(options?: {
    skip?: number;
    limit?: number;
    filterQuery?: mongoose.FilterQuery<DatabaseRecordModel>;
    sorts?: SortSO[];
    idDesc?: boolean; // 是否默认按ID倒序排序
  }): Promise<RecordSO[]> {
    const { skip = 0, limit = 100, filterQuery, sorts = [], idDesc } = options ?? {};
    const sort = sorts.reduce<Record<string, 1 | -1>>((acc, sortSO) => {
      const [sortField, order] = sortSO.toVO();
      acc[sortField] = order;
      return acc;
    }, {});

    if (Object.keys(sort).length === 0 && idDesc) {
      // 默认按ID倒序排序
      sort._id = -1;
    }

    const models = await db.mongo
      .databaseRecord(this.spaceId)
      .find({ databaseId: this.id, status: 'OPEN', ...filterQuery })
      .sort(sort)
      .skip(skip) // skip 0 means the first record
      .limit(limit);
    const space = await this.getSpace();
    return models.map((recordPO) => RecordSO.initWithModel(space, this, recordPO));
  }

  /**
   * 游标分页读取记录
   * @param lastId 上一页最后一条记录ID, 不传递则从头开始
   * @param pageSize 每页记录数
   * @param filterQuery 过滤条件
   * @param session 事务
   */
  async getRecordsAsCursor(options?: {
    lastId?: mongoose.Types.ObjectId;
    pageSize?: number;
    filterQuery?: mongoose.FilterQuery<DatabaseRecordModel>;
    sorts?: SortSO[]; // 排序条件
    session?: mongoose.ClientSession;
  }): Promise<{
    records: RecordSO[];
    nextId?: mongoose.Types.ObjectId;
    hasMore: boolean;
  }> {
    const { lastId, pageSize = 1000, filterQuery, sorts = [], session } = options ?? {};
    const databaseId = this.id;

    // 使用 pageSize + 1 技巧预判下一页存在性
    const querySize = pageSize + 1;
    // 构建查询条件
    const query = lastId
      ? {
          _id: { $gt: lastId }, // 不包含 lastId 的记录
          databaseId,
          status: 'OPEN', // TODO: status即将删掉
          ...filterQuery,
        }
      : { databaseId, status: 'OPEN', ...filterQuery };
    // 构造排序
    const sort = sorts.reduce<[string, mongoose.SortOrder][]>((acc, sortSO) => {
      const [sortField, order] = sortSO.toVO();
      acc.push([sortField, order]);
      return acc;
    }, []);
    // 追加默认按ID正序排序, 以防多个重复值时能保证顺序
    sort.push(['_id', 1]);
    const recordPOs = await db.mongo
      .databaseRecord(this.spaceId)
      .find(query)
      .sort(sort)
      .limit(querySize)
      .session(session ?? null);

    // 关键判断逻辑：实际数量>请求数量说明还有下一页
    const hasMore = recordPOs.length > pageSize;
    // 实际记录
    const actualRecords = hasMore ? recordPOs.slice(0, pageSize) : recordPOs;

    // 计算下一页游标（当存在下一页时）
    const nextId = hasMore && actualRecords.length > 0 ? actualRecords[actualRecords.length - 1]._id : undefined;
    const space = await this.getSpace();
    return {
      records: actualRecords.map((recordPO) => RecordSO.initWithModel(space, this, recordPO)),
      nextId,
      hasMore,
    };
  }

  /**
   * 按批次读取回调(自动翻页, 数据量不可预估, 后期可控制游标分页到指定数量就停止, 也可以把其作为异步查询不作为直接同步结果返回)
   */
  async getRecordsAsStream<T extends unknown[] | void>(
    callback: (records: RecordSO[]) => Promise<T>,
    opts?: {
      pageSize?: number;
      filterQuery?: mongoose.FilterQuery<DatabaseRecordModel>;
      sorts?: SortSO[]; // 排序条件
      session?: mongoose.ClientSession;
    },
  ): Promise<T> {
    const results = [] as unknown as T;
    let nextId: mongoose.Types.ObjectId | undefined;
    let hasMore = true;
    while (hasMore) {
      const {
        records,
        nextId: lastId,
        hasMore: more,
      } = await this.getRecordsAsCursor({
        lastId: nextId,
        pageSize: opts?.pageSize,
        filterQuery: opts?.filterQuery,
        sorts: opts?.sorts,
        session: opts?.session,
      });

      // 当前页无数据时直接中断
      if (records.length === 0) break;

      // 处理当前页数据
      const result = await callback(records);
      if (result && Array.isArray(result)) {
        (results as unknown[]).push(...result);
      }

      // 更新迭代状态
      nextId = lastId;
      hasMore = more;
    }

    return results;
  }

  async aggregateRecords(options: {
    filters?: mongoose.FilterQuery<DatabaseRecordModel>;
    fieldIds?: string[]; // return fields
    aggregateFn?: 'count' | 'sum' | 'avg' | 'min' | 'max';
    limit?: number; // 可选的限制数量
  }): Promise<DatabaseRecordModel[] | { [key: string]: number }[]> {
    const { filters = {}, fieldIds = [], aggregateFn, limit } = options;
    const getPipelineStage = (): mongoose.PipelineStage.Project | mongoose.PipelineStage.Count => {
      if (!aggregateFn) {
        const project: Record<string, number> = { id: 1 };
        for (const fieldId of fieldIds) {
          project[`data.${fieldId}`] = 1;
          project[`values.${fieldId}`] = 1;
          project[`computed.${fieldId}`] = 1;
        }
        return { $project: project };
      }
      if (aggregateFn === 'count') {
        return { $count: 'count' };
      }
      const project: Record<string, { [key: string]: string }> = {};
      for (const fieldId of fieldIds) {
        project[fieldId] = {
          [`$${aggregateFn}`]: `$data.${fieldId}`,
        };
      }
      return { $project: project };
    };

    const match = {
      spaceId: this.spaceId,
      databaseId: this.id,
      status: 'OPEN',
      ...filters,
    };

    const pipeline: mongoose.PipelineStage[] = [{ $match: match }, getPipelineStage()];

    if (limit) {
      pipeline.push({ $limit: limit });
    }

    const result = await db.mongo.databaseRecord(this.spaceId).aggregate(pipeline);
    return result as DatabaseRecordModel[];
  }

  /**
   * 表格第一行记录
   * @returns 第一个记录
   */
  async getFirstRecord(): Promise<RecordSO | null> {
    const firstRecordPO = await db.mongo.databaseRecord(this.spaceId).findOne(
      {
        databaseId: this.id,
        status: 'OPEN',
      },
      { sort: { _id: 1 } },
    );
    if (!firstRecordPO) {
      return null;
    }
    const space = await this.getSpace();
    return RecordSO.initWithModel(space, this, firstRecordPO);
  }

  /**
   * 表格记录总数
   * @returns 记录数量
   */
  async getRecordsCount() {
    return RecordSO.countRecords(this.spaceId, this.id);
  }

  /**
   * @deprecated 临时搬迁, 待重构
   */
  buildSort(fieldKey: string, isAsc: boolean): SortSO | null {
    const field = this.findFieldByFieldKey(fieldKey);
    if (!field) {
      return null;
    }
    const location = field.sortField();
    const fields = this.getFields().map((f) => f.toBO());
    const cvt = CellValueConvertorFactory.create(field.toBO(), { fields });
    const basicValueType = cvt.basicValueType();

    // 当字段类型是 Array 时，仅将第一个值作为排序依据
    if (basicValueType === BasicValueType.Array) {
      return new SortSO(location, `${field.id}.0`, isAsc);
    }
    return new SortSO(location, field.id, isAsc);
  }

  /**
   * 获取字段列表，注意，顺序是打乱的，如果想要正确的顺序，请使用View
   */
  getFields(): FieldSO[] {
    return this.fieldModels.map((fieldPO: DatabaseFieldModel) => FieldSOFactory.newWithModel(fieldPO));
  }

  /**
   * find field by field key
   * @param fieldKey id | name | templateId
   */
  findFieldByFieldKey(fieldKey: string): FieldSO | undefined {
    return this.getFields().find((f) => f.isMatchKey(fieldKey));
  }

  /**
   * get field by field key or else throw error
   * @param fieldKey id | name | templateId
   */
  getFieldByFieldKey(fieldKey: string): FieldSO {
    const field = this.findFieldByFieldKey(fieldKey);
    if (!field) {
      throw new ServerError(errors.database.field_not_found, { key: fieldKey, databaseName: this.getName() });
    }
    return field;
  }

  getFieldIdByFieldKey(fieldKey: string): string {
    const field = this.getFieldByFieldKey(fieldKey);
    return field.id;
  }

  /**
   * 获取所有公式字段
   */
  getFormulaFields(): FormulaFieldSO[] {
    return this.getFields().filter((field): field is FormulaFieldSO => field.type === 'FORMULA');
  }

  /**
   * 获取所有双向关联字段
   */
  getOnlyLinkFields(): LinkFieldSO[] {
    return this.getFields().filter((field) => FieldUtils.isOnlyLinkFieldSO(field));
  }

  /**
   * 获取所有查找引用字段
   */
  getLookupFields(): LookupFieldSO[] {
    return this.getFields().filter((field): field is LookupFieldSO => field.type === 'LOOKUP');
  }

  /**
   * 获取所有修改时间字段
   */
  getModifiedTimeFields(): ModifiedTimeFieldSO[] {
    return this.getFields().filter((field): field is ModifiedTimeFieldSO => field.type === 'MODIFIED_TIME');
  }

  /**
   * 获取所有修改人字段
   */
  getModifiedByFields(): ModifiedByFieldSO[] {
    return this.getFields().filter((field): field is ModifiedByFieldSO => field.type === 'MODIFIED_BY');
  }

  /**
   * 重新加载字段列表
   * 注意：此方法会覆盖当前对象的字段列表
   */
  async reloadFields(): Promise<void> {
    const fieldModels = await db.prisma.databaseField.findMany({
      where: {
        databaseId: this.id,
      },
      orderBy: { sequenceId: 'asc' },
    });
    this._model.fields = fieldModels;
  }

  /**
   * 获取主字段
   */
  getPrimaryField(): FieldSO {
    const primaryField = this.getFields().find((field) => field.primary);
    if (!primaryField) {
      throw new ServerError(errors.database.primary_field_not_found, { name: this.getName() });
    }
    return primaryField;
  }

  /**
   * 检查主字段是否存在
   */
  checkPrimaryFieldExist(): void {
    const primaryField = this.getFields().find((field) => field.primary);
    if (!primaryField) {
      throw new ServerError(errors.database.primary_field_not_found, { name: this.getName() });
    }
  }

  /**
   * 添加字段
   * note: 此方法提供UI创建字段的接口，所以BO结构是没有templateId的概念
   * 如果是存在templateId值, 那么应该是模板安装场景, 不应该是这个方法
   */
  async createField(user: UserSO, field: DatabaseField, options?: { viewId?: string }): Promise<FieldSO> {
    const { viewId } = options ?? {};
    // 检查字段名是否重复
    this.checkBeforeCreateField(field);
    // 字段BO处理器, 关联字段时需要特殊处理
    const fieldBOProcessor = FieldBOProcessorFactory.getProcessor(field);
    // 新字段ID
    const newFieldId = fieldBOProcessor.getId();
    // 这里会在内存里改掉当前fieldBO属性, 下面使用时需注意
    const addFieldOperations = await fieldBOProcessor.getPrismaCreateOperation(user.id, this);
    // PG操作
    const updateViewOperations: PrismaPromise<unknown>[] = [];
    // 表所有视图
    const views = await this.getViews();
    // 视图字段处理
    if (viewId) {
      // 添加字段到指定视图, 其他视图全部隐藏
      updateViewOperations.push(
        ...views.map((view) => {
          if (view.id === viewId) {
            return view.addFieldOperations(user.id, { id: newFieldId, hidden: false });
          }
          return view.addFieldOperations(user.id, { id: newFieldId, hidden: true });
        }),
      );
    } else {
      // 没有指定视图, 那就要判断是否有视图设置了自定义字段列表, 然后还要添加到自定义字段列表
      // 多个视图时, 全部隐藏
      if (views.length > 1) {
        updateViewOperations.push(
          ...views.map((view) => view.addFieldOperations(user.id, { id: newFieldId, hidden: true })),
        );
      }
      // 只有一个视图, 添加到默认视图
      if (views.length === 1) {
        updateViewOperations.push(views[0].addFieldOperations(user.id, { id: newFieldId, hidden: false }));
      }
    }

    // 创建字段操作
    await db.mongo.transaction(
      async (session) => {
        // 初始化单元格数据
        await fieldBOProcessor.initializeCellAsPage(user, this, { session });
        // PG操作
        await db.prisma.$transaction([...addFieldOperations, ...updateViewOperations]);
      },
      // 事务配置
      {
        readPreference: 'primary',
        readConcern: { level: 'majority' },
        writeConcern: { w: 'majority' },
      },
    );
    // 重新加载字段列表, 一个线程里重复使用当前对象继续往下操作, 单测场景用得居多
    await this.reloadFields();
    // 返回字段对象
    return this.getFieldByFieldKey(newFieldId);
  }

  toSimpleVO(opts?: NodeRenderOpts): BaseDatabaseVO {
    const { locale } = opts ?? {};
    return {
      id: this.id,
      name: this.getName(locale),
      description: iStringParse(this.description, locale),
      spaceId: this.spaceId,
    };
  }

  async toVO(opts?: NodeRenderOpts): Promise<DatabaseVO> {
    const { locale } = opts ?? {};
    const baseVO = this.toSimpleVO(opts);
    const views = await this.getViews();
    return {
      ...baseVO,
      views: views.map((item) => item.toVO({ locale })),
    };
  }

  /**
   * @deprecated 有性能隐患,待重构
   */
  public async existsRecordByFieldIdAndData(fieldId: string, data: CellValue): Promise<boolean> {
    const result = await db.mongo.databaseRecord(this.spaceId).exists({
      databaseId: this.id,
      [`${CellKey.DATA}.${fieldId}`]: data,
    });
    return !!result;
  }

  /**
   * 导出数据表到Excel文件
   */
  async exportToExcel(excelFilePath: string, locale: LocaleType = 'en') {
    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet(this.getName(locale));
    const fields = this.getFields();
    const fieldNames = fields.map((field) => iStringParse(field.name, locale));
    worksheet.addRow(fieldNames);

    await this.getRecordsAsStream(
      async (records) => {
        // 获取记录VO
        const recordVOs = await Promise.all(records.map((record) => record.toVO({ locale })));

        // 构建记录ID-row Map
        const recIdToRowMap = new Map<string, CellValueLabelVO[]>();
        for (const recordVO of recordVOs) {
          const row = fields.map((field) => {
            const value = recordVO.cells[field.id].value;
            return Array.isArray(value) ? value.join(',') : value ?? '';
          });
          recIdToRowMap.set(recordVO.id, row);
        }

        // 按顺序添加行
        for (const record of records) {
          const row = recIdToRowMap.get(record.id);
          worksheet.addRow(row);
        }
      },
      { pageSize: 1000 },
    );

    try {
      await workbook.xlsx.writeFile(excelFilePath);
    } catch (e: unknown) {
      throw new Error((e as Error).message);
    }
  }

  /**
   * 生成一个增量导入的模板excel
   */
  async generateImportExcelTemplate(excelFilePath: string, locale: LocaleType) {
    // 创建一个新的工作簿
    const workbook = new Excel.Workbook();
    // 添加一个新的工作表
    const worksheet = workbook.addWorksheet(this.getName(locale));

    // 生成增量导入模板
    const fields = this.getFields().map((field) => field.toBO());
    const excelFields = ExcelImporter.convertToExcelFields(fields);
    ExcelImporter.generateIncrementalTemplate(worksheet, excelFields, locale);

    // 保存到文件
    try {
      await workbook.xlsx.writeFile(excelFilePath);
    } catch (e: unknown) {
      throw new Error((e as Error).message);
    }
  }

  async importFromTemplateExcel(
    user: UserSO,
    workbook: ExcelWorkbookWrapper,
  ): Promise<{
    fields: DatabaseFieldWithId[];
    total: number;
  }> {
    const fields = this.getFields().map((field) => field.toBO());

    // 检查 Excel 字段是否匹配
    const sheet = workbook.getFirstValidatedSheet(fields, user.locale);

    const member = await user.getMember(this.spaceId);

    // 创建一个流，用于将 Sheet 的所有行转换成 Records
    // 每次读取 100 行
    const stream = sheet.createConvertStream(500);

    // 异步导入
    setTimeout(async () => {
      const jobId = generateNanoID('job');
      const totalRecords = sheet.totalRows;

      // Emit import start event
      await this.sendEventToDatabaseOnlineUsers({
        name: 'database-import-start',
        databaseId: this.id,
        jobId,
        totalRecords,
        processedRecord: 0,
      });

      // 已导入的记录数
      let importedRecords = 0;

      try {
        for await (const records of stream) {
          // 追加 records 到数据表
          // 如果某些字段校验失败，则填充 undefined
          await this.createRecords(user, member, records, {
            fillNullOnFieldValidationFail: true,
            skipRelationProcess: true,
          });
          importedRecords += records.length;

          if (importedRecords >= totalRecords) {
            break;
          }

          // Emit progress event
          await this.sendEventToDatabaseOnlineUsers({
            name: 'database-import-progress',
            databaseId: this.id,
            jobId,
            totalRecords,
            processedRecord: importedRecords,
          });
        }

        // Emit complete event
        await this.sendEventToDatabaseOnlineUsers({
          name: 'database-import-complete',
          databaseId: this.id,
          jobId,
          totalRecords,
          operator: user.id,
          processedRecord: importedRecords,
        });
      } catch (error) {
        console.error(error);
        const SseSO = await import('@bika/domains/event/server/sse/sse-so').then((m) => m.SseSO);

        // 发送失败事件给导入者
        await SseSO.emit(user.id, {
          name: 'database-import-failure',
          databaseId: this.id,
          jobId,
          totalRecords,
          processedRecord: importedRecords,
          reason: String(error),
        });
      }
    });

    return {
      fields,
      total: sheet.totalRows,
    };
  }

  async importFromTemplateExcelPreview(
    workbook: ExcelWorkbookWrapper,
    locale: LocaleType,
  ): Promise<{
    fields: DatabaseFieldWithId[];
    records: DatabaseRecord[];
    total: number;
  }> {
    const fields = this.getFields().map((field) => field.toBO());
    const sheet = workbook.getFirstValidatedSheet(fields, locale);

    // 默认预览前 10 行
    const records = sheet.batchConvertRowsToRecords(0, 10);
    const total = sheet.totalRows;

    const recordBOS: DatabaseRecord[] = records.map((record) => ({ data: record }));
    return {
      fields,
      records: recordBOS,
      total,
    };
  }

  /**
   * update database
   * @param data update data
   * @deprecated 升级模版未开放
   */
  public updateOperation(
    data: Pick<Prisma.DatabaseUpdateInput, 'name' | 'description' | 'updatedBy' | 'node'>,
  ): PrismaPromise<DatabaseModel> {
    return db.prisma.database.update({
      where: { id: this.id },
      data,
      include: {
        fields: true,
        views: true,
        node: true,
      },
    });
  }

  /**
   * @deprecated 待重构, 目前功能未开放, 以后重构
   */
  public async upgradeDatabaseViewOperation(userId: string, upgradeViews: View[]) {
    // 有序遍历
    const viewUpsertOperation: PrismaPromise<DatabaseViewModel>[] = [];
    const beforeViews = await this.getViews();
    // 过滤默认视图
    const beforeViewSOs = beforeViews.filter((view) => view.name !== DefaultViewName);
    // 最后一个视图的id作为前置视图id往下叠加新视图
    let preViewId: string | undefined =
      beforeViewSOs.length > 0 ? beforeViewSOs[beforeViewSOs.length - 1].id : undefined;
    upgradeViews.forEach((upgradeView) => {
      if (upgradeView.templateId) {
        // 有设置唯一键,根据键查找并修改
        const matchView = beforeViewSOs.find((view) => view.templateId === upgradeView.templateId);
        if (matchView) {
          // 更新视图
          const updateViewOperation = matchView.updateOperation(userId, upgradeView);
          viewUpsertOperation.push(updateViewOperation);
        } else {
          // 创建视图, 并且按序创建
          const { id, operation } = this.createViewOperationWithTemplate(userId, preViewId, upgradeView);
          viewUpsertOperation.push(operation);
          preViewId = id;
        }
      } else {
        // 没有设置,直接创建视图, 并且按序创建
        const { id, operation } = this.createViewOperationWithTemplate(userId, preViewId, upgradeView);
        viewUpsertOperation.push(operation);
        preViewId = id;
      }
    });
    // console.log(`表 ${databaseSO.name} 的视图更改数量: ${viewUpsertOperation.length}`);
    return viewUpsertOperation;
  }

  /**
   * @deprecated 待重构, 目前功能未开放, 以后重构
   */
  public upgradeFieldOperation(userId: string, upgradeFields: DatabaseField[]): PrismaPromise<DatabaseFieldModel>[] {
    // 现有的字段
    const oldFieldSOs = this.getFields();
    // 为 upgradeFields 填充 id，如果是新字段且未分配 id，则生成新 id
    const fieldWithIdList: DatabaseFieldWithId[] = upgradeFields.map((upgradeField) => {
      if (upgradeField.id) {
        return { ...upgradeField, id: upgradeField.id };
      }
      if (upgradeField.templateId) {
        const matchField = oldFieldSOs.find((field) => field.templateId === upgradeField.templateId);
        if (matchField) {
          return {
            ...upgradeField,
            id: matchField.id,
          };
        }
      }
      return {
        ...upgradeField,
        id: upgradeField.id ?? generateNanoID(CONST_PREFIX_FIELD),
      };
    });
    const finalFields: DatabaseFieldWithId[] = [];
    for (const upgradeField of fieldWithIdList) {
      const processor = FieldBOProcessorFactory.getProcessor(upgradeField as DatabaseField);
      processor.handleProperty({ fields: fieldWithIdList });
      const processedField = processor.getBO();
      if (processedField.id) {
        finalFields.push({ ...processedField, id: processedField.id });
      } else if (processedField.templateId) {
        const oldField = oldFieldSOs.find((field) => field.templateId === processedField.templateId);
        if (oldField) {
          finalFields.push({
            ...processedField,
            id: oldField.id,
          });
        } else {
          finalFields.push({
            ...processedField,
            id: processedField.id ?? generateNanoID('fld'),
          });
        }
      } else {
        finalFields.push({
          ...processedField,
          id: processedField.id ?? generateNanoID('fld'),
        });
      }
    }

    // TODO: 交由BO处理器来检查
    FormulaHelper.topologicalSort(finalFields as DatabaseFieldWithId[]);

    return finalFields.map((finalField) => {
      if (finalField.templateId) {
        const oldField = oldFieldSOs.find((field) => field.templateId === finalField.templateId);
        if (oldField) {
          // 更新字段
          return oldField.updateOperation(userId, finalField);
        }
      }
      // 创建字段
      const processor = FieldBOProcessorFactory.getProcessor(finalField as DatabaseField);
      processor.handleProperty({ fields: finalFields });
      const processedField = processor.getBO();
      return db.prisma.databaseField.create({
        data: {
          id: finalField.id,
          databaseId: this.id,
          spaceId: this.spaceId,
          templateId: processedField.templateId,
          name: processedField.name,
          type: processedField.type.toString() as DatabaseFieldType,
          property: processedField.property ?? undefined,
          validators: processedField.validators,
          revision: 0,
          primary: processedField.primary,
          createdBy: userId,
          updatedBy: userId,
        },
      });
    });
  }

  /**
   * 根据模板创建视图的数据库操作
   * @param userId user id
   * @param preViewId previous view id
   * @param viewTemplate view template
   * @deprecated 待重构, 目前功能未开放, 以后重构
   */
  public createViewOperationWithTemplate(
    userId: string,
    preViewId: string | undefined,
    viewTemplate: View,
  ): { id: string; operation: PrismaPromise<DatabaseViewModel> } {
    const { id, operation } = ViewSO.createViewOperationWithTemplate(userId, {
      databaseId: this.id,
      spaceId: this.spaceId,
      preViewId,
      viewTemplate,
    });
    return { id, operation };
  }

  /**
   * 基于模板创建数据库表的数据库操作
   * @param userId user id
   * @param createParam create param
   * @param databaseTemplate database template
   */
  static createDatabaseOperationWithTemplate(
    userId: string,
    createParam: {
      spaceId: string;
      parentId: string;
      preNodeId?: string;
      nextNodeId?: string;
      unitId?: string;
    },
    databaseTemplate: Database,
  ): { id: string; operation: PrismaPromise<DatabaseModel>; recordModels: DatabaseRecordModel[] } {
    const {
      id,
      input: databaseCreateInput,
      recordModels,
    } = this.buildDatabaseCreateInputWithTemplate(userId, createParam, databaseTemplate);
    const operation = db.prisma.database.create({
      data: databaseCreateInput,
      include: {
        views: true,
        fields: true,
        node: true,
      },
    });
    return {
      id,
      operation,
      recordModels,
    };
  }

  async updateWithNodeInput(
    userId: string,
    param: DatabaseUpdateDTO,
  ): Promise<PrismaPromise<DatabaseModel | DatabaseViewModel>[]> {
    // todo more option for fields and views
    const databaseOperation: PrismaPromise<DatabaseModel | DatabaseViewModel>[] = [];
    databaseOperation.push(
      db.prisma.database.update({
        where: {
          id: this.id,
        },
        data: {
          name: param.name,
          description: param.description,
          updatedBy: userId,
          node: {
            update: {
              name: param.name,
              description: param.description,
              updatedBy: userId,
            },
          },
        },
        include: {
          fields: true,
          views: true,
          node: true,
        },
      }),
    );
    if (param.views) {
      const viewOperations = await ViewSO.updateSort(userId, param.views);
      databaseOperation.push(...viewOperations);
    }
    return databaseOperation;
  }

  static boToCreateInput(
    user: UserSO,
    databaseId: string,
    spaceId: string,
    data: DatabaseCreateDTO,
  ): {
    databaseInput?: Prisma.DatabaseUncheckedCreateNestedOneWithoutNodeInput;
    records?: DatabaseRecordModel[];
    operations?: PrismaPromise<unknown>[];
  } {
    if (data.resourceType !== 'DATABASE') {
      return {};
    }
    const userId = user.id;
    const fields = data.fields ? data.fields : defaultDatabaseFieldCreateBO(user.locale);
    if (!fields) {
      throw new Error('Default create field data is required');
    }
    const views = data.views
      ? data.views
      : [
          {
            name: DefaultViewName,
          },
        ];
    const fieldsCreateInput: Prisma.DatabaseFieldCreateManyDatabaseInput[] = this.buildFieldCreateInput(
      userId,
      spaceId,
      fields,
    );
    const { records, operations } = this.transformRecordTemplate(
      userId,
      databaseId,
      spaceId,
      data.records ?? [],
      fieldsCreateInput,
    );
    let preViewId: string;
    const databaseInput: Prisma.DatabaseUncheckedCreateNestedOneWithoutNodeInput = {
      create: {
        name: data.name,
        description: data.description,
        templateId: data.templateId,
        createdBy: userId,
        updatedBy: userId,
        revision: 0,
        type: DatabaseType.DATUM,
        views: {
          create: views.map((o) => {
            const viewInput = ViewSO.buildViewCreateWithoutDatabaseInput(userId, { ...o, spaceId, preViewId });
            preViewId = viewInput.id;
            return viewInput;
          }),
        },
        fields: {
          create: fieldsCreateInput,
        },
      },
    };
    return { databaseInput, records, operations };
  }

  private static transformRecordTemplate(
    userId: string,
    databaseId: string,
    spaceId: string,
    records: DatabaseRecord[],
    fieldsInput: Prisma.DatabaseFieldCreateManyDatabaseInput[],
  ): { records: DatabaseRecordModel[]; operations?: PrismaPromise<unknown>[] } {
    const recordModels: DatabaseRecordModel[] = [];
    const operations: PrismaPromise<unknown>[] = [];
    if (records && records.length > 0) {
      if (fieldsInput.length === 0) {
        throw new Error('Database template records must have fields');
      }
      const templateRecordDataTransformer = (dataOfRecord: RecordData, valuesOfRecord: RecordData | undefined) => {
        // 转换字段TemplateId成对应的ID
        const recordData: RecordData = {};
        const recordValues: RecordData = _.cloneDeep(valuesOfRecord) ?? {};
        for (const fieldTemplateId in dataOfRecord) {
          if (fieldTemplateId in dataOfRecord) {
            const foundField = fieldsInput.find((f) => f.templateId === fieldTemplateId || f.id === fieldTemplateId);
            if (!foundField || !foundField.id) {
              throw new Error(`Field template id not found in this template context: ${fieldTemplateId}`);
            }
            recordData[foundField.id] = dataOfRecord[fieldTemplateId];
            if (foundField.type === 'WORK_DOC') {
              // 导出的时候将doc 转换成了json str, 存入了values
              const docStr = _.get(valuesOfRecord, foundField.id) as string;
              const docCellData = _.get(dataOfRecord, foundField.id) as DocCellData;
              // 创建文档，并关联到新的ID
              const { id: docId, operation } = DocSO.createByJsonString(userId, spaceId, docCellData.name, docStr);
              docCellData.docId = docId;
              // 将文档的名字重新写入到values, 因为values 原来保存的就只有名字
              recordValues[foundField.id] = docCellData.name;
              operations.push(operation);
            }
          }
        }

        // 填充字段默认值
        // TODO: 优化，应该在 FieldSO 中统一处理
        for (const field of fieldsInput) {
          if (field.type === 'CREATED_TIME' || field.type === 'MODIFIED_TIME') {
            recordData[field.id] = DateTimeSO.now().toISOString();
          }
        }
        return recordData;
      };
      for (const record of records) {
        // 构建数据
        const wrappedRecord = templateRecordDataTransformer(record.data, record.values);
        const recordModel: DatabaseRecordModel = {
          id: record.id || generateNanoID(CONST_PREFIX_RECORD),
          spaceId,
          databaseId,
          templateId: record.templateId,
          data: wrappedRecord,
          computed: {},
          values: record.values || wrappedRecord,
          subscribers: [],
          revision: 0,
          status: 'OPEN',
          createdBy: userId,
          updatedBy: userId,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        recordModels.push(recordModel);
      }
    }
    return { records: recordModels, operations };
  }

  static async createDefaultRecordsSession(
    data: {
      databaseId: string;
      userId: string;
      spaceId: string;
    },
    options: { session: mongoose.ClientSession },
  ): Promise<void> {
    const { databaseId, spaceId, userId } = data;

    const records = [0, 1, 2].map((_o) => ({
      id: generateNanoID(CONST_PREFIX_RECORD),
      spaceId,
      revision: 0,
      databaseId,
      data: {},
      computed: {},
      values: {},
      status: 'OPEN',
      createdBy: userId,
      updatedBy: userId,
      createdAt: new Date(),
      updatedAt: new Date(),
    }));
    const recordDAO = db.mongo.databaseRecord(spaceId);
    await recordDAO.init();
    await recordDAO.insertMany(records, options);
  }

  override relationInstanceId(database: Database, opts: IRelationIdOpts): boolean {
    // TODO 下面的也要重构
    ViewSO.relationInstanceId(database.templateId || database.id!, database.views || [], opts);
    if (database.records) {
      RecordSO.relationInstanceId(database.templateId || database.id!, database.fields!, database.records, opts);
    }

    if (database.fields) {
      const processFields = database.fields.map((field) => {
        const fieldBOProcessor = FieldBOProcessorFactory.getProcessor(field);
        fieldBOProcessor.relationInstanceId(database.templateId || database.id!, opts);
        return fieldBOProcessor.getBO();
      });
      for (const field of database.fields) {
        if (field.id) {
          // 有设置ID
          const processField = processFields.find((f) => f.id && f.id === field.id);
          if (processField) {
            field.property = processField.property;
          }
        } else if (field.templateId) {
          // 有设置模板ID
          const processField = processFields.find((f) => f.templateId && f.templateId === field.templateId);
          if (processField) {
            field.id = processField.id;
            field.property = processField.property;
          }
        }
      }
    }
    return true;
  }

  /**
   * 基于模板构建创建数据库表的结构
   * @param userId user id
   * @param createParam create param
   * @param databaseTemplate database template
   */
  private static buildDatabaseCreateInputWithTemplate(
    userId: string,
    createParam: {
      spaceId: string;
      parentId: string;
      preNodeId?: string;
      nextNodeId?: string;
      unitId?: string;
    },
    databaseTemplate: Database,
  ) {
    const { spaceId, parentId, preNodeId, nextNodeId, unitId } = createParam;
    const databaseId = databaseTemplate.id || generateNanoID('dat');
    const databaseCreateInput: Prisma.DatabaseCreateInput = {
      templateId: databaseTemplate.templateId,
      name: databaseTemplate.name,
      description: databaseTemplate.description,
      type: DatabaseType.DATUM,
      revision: 0,
      createdBy: userId,
      updatedBy: userId,
      node: {
        create: {
          id: databaseId,
          templateId: databaseTemplate.templateId,
          name: databaseTemplate.name,
          description: databaseTemplate.description,
          icon: databaseTemplate.icon,
          type: 'DATABASE',
          createdBy: userId,
          updatedBy: userId,
          unit: unitId ? { connect: { id: unitId } } : undefined,
          space: {
            connect: {
              id: spaceId,
            },
          },
          parent: {
            connect: {
              id: parentId,
            },
          },
          preNode: {
            connect: preNodeId
              ? {
                  id: preNodeId,
                }
              : undefined,
          },
          nextNode: {
            connect: nextNodeId
              ? {
                  id: nextNodeId,
                }
              : undefined,
          },
        },
      },
    };

    const { databaseType, views, fields, records } = databaseTemplate;

    // 构建字段
    let fieldsInput: Prisma.DatabaseFieldCreateManyDatabaseInput[];
    if (!fields || fields.length === 0) {
      fieldsInput = this.buildDefaultFieldsCreateInput(userId, spaceId, databaseType);
    } else {
      let mixedFields = fields;
      if (databaseType === DatabaseType.TASK) {
        // TASK类型表格，有固定字段附上，不可被删除
        mixedFields = [...FixedTaskDatabaseFields, ...fields];
      }
      fieldsInput = this.buildFieldCreateInput(userId, spaceId, mixedFields);
    }
    databaseCreateInput.fields = {
      create: fieldsInput,
    };

    // 构建视图
    if (views && views.length > 0) {
      const viewInput = ViewSO.buildManyViewCreateWithoutDatabaseInput(userId, spaceId, views);
      databaseCreateInput.views = {
        createMany: {
          data: viewInput,
        },
      };
    } else {
      const viewInput = ViewSO.buildViewCreateWithoutDatabaseInput(userId, {
        name: DefaultViewName,
        spaceId,
      });
      databaseCreateInput.views = {
        createMany: {
          data: viewInput,
        },
      };
    }

    // 构建记录
    const recordModels: DatabaseRecordModel[] = [];
    if (records && records.length > 0) {
      if (!fields || fields.length === 0) {
        throw new Error('Database template records must have fields');
      }
      const templateRecordDataTransformer = (dataOfRecord: RecordData) => {
        // 转换字段TemplateId成对应的ID
        const recordData: RecordData = {};
        for (const fieldTemplateId in dataOfRecord) {
          if (fieldTemplateId in dataOfRecord) {
            const foundField = fieldsInput.find((f) => f.templateId === fieldTemplateId || f.id === fieldTemplateId);
            if (!foundField || !foundField.id) {
              throw new Error(`Field template id not found in this template context: ${fieldTemplateId}`);
            }
            recordData[foundField.id] = dataOfRecord[fieldTemplateId];
          }
        }

        // 填充字段默认值
        // TODO: 优化，应该在 FieldSO 中统一处理
        for (const field of fieldsInput) {
          if (field.type === 'CREATED_TIME' || field.type === 'MODIFIED_TIME') {
            recordData[field.id] = DateTimeSO.now().toISOString();
          }
        }
        return recordData;
      };
      for (const record of records) {
        // 构建数据
        const wrappedRecord = templateRecordDataTransformer(record.data);
        const recordModel: DatabaseRecordModel = {
          id: record.id || generateNanoID(CONST_PREFIX_RECORD),
          spaceId,
          databaseId,
          templateId: record.templateId,
          data: wrappedRecord,
          computed: {},
          values: record.values || wrappedRecord,
          subscribers: [],
          revision: 0,
          status: 'OPEN',
          createdBy: userId,
          updatedBy: userId,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        recordModels.push(recordModel);
      }
    }

    return {
      id: databaseId,
      input: databaseCreateInput,
      recordModels,
    };
  }

  /**
   * 默认字段，用户没有传入任何字段时，空字段时系统强制默认创建
   *
   * 可通过config/server/database/default-fields.ts自己配置
   */
  private static buildDefaultFieldsCreateInput(userId: string, spaceId: string, type: DatabaseType | undefined) {
    let databaseFields: DatabaseField[];

    if (type === undefined || type === 'DATUM') databaseFields = DefaultDatumDatabaseFieldsWhenEmptyFields;
    else if (type === 'TASK') databaseFields = FixedTaskDatabaseFields;
    else throw new Error(`Invalid database type on build default fields: ${type}`);

    return this.buildFieldCreateInput(userId, spaceId, databaseFields);
  }

  /**
   * 构造数据库类型表字段创建数据
   * @param userId user id
   * @param spaceId space id
   * @param fields fields in database template
   */
  private static buildFieldCreateInput(
    userId: string,
    spaceId: string,
    fields: DatabaseField[],
  ): Prisma.DatabaseFieldCreateWithoutDatabaseInput[] {
    // 初始化每个字段BO处理器
    const fieldBOProcessorMap: { [id: string]: FieldBOProcessor } = fields.reduce<{
      [id: string]: FieldBOProcessor;
    }>((acc, field) => {
      const processor = FieldBOProcessorFactory.getProcessor(field);
      acc[processor.getId()] = processor;
      return acc;
    }, {});

    // 初始化上下文BO处理器所需的上下文
    const allFields: DatabaseFieldWithId[] = Object.values(fieldBOProcessorMap).map((processor) => ({
      id: processor.getId(),
      ...processor.getBO(),
    }));

    const createInputs: Prisma.DatabaseFieldCreateWithoutDatabaseInput[] = Object.keys(fieldBOProcessorMap).map(
      (fieldId, index) => {
        const processor = fieldBOProcessorMap[fieldId];
        return processor.toPrismaInputWithoutDatabase(userId, spaceId, {
          primary: index === 0,
          ctx: { fields: allFields },
        });
      },
    );

    // TODO: 交由BO处理器来检查
    FormulaHelper.topologicalSort(createInputs as DatabaseFieldWithId[]);

    return createInputs;
  }

  private setTemplateIdWithIdOperation() {
    const operations: PrismaPromise<Prisma.BatchPayload>[] = [];
    if (!this.templateId) {
      operations.push(
        ...[
          db.prisma.database.updateMany({
            where: { id: this.id },
            data: {
              templateId: this.id,
            },
          }),
          db.prisma.node.updateMany({
            where: { id: this.id },
            data: {
              templateId: this.id,
            },
          }),
        ],
      );
    }
    return operations;
  }

  /**
   * 删除表记录的DB会话
   */
  static async deleteRecordSession(spaceId: string, databaseId: string): Promise<MongoTransactionCB> {
    return async (session) => {
      await db.mongo.databaseRecord(spaceId).deleteMany({ spaceId, databaseId }, { session });
    };
  }

  async updateNodeState(): Promise<void> {
    const node = this.toNodeSO();
    const state: NodeStateBO[] = node.state.filter((i) => i.state !== 'NUMBER');
    state.unshift({
      state: 'NUMBER',
      number: await this.getRecordsCount(),
    });
    await NodeSO.updateNodeState(this.id, state);
  }

  /**
   * 删除数据库后的操作
   */
  override async deleteAfter(user: UserSO): Promise<void> {
    const linkFields = this.getOnlyLinkFields();
    for (const linkField of linkFields) {
      // 转换兄弟字段为单行文本
      await linkField.convertBrotherFieldToSingleText(user);
    }
  }

  /**
   * 发送事件到数据库在线用户
   */
  private async sendEventToDatabaseOnlineUsers(
    event: SSEEventDatabaseImportStart | SSEEventDatabaseImportProgress | SSEEventDatabaseImportComplete,
  ): Promise<void> {
    const SseSO = await import('@bika/domains/event/server/sse/sse-so').then((m) => m.SseSO);

    const onlineSessions = await OnlineSessionSO.getByRoute(`/space/${this.spaceId}/node/${this.id}`);

    await Promise.all(onlineSessions.map(async (session) => SseSO.emit(session.userId, event)));
  }
}
