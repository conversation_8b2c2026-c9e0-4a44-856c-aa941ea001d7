import _ from 'lodash';
import {
  FormulaBaseError,
  FormulaExprLexer,
  FormulaHelper,
  TokenType,
  formatErrorMessage,
} from '@bika/domains/database/shared';
import { isArrayOfType, isNullOrUndefined } from '@bika/domains/shared/shared';
import { DatabaseRecordModel } from '@bika/server-orm';
import { DatabaseFieldWithId, BasicValueType, DatabaseFormulaField, CellValue } from '@bika/types/database/bo';
import { iString, iStringMatch, iStringParse, iStringSchema } from '@bika/types/system';
import { divide } from '@bika/types/system/formatting';
import { FormulaInterpreter } from './interpreter';
import { ValueResolverFunction } from './types';
import { CellValueConvertorFactory } from '../../cells/cellvalue-convertor/factory';
import { CellModel } from '../../cells/types';
import { ThinRecord } from '../../thin-record';

export class FormulaUtil {
  /**
   * 公式字段BO的表达式模板转换为真实表达式
   * @param expressionTemplate 公式表达式模板, 变量使用的是 {fieldTemplateId} 的形式
   * @param fields 当前表的所有字段
   * @returns 真实的公式表达式, 变量使用的是 {fieldId} 的形式
   */
  static parseExpressionTemplate(expressionTemplate: string, fields: DatabaseFieldWithId[]): string {
    if (!expressionTemplate) {
      return expressionTemplate;
    }

    const lexer = new FormulaExprLexer(expressionTemplate);
    if (!lexer) {
      // 允许空字符串
      return '';
    }

    // Init map: field templateId -> field id
    const templateId2IdEntries: [string, string][] = fields
      .map((field) => {
        if (field.templateId) {
          return [field.templateId, field.id];
        }
        return null;
      })
      .filter((item): item is [string, string] => item !== null);
    const templateId2IdMap = Object.fromEntries(templateId2IdEntries);

    return lexer.fullMatches.reduce<string>((acc, token) => {
      let tokenValue = token.value;

      function getPureTokenValue() {
        // 将反斜杠去掉，仅保留反斜杠后面的字符
        const templateId = tokenValue.replace(/\\(.)/g, '$1');
        const fieldId = templateId2IdMap[templateId];
        if (!fieldId) {
          return tokenValue;
        }
        return fieldId;
      }

      function getTokenValue() {
        // 将反斜杠去掉，仅保留反斜杠后面的字符, 同时取出 { } 里面的内容
        // 例如 {a} => a
        // 例如 {\\a} => a
        const templateId = tokenValue.slice(1, -1).replace(/\\(.)/g, '$1');
        const fieldId = templateId2IdMap[templateId];
        if (!fieldId) {
          return tokenValue;
        }
        return `{${fieldId}}`;
      }

      if (token.type === TokenType.PureValue) {
        tokenValue = getPureTokenValue();
      }

      if (token.type === TokenType.Value) {
        tokenValue = getTokenValue();
      }

      return acc + tokenValue;
    }, '');
  }

  /**
   * 提供一个方法给公式解析, 通过 fieldId 获取 cellValue, 并转为对应的数据
   * TODO 这里应该只解释字段单元格值的类型, 外面总体拿到各个语法值后根据类型优先级组合计算
   * TODO 彻底重构这个变量解析器, 确保每个字段返回的值应该是什么数据
   */
  static valueResolver(ctx: {
    // 当前表所有字段
    fields: DatabaseFieldWithId[];
    // 当前这一行记录
    record: DatabaseRecordModel;
  }): ValueResolverFunction {
    return (fieldKey: string) => {
      const field = ctx.fields.find((f) => f.id === fieldKey || iStringMatch(f.name, fieldKey));
      if (!field) {
        throw new Error(formatErrorMessage('not found this field key: {value}', { value: fieldKey }));
      }

      // 单元格值转换器
      const cvt = CellValueConvertorFactory.create(field, { fields: ctx.fields });

      const thinRecord = new ThinRecord(ctx.record);

      if (field.type === 'PERCENT') {
        const cellData = thinRecord.getCellData(field.id);
        if (isNullOrUndefined(cellData)) {
          return 0;
        }
        const cellDataAsNumber = Number(cellData);
        return divide(cellDataAsNumber, 100);
      }

      // 得到字段的数据类型
      const fieldBasicValueType = cvt.basicValueType();
      // Currently "", [], false will be converted to null when getCellValue,
      // in order to ensure the correct calculation result of the formula, the boolean type needs to be converted null => false
      if (fieldBasicValueType === BasicValueType.Boolean) {
        // 目标字段是一个布尔值
        const value = thinRecord.getCellValue(field.id);

        // TODO: 临时修复 checkbox 返回值为 0，但是在公式中应该是 false
        if (value === '0') {
          return false;
        }

        return Boolean(value);
      }
      if (fieldBasicValueType === BasicValueType.String) {
        const cellValue = thinRecord.getCellValue(field.id);
        if (isNullOrUndefined(cellValue)) {
          const cellData = thinRecord.getCellData(field.id);
          if (!isNullOrUndefined(cellData)) {
            return cvt.cellValueToString(cellData);
          }
          return null;
        }
        return cvt.cellValueToString(cellValue);
      }
      if (fieldBasicValueType === BasicValueType.Number) {
        return thinRecord.getCellData(field.id) as number;
      }
      if (fieldBasicValueType === BasicValueType.DateTime) {
        return thinRecord.getCellData(field.id) as string;
      }
      if (fieldBasicValueType === BasicValueType.Array) {
        // 数组类型的单元格值: 附件/单多选/成员/公式/关联/引用
        const value = thinRecord.getCellValue(field.id);
        // console.log(`===========> array ${JSON.stringify(value)}`);
        if (isArrayOfType(value, (v) => typeof v === 'string')) {
          // 是字符串数组
          const converted = cvt.cellValueToArray(value) as string[];
          // console.log(`===========> return array string ${JSON.stringify(converted)}`);
          return converted;
        }
        if (isArrayOfType(value, (v) => typeof v === 'object')) {
          // 是iString[]
          const converted = (value as iString[]).map((iStr) => iStringParse(iStr));
          // console.log(`===========> return array iString ${JSON.stringify(converted)}`);
          return converted;
        }
      }
      // 其他类型, 一般不会走到这里
      const value = thinRecord.getCellValue(field.id);
      // console.log(`===========> ${JSON.stringify(value)}`);
      if (!value) {
        return null;
      }
      const parsed = iStringSchema.safeParse(value);
      if (parsed.success) {
        // 可能是 iString
        return iStringParse(parsed.data);
      }
      return value as string;
    };
  }

  /**
   * 计算公式值
   */
  private static evaluate(
    formulaFieldBO: DatabaseFieldWithId<DatabaseFormulaField>,
    databaseFields: DatabaseFieldWithId[],
    record: DatabaseRecordModel,
  ): CellValue {
    const { expression: expr } = formulaFieldBO.property;
    if (!expr) {
      // 公式表达式不存在, 返回空
      return null;
    }
    try {
      const fExpr = FormulaHelper.parse(expr, {
        field: formulaFieldBO,
        fields: databaseFields,
      });
      if (!fExpr.success) {
        // 默认错误返回 null
        return null;
      }
      // 公式翻译
      const interpreter = new FormulaInterpreter({
        field: formulaFieldBO,
        fields: databaseFields,
        record,
      });
      // 解释结果值
      const result = interpreter.visit(fExpr.ast);
      // 处理结果值
      if (_.isNumber(result) && !Number.isFinite(result)) {
        throw new FormulaBaseError('NaN');
      }
      // TODO: 修复类型差异
      return result as CellValue;
    } catch (error) {
      if (error instanceof FormulaBaseError) {
        // 解析错误展示到单元格
        return error.message;
      }
    }

    return null;
  }

  private static transformToCellValues(cellValue: CellValue): string | string[] | undefined {
    if (Array.isArray(cellValue)) {
      return cellValue.map((item) => String(item));
    }
    // return cellValue ? String(cellValue) : undefined;
    return String(cellValue);
  }

  static computeCellModel(
    formulaFieldBO: DatabaseFieldWithId<DatabaseFormulaField>,
    databaseFields: DatabaseFieldWithId[],
    record: DatabaseRecordModel,
  ): CellModel {
    const computed = this.evaluate(formulaFieldBO, databaseFields, record);
    const values = this.transformToCellValues(computed);
    // console.debug(`公式计算值: ${computed} ${typeof computed}, 渲染值: ${values}`);
    return {
      data: undefined, // 公式字段不需要data
      computed,
      values,
    };
  }

  /**
   * @deprecated 待重构, 不应该在这里做
   */
  public static getDependentFields(expression: string) {
    const { valueMap } = new FormulaExprLexer(expression);
    return valueMap;
  }
}
