import assert from 'assert';
import { errors } from '@bika/contents/config/server/error/errors';
import { ServerError } from '@bika/contents/config/server/error/server_error';
import { isArrayOfType } from '@bika/domains/shared/shared';
import { UserSO } from '@bika/domains/user/server';
import { DatabaseRecordModel, db, DistributedOperation, mongoose, PrismaPromise } from '@bika/server-orm';
import { DatabaseLookupField, LookupDataType, LookupFieldProperty, RollUpFuncType } from '@bika/types/database/bo';
import type { CellValue, DatabaseFieldWithId } from '@bika/types/database/bo';
import { DatabaseLookupFieldRO } from '@bika/types/database/ro';
import { ToTemplateOptions } from '@bika/types/node/bo';
import { CellKey, CellModel } from '../cells/types';
import { RecordSO } from '../record-so';
import { FieldSO } from './field-so';
import { LinkFieldSO } from './link-field';
import type { BuildCellModelOptions, UpdateRecordContext } from './types';
import { DatabaseSO } from '../database-so';
import { FieldUtils } from './utils';
import { RecordLinkSO } from '../record-link-so';
import { FieldCellModelMap, FieldCellRenderModel, RecordCellModelMap } from '../types';
import { Aggregate, LookupAggregateOptions } from './aggregate/aggregate';
import { LookupRecord } from './aggregate/types';
import { LinkCellSO } from '../cells/link-cell-so';

export class LookupFieldSO extends FieldSO<DatabaseLookupField> {
  override getQueryFieldKey(): string {
    return `${CellKey.VALUES}.${this.id}`;
  }

  get isAutoInject(): boolean {
    return true;
  }

  getAggregateOption(): LookupAggregateOptions {
    return {
      rollUpType: this.property.rollUpType ?? 'VALUES',
      lookUpLimit: this.property.lookUpLimit ?? 'ALL',
    };
  }

  override assertValueType(_input: Exclude<CellValue, null>): boolean {
    // 输入忽略
    return true;
  }

  override initializeCellModelOnUpdate(_context: UpdateRecordContext): CellModel {
    // 更新记录时, 不接受输入值, 不初始化
    throw new Error(`the type of field [${this.id}] does not support auto initialization of cell value: ${this.type}`);
  }

  override buildCellModel(_options: BuildCellModelOptions): CellModel {
    // 创建记录时先暂停计算, 后面刷新
    throw new Error(`the type of field [${this.id}] does not support input cell: ${this.type}`);
  }

  override getFakeCellData() {
    const dataType = this.property.dataType ?? 'STRING';

    switch (dataType) {
      case 'BOOLEAN':
        return { data: false, value: 'false' };
      case 'NUMBER':
        return { data: 0, value: '0' };
      case 'DATETIME': {
        const data = new Date().toISOString();
        return { data, value: data };
      }
      default:
        return { data: '', value: '' };
    }
  }

  override sortField(): CellKey {
    return CellKey.VALUES;
  }

  override toTemplate(opts?: ToTemplateOptions): DatabaseLookupField {
    const bo = super.toTemplate(opts);
    const property = bo.property as LookupFieldProperty;
    if (opts?.getTemplateId && !property.relatedLinkFieldTemplateId && property.relatedLinkFieldId) {
      property.relatedLinkFieldTemplateId = opts.getTemplateId(property.relatedLinkFieldId);
      property.relatedLinkFieldId = undefined;
    }
    if (opts?.getTemplateId && !property.lookupTargetFieldTemplateId && property.lookupTargetFieldId) {
      property.lookupTargetFieldTemplateId = opts.getTemplateId(property.lookupTargetFieldId);
      property.lookupTargetFieldId = undefined;
    }
    if (opts?.getTemplateId && !property.databaseTemplateId && property.databaseId) {
      property.databaseTemplateId = opts.getTemplateId(property.databaseId);
      property.databaseId = undefined;
    }
    return { ...bo, property } as DatabaseLookupField;
  }

  protected override shouldUpdateCells(other: LookupFieldProperty): boolean {
    // 是否改变了引用配置, 任何以下配置变动都将发生整列数据变化
    return (
      this.property.relatedLinkFieldId !== other.relatedLinkFieldId ||
      this.property.lookupTargetFieldId !== other.lookupTargetFieldId ||
      this.property.rollUpType !== other.rollUpType ||
      this.property.lookUpLimit !== other.lookUpLimit
    );
  }

  protected override shouldCheckCircularReference(other: LookupFieldProperty): boolean {
    // 只要切换表或切换引用字段,就会触发循环引用检查
    return (
      this.property.relatedLinkFieldId !== other.relatedLinkFieldId ||
      this.property.lookupTargetFieldId !== other.lookupTargetFieldId
    );
  }

  getLookupTargetFieldId(): string | undefined {
    return this.property.lookupTargetFieldId;
  }

  async getLookupTargetField(): Promise<FieldSO | undefined> {
    const relatedLinkFieldId = this.getRelatedLinkFieldId();
    const lookupTargetFieldId = this.getLookupTargetFieldId();
    // 确保参数可用
    if (!relatedLinkFieldId || !lookupTargetFieldId) {
      // 无法重建记录
      throw new Error('fail to refresh cells when lookup field property changed');
    }
    const database = await this.getDatabase();
    const relateLinkField = database.getFieldByFieldKey(relatedLinkFieldId) as LinkFieldSO;
    const relateDatabase = await relateLinkField.getForeignDatabase();
    if (!relateDatabase) {
      throw new ServerError(errors.database.relate_database_not_found);
    }
    return relateDatabase.findFieldByFieldKey(lookupTargetFieldId);
  }

  getRelatedLinkFieldId(): string | undefined {
    return this.property.relatedLinkFieldId;
  }

  async getRelatedLinkField(): Promise<LinkFieldSO | undefined> {
    const relatedLinkFieldId = this.getRelatedLinkFieldId();
    if (!relatedLinkFieldId) {
      return undefined;
    }
    const database = await this.getDatabase();
    const linkField = database.getFieldByFieldKey(relatedLinkFieldId);
    return linkField as LinkFieldSO;
  }

  /**
   * 查找引用字段属性变更特殊处理
   * 1. 直接按着变更的配置属性来验证一遍再落库
   */
  protected async buildUpdatePropertyOperation(
    user: UserSO,
    updateFieldBO: DatabaseLookupField,
  ): Promise<DistributedOperation> {
    // 是否发生变更
    if (!this.shouldUpdateCells(updateFieldBO.property)) {
      // 配置没发生变化, 直接返回更改即可
      return super.buildUpdatePropertyOperation(user, updateFieldBO);
    }
    // 也许变更情况: 变换关联表+变换引用字段+变更聚合方式(rollUpType, lookUpLimit)
    const prismaOperations: PrismaPromise<unknown>[] = [];
    const { relatedLinkFieldId, lookupTargetFieldId, rollUpType = 'VALUES' } = updateFieldBO.property;
    if (!relatedLinkFieldId || !lookupTargetFieldId) {
      throw new Error('lookup field property is required');
    }
    // 引用当前表的关联字段所属表
    const database = await this.getDatabase();
    const relateLinkField = database.getFieldByFieldKey(relatedLinkFieldId);
    if (relateLinkField.type !== 'LINK') {
      throw new Error(`relatedLinkFieldId ${relateLinkField.id} is not a link type field`);
    }
    const relateDatabase = await (relateLinkField as LinkFieldSO).getForeignDatabase();
    if (!relateDatabase) {
      throw new ServerError(errors.database.relate_database_not_found);
    }

    // 引用的字段
    const lookupTargetField = relateDatabase.findFieldByFieldKey(lookupTargetFieldId);
    if (!lookupTargetField) {
      throw new Error(`lookupTargetFieldId [${lookupTargetFieldId}] not found`);
    }
    const lookupTargetFieldType = lookupTargetField.type;

    // 推断存储的数据类型
    const dataType = this.inferLookupDataType(rollUpType) ?? lookupTargetField.inferLookupedDataType();

    prismaOperations.push(
      this.getUpdateModelOperation(user.id, {
        ...updateFieldBO,
        property: { ...updateFieldBO.property, lookupTargetFieldType, dataType },
      }),
    );

    return { prismaOperations, mongoOperations: [] };
  }

  /**
   * @deprecated 待重构
   */
  private inferLookupDataType(rollUpType: RollUpFuncType): LookupDataType | undefined {
    const ROLL_UP_BOOLEAN_TYPES = new Set(['AND', 'OR', 'XOR']);
    if (ROLL_UP_BOOLEAN_TYPES.has(rollUpType)) {
      return 'BOOLEAN';
    }
    const ROLL_UP_NUMBER_TYPES = new Set(['AVERAGE', 'COUNT', 'COUNTA', 'COUNTALL', 'SUM', 'MIN', 'MAX']);
    if (ROLL_UP_NUMBER_TYPES.has(rollUpType)) {
      return 'NUMBER';
    }
    const ROLL_UP_STRING_TYPES = new Set(['CONCATENATE', 'ARRAYJOIN']);
    if (ROLL_UP_STRING_TYPES.has(rollUpType)) {
      return 'STRING';
    }
    const ROLL_UP_ARRAY_TYPES = new Set(['VALUES', 'ARRAYUNIQUE', 'ARRAYCOMPACT']);
    if (!ROLL_UP_ARRAY_TYPES.has(rollUpType)) {
      throw new Error(`Invalid rollUpType [${rollUpType}]`);
    }
    return undefined;
  }

  /**
   * @returns 有引用时，填充最终引用字段的信息，否则直接返回 BO
   */
  public override async toRO(): Promise<DatabaseLookupFieldRO> {
    const currentDatabase = await this.getDatabase();
    const fields = currentDatabase.getFields();

    const bo = this.toBO() as DatabaseLookupField & { id: string };

    let lookupTargetField: DatabaseLookupField = bo;
    assert(lookupTargetField.id, 'lookupTargetField.id is required');

    // 如果 lookupTargetField 是 LOOKUP 类型，需要递归查找到最终的字段
    // 注意：需要检查是否存在循环引用
    const visited = new Set<string>([lookupTargetField.id]);
    for (;;) {
      const { relatedLinkFieldId, lookupTargetFieldId } = lookupTargetField.property;

      // 没有关联目标字段，直接返回 BO
      if (!lookupTargetFieldId) {
        return bo;
      }

      // 关联字段不存在，直接返回 BO
      const relatedLinkField = fields.find((f) => f.id === relatedLinkFieldId);
      if (!relatedLinkField || !FieldUtils.isLinkFieldSO(relatedLinkField)) {
        return bo;
      }
      assert(relatedLinkField.property.foreignDatabaseId, "relatedLinkField's foreignDatabaseId is required");

      // 关联的数据库不存在，直接返回 BO
      const database = await DatabaseSO.getDatabaseById(relatedLinkField.property.foreignDatabaseId);
      if (!database) {
        return bo;
      }

      // 引用的字段不存在，直接返回 BO
      const targetFieldSO = database.findFieldByFieldKey(lookupTargetFieldId);
      if (!targetFieldSO) {
        return bo;
      }
      const targetField = targetFieldSO.toBO();

      // 循环引用检查
      // 该 field 已经被查找过，直接返回 BO
      if (visited.has(targetField.id)) {
        return bo;
      }
      visited.add(targetField.id);

      // 如果 lookupTargetField 不是 LOOKUP 类型，直接返回
      if (targetField.type !== 'LOOKUP') {
        const render: DatabaseLookupFieldRO['render'] = {
          lookupFinalField: {
            ...targetField,
            databaseId: database.id,
          },
        };

        // TODO: 待前端不再使用后移除
        // @deprecated
        const property: DatabaseLookupFieldRO['property'] = {
          ...bo.property,
          lookupTargetFieldType: targetField.type,
        };

        return {
          ...bo,
          property,
          render,
        };
      }

      lookupTargetField = targetField;
    }
  }

  override async getDependencies(): Promise<FieldSO[]> {
    const relatedLinkFieldId = this.getRelatedLinkFieldId();
    const lookupTargetFieldId = this.getLookupTargetFieldId();
    if (!relatedLinkFieldId || !lookupTargetFieldId) {
      return [];
    }
    // 获取对应关联字段
    const database = await this.getDatabase();
    const relateLinkField = database.findFieldByFieldKey(relatedLinkFieldId);
    if (!relateLinkField || relateLinkField.type !== 'LINK') {
      return [];
    }
    // 获取关联表
    const relateDatabase = await (relateLinkField as LinkFieldSO).getForeignDatabase();
    if (!relateDatabase) {
      return [];
    }
    const lookupTargetField = relateDatabase.getFieldByFieldKey(lookupTargetFieldId);
    return [lookupTargetField];
  }

  /**
   * 重新刷新指定行数的单元格
   */
  async reloadCells(
    user: UserSO | null,
    recordIds: string[], // 本表的记录
    options?: { session?: mongoose.ClientSession },
  ): Promise<void> {
    const lookupTargetFieldId = this.getLookupTargetFieldId();
    if (!lookupTargetFieldId) {
      // 没有查找目标字段, 跳过
      return;
    }
    const lookupTargetField = await this.getLookupTargetField();
    if (!lookupTargetField) {
      // 没有查找目标字段, 跳过
      return;
    }
    const relatedLinkField = await this.getRelatedLinkField();
    if (!relatedLinkField) {
      // 没有关联字段, 跳过
      return;
    }
    const brotherField = await relatedLinkField.getBrotherField();
    if (!brotherField) {
      // 没有兄弟字段, 跳过
      return;
    }
    // console.debug(`刷新本表记录: ${recordIds}`);
    // 关联表的记录
    // console.debug(`关联表字段: ${relatedLinkField.name}(${relatedLinkField.id})`);
    const linkRecordIds = await RecordLinkSO.getLinkRecordIds(relatedLinkField.id, recordIds, {
      session: options?.session,
    });
    // console.debug(`关联表记录: ${JSON.stringify(linkRecordIds)}`);
    const foreignDatabase = await relatedLinkField.getForeignDatabase();
    const linkRecords = await foreignDatabase?.getRecords(Array.from(new Set(linkRecordIds)), options?.session);
    const bulkUpdates: mongoose.AnyBulkWriteOperation<DatabaseRecordModel>[] = [];
    const updatedRecordIds: string[] = [];
    // 遍历更新本表记录刷新
    const database = await this.getDatabase();
    const toUpdateRecords = await database.getRecords(recordIds, options?.session);
    for (const toUpdateRecord of toUpdateRecords) {
      // 关联表的记录ID
      const relatedLinkFieldCell = toUpdateRecord.getCellSO(relatedLinkField.id) as LinkCellSO;
      const relatedRecordIds = relatedLinkFieldCell.getData();
      // console.debug(`[${this.name}] 刷新记录关联记录: ${JSON.stringify(relatedRecordIds)}`);
      // const linkRecordIds = cell.getModelData();
      if (isArrayOfType(relatedRecordIds, (v) => typeof v === 'string')) {
        // 引用的记录
        const lookupRecords: LookupRecord[] = await Promise.all(
          relatedRecordIds.map(async (relatedRecordId) => {
            const linkRecord = linkRecords?.find((linkedRecord) => linkedRecord.id === relatedRecordId);
            const cellRenderModel = await linkRecord?.getCellRenderModel(lookupTargetField.id);
            const fieldCellModel: FieldCellRenderModel = {
              bo: lookupTargetField.toBO(),
              cell: cellRenderModel ?? {},
            };
            return [toUpdateRecord.id, lookupTargetField.id, fieldCellModel];
          }),
        );

        if (lookupRecords.length === 0) {
          // 没有依赖的单元格值, 清空引用值
          const fieldCellModelMap: FieldCellModelMap = { [this.id]: { bo: this.toBO(), cell: {} } };
          const updateOne = toUpdateRecord.buildUpdateQuery(user, fieldCellModelMap);
          if (updateOne) {
            bulkUpdates.push({ updateOne });
            updatedRecordIds.push(toUpdateRecord.id);
          }
          continue;
        }
        // 引用+聚合计算
        const aggregate = new Aggregate(lookupTargetField.toBO(), this.getAggregateOption());
        const cellModel = aggregate.compute(lookupRecords);
        const fieldCellModelMap: FieldCellModelMap = { [this.id]: { bo: this.toBO(), cell: cellModel } };
        const updateOne = toUpdateRecord.buildUpdateQuery(user, fieldCellModelMap);
        if (updateOne) {
          bulkUpdates.push({ updateOne });
          updatedRecordIds.push(toUpdateRecord.id);
        }
      }
    }
    // console.debug(`[${this.name}] 引用字段依赖的记录: ${JSON.stringify(linkRecords?.map((r) => r.model))}`);
    // console.debug(`[${this.name}] 依赖的字段ID: ${lookupTargetField.id}`);
    // console.debug(`[${this.name}] 依赖的字段名: ${lookupTargetField.name}`);
    // console.debug(`更新语句: ${JSON.stringify(bulkUpdates)}`);

    // 执行更新
    await db.mongo.databaseRecord(this.spaceId).bulkWrite(bulkUpdates, { session: options?.session });

    // 重新获取最新的记录
    const updatedRecordModels = await db.mongo
      .databaseRecord(this.spaceId)
      .find({
        databaseId: this.databaseId,
        id: { $in: updatedRecordIds },
      })
      .session(options?.session || null);
    // console.debug(`[${this.name}] 引用字段刷新后的值: ${JSON.stringify(updatedRecordModels)}`);

    // 执行依赖更新
    const dependentFields = await this.getDependent();
    for (const dependentField of dependentFields) {
      console.debug(`触发引用字段${this.name}的依赖更新: ${dependentField.name}`);
      await dependentField.updateCells(user, this.toBO(), updatedRecordModels, { session: options?.session });
    }
  }

  /**
   * 重新刷新指定行数的单元格
   */
  async refreshCells(
    _user: UserSO,
    records: RecordSO[],
    updateFieldBO: DatabaseLookupField,
    options?: { session?: mongoose.ClientSession },
  ): Promise<RecordCellModelMap> {
    // 重建引用单元格, 批量查询记录
    const {
      relatedLinkFieldId,
      lookupTargetFieldId,
      rollUpType = 'VALUES',
      lookUpLimit = 'ALL',
    } = updateFieldBO.property;
    // 确保参数可用
    if (!relatedLinkFieldId || !lookupTargetFieldId) {
      // 无法重建记录
      throw new Error('fail to refresh cells when lookup field property changed');
    }
    const database = await this.getDatabase();
    const relateLinkField = database.getFieldByFieldKey(relatedLinkFieldId) as LinkFieldSO;
    const relateDatabase = await relateLinkField.getForeignDatabase();
    if (!relateDatabase) {
      throw new ServerError(errors.database.relate_database_not_found);
    }
    // const relatedDatabaseFields = relateDatabase.getFields();
    const lookupTargetField = relateDatabase.getFieldByFieldKey(lookupTargetFieldId);
    const recordCellModelMap: RecordCellModelMap = {};
    // 每一行里对应关联字段的单元格数据是一个数组, 里面存储的是关联表的记录ID
    const recordCellMap = records.reduce<{ [recordId: string]: string[] }>((acc, record) => {
      const cell = record.getCellSO(relatedLinkFieldId);
      const recordIds = cell.getModelData();
      if (isArrayOfType(recordIds, (v) => typeof v === 'string') && recordIds.length > 0) {
        acc[record.id] = recordIds;
      }
      return acc;
    }, {});
    // 去重后一起查询
    const linkedRecords = await relateDatabase.getRecords(
      Array.from(new Set(Object.values(recordCellMap).flat())),
      options?.session,
    );
    // 只更新有数据的行
    for (const [recordId, linkedRecordIds] of Object.entries(recordCellMap)) {
      const lookupRecords: LookupRecord[] = await Promise.all(
        linkedRecordIds.map(async (linkedRecordId) => {
          const record = linkedRecords.find((linkedRecord) => linkedRecord.id === linkedRecordId);
          const cellRenderModel = await record?.getCellRenderModel(lookupTargetFieldId);
          const fieldCellModel: FieldCellRenderModel = {
            bo: lookupTargetField.toBO(),
            cell: cellRenderModel ?? {},
          };
          return [linkedRecordId, lookupTargetFieldId, fieldCellModel];
        }),
      );
      const aggregate = new Aggregate(lookupTargetField.toBO(), { rollUpType, lookUpLimit });
      const cellModel = aggregate.compute(lookupRecords);
      const record = records.find((r) => r.id === recordId);
      if (record) {
        recordCellModelMap[record.id] = { [this.id]: { bo: updateFieldBO, cell: cellModel } };
      }
    }
    return recordCellModelMap;
  }

  /**
   * 引用的字段单元格值更新后触发当前字段的单元格值更新
   * 依赖传入的单元格值重新初始化
   */
  override async updateCellsWithDepend(
    user: UserSO | null,
    fromField: DatabaseFieldWithId, // 这是引用的对面表字段
    dependRecords: DatabaseRecordModel[], // 这是对面表的关联记录
    options?: { session?: mongoose.ClientSession },
  ): Promise<DatabaseRecordModel[]> {
    console.log(`依赖更新: 引用字段名称 ${this.name}`);
    const relatedLinkFieldId = this.getRelatedLinkFieldId();
    const lookupTargetFieldId = this.getLookupTargetFieldId();
    if (!relatedLinkFieldId || !lookupTargetFieldId) {
      return [];
    }
    if (lookupTargetFieldId !== fromField.id) {
      // 不是引用的对面表字段, 不需要更新
      throw new Error(
        `lookup field [${this.id}] can only update when the fromField is the lookup target field [${lookupTargetFieldId}]`,
      );
    }
    // 依赖表记录ID
    // console.debug(`引用依赖的数据: ${JSON.stringify(dependRecords)}`);
    const dependRecordIds = dependRecords.map((record) => record.id);
    if (dependRecordIds.length === 0) {
      // 没有引用的记录, 直接返回
      console.warn(`lookup field [${this.id}] has no depend records, skip update`);
      return [];
    }
    const database = await this.getDatabase();
    const relateLinkField = await this.getRelatedLinkField();
    if (!relateLinkField) {
      // 没有关联字段, 无法更新
      console.warn(`lookup field [${this.id}] has no related link field, skip update`);
      return [];
    }
    // 对面表的兄弟字段
    const brotherField = await relateLinkField.getBrotherField();
    if (!brotherField) {
      // 兄弟字段不存在?
      console.warn(`lookup field [${this.id}] has no brother field, skip update`);
      return [];
    }
    const foreignDatabase = await relateLinkField.getForeignDatabase();
    if (!foreignDatabase) {
      // 没有关联表, 无法更新
      console.warn(`lookup field [${this.id}] has no foreign database, skip update`);
      return [];
    }
    // 引用字段只会依赖双向关联表的指定字段
    const dependencies = await this.getDependencies();
    if (dependencies.length === 0 || dependencies.length > 1) {
      // 引用只会依赖一个字段
      console.warn(`lookup field [${this.id}] depend on more than one field, it is illegal`);
      return [];
    }
    // 对面表的目标字段
    // const lookupTargetField = dependencies[0];
    const updatedRecords: DatabaseRecordModel[] = [];
    const bulkUpdates: mongoose.AnyBulkWriteOperation<DatabaseRecordModel>[] = [];
    // console.debug(`根据关联字段ID(${brotherField.id})和依赖记录ID(${dependRecordIds})查询关联记录`);
    // 本表记录
    const recordIds = await RecordLinkSO.getLinkRecordIds(brotherField.id, dependRecordIds, {
      session: options?.session,
    });
    // console.debug(`关联记录ID: ${JSON.stringify(recordIds)}`);
    if (recordIds.length === 0) {
      // 没有关联记录, 不需要更新
      return [];
    }
    const toUpdateRecords = await database.getRecords(Array.from(new Set(recordIds)), options?.session);
    // console.debug(`需要更新的记录: ${JSON.stringify(toUpdateRecords.map((r) => r.model))}`);
    // 只更新有数据的行
    for (const toUpdateRecord of toUpdateRecords) {
      // 使用的关联字段单元格
      const relatedLinkFieldCell = toUpdateRecord.getCellSO(relatedLinkFieldId);
      // 关联了多少记录
      const linkRecordIds = relatedLinkFieldCell.getModelData();
      if (isArrayOfType(linkRecordIds, (v) => typeof v === 'string') && linkRecordIds.length > 0) {
        // 查到原来所有记录
        const linkRecords = await foreignDatabase?.getRecords(linkRecordIds, options?.session);
        const idToRecordMap = new Map(linkRecords.map((record: RecordSO) => [record.id, record]));
        // 引用的记录
        const lookupRecords: LookupRecord[] = await Promise.all(
          linkRecordIds.map(async (linkRecordId) => {
            const linkRecord = idToRecordMap.get(linkRecordId);
            const cellModel = await linkRecord?.getCellRenderModel(lookupTargetFieldId);
            return [linkRecordId, fromField.id, { bo: fromField, cell: cellModel ?? {} }];
          }),
        );
        // console.debug(`引用原始数据: ${JSON.stringify(lookupRecords)}`);
        if (lookupRecords.length === 0) {
          // 没有依赖的单元格值, 直接跳过
          continue;
        }
        // 引用+聚合计算
        const aggregate = new Aggregate(fromField, this.getAggregateOption());
        const cellModel: CellModel = aggregate.compute(lookupRecords);
        const fieldCellModelMap: FieldCellModelMap = { [this.id]: { bo: this.toBO(), cell: cellModel } };
        const updateOne = toUpdateRecord.buildUpdateQuery(user, fieldCellModelMap);
        if (updateOne) {
          bulkUpdates.push({ updateOne });
          const recordModel = toUpdateRecord.mergeCellModelToNewModel(fieldCellModelMap);
          updatedRecords.push(recordModel);
        }
      }
    }

    // 执行更新
    await db.mongo.databaseRecord(this.spaceId).bulkWrite(bulkUpdates, { session: options?.session });
    console.log(`结束依赖更新: 引用字段名称 [${this.name}]`);
    // console.debug(`更新的记录: ${JSON.stringify(updatedRecords)}`);
    return updatedRecords;
  }
}
