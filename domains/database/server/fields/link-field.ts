// eslint-disable-next-line max-classes-per-file
import { errors } from '@bika/contents/config/server/error';
import { ServerError } from '@bika/contents/config/server/error/server_error';
import { convertStringToArray } from '@bika/domains/shared/server';
import { isArrayOfType, isNullOrUndefined, isStringArray } from '@bika/domains/shared/shared';
import { UserSO } from '@bika/domains/user/server';
import {
  DatabaseRecordLinkModel,
  DatabaseRecordModel,
  db,
  DistributedOperation,
  mongoose,
  MongoTransactionCB,
  PrismaPromise,
} from '@bika/server-orm';
import {
  DatabaseField,
  DatabaseFieldWithId,
  DatabaseLinkField,
  DatabaseOneWayLinkField,
  DatabaseSingleTextField,
  LinkFieldProperty,
  OneWayLinkFieldProperty,
  CellValue,
} from '@bika/types/database/bo';
import { ExportBOOptions, ToTemplateOptions } from '@bika/types/node/bo';
import { FieldSO } from './field-so';
import type {
  BuildCellModelOptions,
  RecordChanges,
  UpdateCellModelOptions,
  UpdateFieldRecordCell,
  UpdateRecordContext,
} from './types';
import { LinkCellSO } from '../cells/link-cell-so';
import { CellKey, CellModel } from '../cells/types';
import { DatabaseSO } from '../database-so';
import { RecordLinkSO } from '../record-link-so';
import { RecordSO } from '../record-so';
import { FieldBOProcessorFactory } from './bo/factory';
import { LookupFieldSO } from './lookup-field';
import { FieldUtils } from './utils';
import { FieldCellModelMap, RecordCellModelMap } from '../types';

abstract class AbstractLinkFieldSO<T extends DatabaseLinkField | DatabaseOneWayLinkField> extends FieldSO<T> {
  private foreignDatabase: DatabaseSO | null = null;

  getForeignDatabaseId(): string | undefined {
    return this.property.foreignDatabaseId;
  }

  /**
   * 获取关联所在的表
   */
  async getForeignDatabase(): Promise<DatabaseSO | null> {
    if (!this.foreignDatabase) {
      const foreignDatabaseId = this.getForeignDatabaseId();
      if (!foreignDatabaseId) {
        throw new ServerError(errors.database.relate_database_not_found);
      }
      this.foreignDatabase = await DatabaseSO.getDatabaseById(foreignDatabaseId);
    }
    return this.foreignDatabase;
  }

  override sortField(): CellKey {
    return CellKey.VALUES;
  }

  override getQueryFieldKey(): string {
    return `${CellKey.DATA}.${this.id}`;
  }

  override convertToCellValue(value: string | string[]): string[] | null {
    const values = Array.isArray(value) ? value : [value];
    const recordIds = values.flatMap((val) => convertStringToArray(val));
    return recordIds.length > 0 ? recordIds : null;
  }

  override getFakeCellData(): { data: string[]; value: string[] } {
    return { data: ['record_id'], value: ['record_title'] };
  }

  override buildCellModel(options: BuildCellModelOptions): { data?: string[]; values?: (string | null)[] } {
    // 输入值是关联表的记录ID数组
    const { input } = options;
    if (!isStringArray(input)) {
      // 不是字符串数组, 不处理
      return { data: [], values: [] };
    }
    // 去重保存, 这里不能产生IO去获取关联表的首列值, 只初始化记录ID保持, 后续统一加载, 以防一行行去不断IO操作
    return { data: Array.from(new Set(input)) };
  }

  override buildUpdateCellModel(
    options: UpdateCellModelOptions,
  ): { data?: string[]; values?: (string | null)[] } | undefined {
    const { input } = options;
    if (!isStringArray(input)) {
      // 不是字符串数组, 不处理
      return { data: [], values: [] };
    }
    // 去重保存, 这里不能产生IO去获取关联表的首列值, 只初始化记录ID保持, 后续统一加载, 以防一行行去不断IO操作
    return { data: Array.from(new Set(input)) };
  }

  override exportBO(opts?: ExportBOOptions): DatabaseFieldWithId {
    const bo = super.exportBO(opts);
    const property = bo.property as LinkFieldProperty;
    const { foreignDatabaseId } = property;
    if (foreignDatabaseId && !opts?.getInstanceId?.(foreignDatabaseId)) {
      throw new ServerError(errors.database.linked_database_not_current_folder, {
        foreignDatabaseId,
      });
    }
    return bo;
  }

  async buildDeleteOperations(user: UserSO): Promise<DistributedOperation> {
    const operation = await super.buildDeleteOperations(user);
    // 将本表所有使用本字段作为引用字段依赖的属性重置掉
    const resetLookupFieldOperations = await this.resetRelatedLookupFieldsOperations();
    operation.prismaOperations.push(...resetLookupFieldOperations);
    return operation;
  }

  /**
   * 重置在表里面引用到的查找引用字段单元格
   */
  async resetRelatedLookupFieldsOperations(): Promise<PrismaPromise<unknown>[]> {
    // 获取所有引用到本字段的查找字段
    const relatedLookupFields = await this.getRelatedLookupFields();
    // 重置引用字段的属性
    return relatedLookupFields.map((lookupField) => {
      const updateProperty = { ...lookupField.property, relatedLinkFieldId: undefined, lookupTargetFieldId: undefined };
      // console.log(`=========> 重置前: ${lookupField.id} ${JSON.stringify(lookupField.property)}`);
      // console.log(`=========> 重置引用字段的属性: ${lookupField.id} ${JSON.stringify(updateProperty)}`);
      return db.prisma.databaseField.update({
        where: {
          id: lookupField.id,
          databaseId: this.databaseId,
        },
        data: {
          property: updateProperty,
        },
      });
    });
  }

  /**
   * 获取使用当前关联的查找引用字段列表
   */
  async getRelatedLookupFields(): Promise<LookupFieldSO[]> {
    const database = await this.getDatabase();
    const fields = database.getFields();
    return fields.filter(
      (field): field is LookupFieldSO =>
        FieldUtils.isLookupFieldSO(field) && field.property.relatedLinkFieldId === this.id,
    );
  }
}

/**
 * 双向关联字段
 */
export class LinkFieldSO extends AbstractLinkFieldSO<DatabaseLinkField> {
  private brotherFieldIsExists: boolean | null = null;

  private needConvertBrotherField = false;

  getBrotherFieldId(): string | undefined {
    return this.property.brotherFieldId;
  }

  async getBrotherField(): Promise<LinkFieldSO | null> {
    const originRelateDatabase = await this.getForeignDatabase();
    const brotherFieldId = this.getBrotherFieldId();
    if (originRelateDatabase && brotherFieldId) {
      const originBrotherField = originRelateDatabase.findFieldByFieldKey(brotherFieldId);
      if (originBrotherField && FieldUtils.isOnlyLinkFieldSO(originBrotherField)) {
        return originBrotherField;
      }
    }
    return null;
  }

  override toTemplate(opts?: ToTemplateOptions): DatabaseLinkField {
    const bo = super.toTemplate(opts);
    if (!opts?.getTemplateId) {
      return bo as DatabaseLinkField;
    }
    const property = bo.property as LinkFieldProperty;
    if (property.foreignDatabaseId && !opts.getTemplateId(property.foreignDatabaseId)) {
      throw new ServerError(errors.database.linked_database_not_current_folder, {
        foreignDatabaseId: property.foreignDatabaseId,
      });
    }
    if (property.foreignDatabaseId) {
      property.foreignDatabaseTemplateId = opts.getTemplateId(property.foreignDatabaseId);
      property.foreignDatabaseId = undefined;
    }
    if (property.brotherFieldId) {
      property.brotherFieldTemplateId = opts.getTemplateId(property.brotherFieldId);
      property.brotherFieldId = undefined;
    }
    return { ...bo, property } as DatabaseLinkField;
  }

  override assertValueType(input: Exclude<CellValue, null>): boolean {
    // 只能字符串数组, 允许多个
    return this.isCellValueValidStringArray(input, true);
  }

  override initializeCellModelOnUpdate(_context: UpdateRecordContext): CellModel {
    // 更新记录时, 不接受输入值, 不初始化
    throw new Error(`the type of field [${this.id}] does not support auto initialization of cell value: ${this.type}`);
  }

  override async reloadCellModel(
    user: UserSO | null,
    recordModel: DatabaseRecordModel,
    options?: { session?: mongoose.ClientSession },
  ): Promise<DatabaseRecordModel> {
    const newRecordModel = { ...recordModel };
    const input = recordModel.data[this.id];
    if (isNullOrUndefined(input)) {
      // 没有输入值, 也就是没有关联, 跳过
      return newRecordModel;
    }
    // 有关联关系, 处理
    const linkRecordIds = input as string[];
    console.debug(`关联记录输入值: ${JSON.stringify(linkRecordIds)}`);
    const foreignDatabase = await this.getForeignDatabase();
    const brotherField = await this.getBrotherField();
    if (!brotherField || !foreignDatabase || !foreignDatabase) {
      // 没有关联记录或主字段, 跳过
      console.warn(`关联表或对面表的双向关联字段不存在, 跳过: ${this.id}`);
      return newRecordModel;
    }
    // 对面表的记录
    const foreignDatabaseRecords = await foreignDatabase.getRecords(linkRecordIds, options?.session);
    // console.debug(`对面表的记录: ${JSON.stringify(foreignDatabaseRecords.map((r) => r.model))}`);
    // 绑定关联记录
    const bulkUpdates: mongoose.AnyBulkWriteOperation<DatabaseRecordModel>[] = [];
    const recordLinkModels: DatabaseRecordLinkModel[] = [];
    // 遍历对面关联表记录(一定要按照原来的顺序)
    const primaryValues: (string | null)[] = [];
    for (const linkRecordId of linkRecordIds) {
      const foreignDatabaseRecord = foreignDatabaseRecords.find((record) => record.id === linkRecordId);
      if (!foreignDatabaseRecord) {
        throw new Error(`input record id ${linkRecordId} not found`);
      }
      // 对面表的记录追加当前记录ID
      const modelData = foreignDatabaseRecord.getCellData(brotherField.id);
      const modelValues = foreignDatabaseRecord.getCellValue(brotherField.id);
      const recordIdList: string[] = modelData ? (modelData as string[]) : [];
      recordIdList.push(newRecordModel.id);
      const cell: CellModel = { data: recordIdList, values: modelValues ?? undefined };
      const updateOne = foreignDatabaseRecord.buildUpdateQuery(user, {
        [brotherField.id]: { bo: brotherField.toBO(), cell },
      });
      if (updateOne) {
        bulkUpdates.push({ updateOne });
        recordLinkModels.push({
          // 关联字段ID
          fieldId: this.id,
          // 当前表的记录
          recordId: newRecordModel.id,
          // 对面关联字段ID
          brotherFieldId: brotherField.id,
          // 对面表的记录
          brotherRecordId: foreignDatabaseRecord.id,
        });
      }

      // 主字段值(应该没有IO操作才对)
      const primaryCellValue = foreignDatabaseRecord.getPrimaryCellValue();
      if (isArrayOfType(primaryCellValue, (v) => typeof v === 'string')) {
        primaryValues.push(primaryCellValue.join(', '));
      } else {
        primaryValues.push(primaryCellValue as string | null);
      }
    }
    await db.mongo.databaseRecord(this.spaceId).bulkWrite(bulkUpdates, { session: options?.session });
    await RecordLinkSO.createMany(recordLinkModels, { session: options?.session });

    console.debug(`关联记录的主字段值: ${JSON.stringify(primaryValues)}`);
    return { ...newRecordModel, values: { ...newRecordModel.values, [this.id]: primaryValues } };
  }

  /**
   * 指定行数里追加单元格的关联记录
   */
  async addLinkRecord(
    user: UserSO | null,
    updateRecordCellMap: Record<string /** record id */, string[]>,
    options?: { session?: mongoose.ClientSession },
  ): Promise<void> {
    // 单元格值必须是字符串数组, 空则代表清空
    const recordIds = Object.keys(updateRecordCellMap);
    if (recordIds.length === 0) {
      // 没有记录需要更新
      return;
    }
    const database = await this.getDatabase();
    const records = await database.getRecords(recordIds, options?.session);
    if (records.length === 0) {
      // 没有记录需要更新
      console.warn(`没有需要更新的记录: ${this.id}`);
      return;
    }
    const foreignDatabase = await this.getForeignDatabase();
    const brotherField = await this.getBrotherField();
    if (!foreignDatabase || !brotherField) {
      // 没有关联表, 跳过
      console.warn(`关联表不存在, 跳过: ${this.id}`);
      return;
    }
    const bulkUpdates: mongoose.AnyBulkWriteOperation<DatabaseRecordModel>[] = [];
    for (const record of records) {
      // 添加的记录ID列表
      const addRecordIds = updateRecordCellMap[record.id];
      // 追加对应的记录ID
      const linkCell = record.getCellSO(this.id) as LinkCellSO;
      const beforeRecordIds = linkCell.getData() ?? [];
      // 维护关联记录关系
      await this.createRecordLinks(record.id, brotherField.id, addRecordIds, { session: options?.session });
      // 追加后的最新记录ID列表
      const afterRecordIds = Array.from(new Set([...beforeRecordIds, ...addRecordIds]));
      // console.debug(`追加后的记录ID: ${JSON.stringify(afterRecordIds)}`);
      if (afterRecordIds.length === 0) {
        // 如果没有关联记录了, 则清空单元格
        const updateOne = record.buildUpdateQuery(user, {
          [this.id]: { bo: this.toBO(), cell: { data: [], values: [] } },
        });
        if (updateOne) {
          // console.debug(`清空关联记录: ${JSON.stringify(updateOne)}`);
          bulkUpdates.push({ updateOne });
        }
        continue;
      }
      // 刷新单元格的值
      const primaryValues: (string | null)[] = await foreignDatabase.getPrimaryFieldValueOfRecords(afterRecordIds, {
        session: options?.session,
        locale: user?.locale,
      });
      // console.debug(`关联记录的主字段值: ${JSON.stringify(primaryValues)}`);
      const cell: CellModel = { data: afterRecordIds, values: primaryValues };
      const updateOne = record.buildUpdateQuery(user, {
        [this.id]: { bo: this.toBO(), cell },
      });
      if (updateOne) {
        // console.debug(`重新刷新关联记录: ${JSON.stringify(updateOne)}`);
        bulkUpdates.push({ updateOne });
      }
    }
    // 执行更改
    await db.mongo.databaseRecord(this.spaceId).bulkWrite(bulkUpdates, { session: options?.session });
    // 更新引用字段
    await this.reloadRelateLookupCells(user, recordIds, { session: options?.session });

    // 最新的记录
    const updatedRecordModels = await db.mongo
      .databaseRecord(this.spaceId)
      .find({
        id: { $in: recordIds },
        databaseId: this.databaseId,
      })
      .session(options?.session ?? null);

    // 更新它的依赖链路
    const dependentFields = await this.getDependent();
    for (const dependentField of dependentFields) {
      // 逐个执行刷新
      await dependentField.updateCells(user, this.toBO(), updatedRecordModels, { session: options?.session });
    }
  }

  /**
   * 指定行数里移除单元格的关联记录
   */
  async removeLinkRecord(
    user: UserSO | null,
    updateRecordCellMap: Record<string /** record id */, string[]>,
    options?: { session?: mongoose.ClientSession },
  ): Promise<void> {
    // 单元格值必须是字符串数组, 空则代表清空
    const recordIds = Object.keys(updateRecordCellMap);
    if (recordIds.length === 0) {
      // 没有记录需要更新
      return;
    }
    const database = await this.getDatabase();
    const records = await database.getRecords(recordIds, options?.session);
    if (records.length === 0) {
      // 没有记录需要更新
      console.warn(`没有需要更新的记录: ${this.id}`);
      return;
    }
    const foreignDatabase = await this.getForeignDatabase();
    const brotherField = await this.getBrotherField();
    if (!foreignDatabase || !brotherField) {
      // 没有关联表, 跳过
      console.warn(`关联表不存在, 跳过: ${this.id}`);
      return;
    }
    const bulkUpdates: mongoose.AnyBulkWriteOperation<DatabaseRecordModel>[] = [];
    for (const record of records) {
      // 删除的记录ID列表
      const deleteRecordIds = updateRecordCellMap[record.id];
      // 移除对应的记录ID
      const linkCell = record.getCellSO(this.id) as LinkCellSO;
      const beforeRecordIds = linkCell.getData();
      if (!beforeRecordIds || beforeRecordIds.length === 0) {
        // 没有关联记录, 跳过
        continue;
      }
      // console.debug(`删除关联记录: ${JSON.stringify(deleteRecordIds)} from record: ${record.id}`);
      // console.debug(`删除前: ${JSON.stringify(beforeRecordIds)}`);
      // 删除关联记录关系
      await this.removeRecordLinks(record.id, deleteRecordIds, { session: options?.session });
      // 删除记录ID
      const afterRecordIds = beforeRecordIds.filter((id) => !deleteRecordIds.includes(id));
      // console.debug(`删除后: ${JSON.stringify(afterRecordIds)}`);
      if (afterRecordIds.length === 0) {
        // 如果删除后没有关联记录了, 则清空单元格
        const updateOne = record.buildUpdateQuery(user, {
          [this.id]: { bo: this.toBO(), cell: { data: [], values: [] } },
        });
        if (updateOne) {
          // console.debug(`清空关联记录: ${JSON.stringify(updateOne)}`);
          bulkUpdates.push({ updateOne });
        }
        continue;
      }
      // 刷新单元格的值
      const primaryValues: (string | null)[] = await foreignDatabase.getPrimaryFieldValueOfRecords(afterRecordIds, {
        session: options?.session,
        locale: user?.locale,
      });
      const cell: CellModel = { data: afterRecordIds, values: primaryValues };
      const updateOne = record.buildUpdateQuery(user, {
        [this.id]: { bo: this.toBO(), cell },
      });
      if (updateOne) {
        // console.debug(`重新刷新关联记录: ${JSON.stringify(updateOne)}`);
        bulkUpdates.push({ updateOne });
      }
    }
    // 执行更改
    await db.mongo.databaseRecord(this.spaceId).bulkWrite(bulkUpdates, { session: options?.session });
    // 更新引用字段
    await this.reloadRelateLookupCells(user, recordIds, { session: options?.session });

    // 最新的记录
    const updatedRecordModels = await db.mongo
      .databaseRecord(this.spaceId)
      .find({
        id: { $in: recordIds },
        databaseId: this.databaseId,
      })
      .session(options?.session ?? null);

    // 更新它的依赖链路
    const dependentFields = await this.getDependent();
    for (const dependentField of dependentFields) {
      // 逐个执行刷新
      await dependentField.updateCells(user, this.toBO(), updatedRecordModels, { session: options?.session });
    }
  }

  protected override shouldUpdateCells(other: LinkFieldProperty): boolean {
    // 关联字段只需要比较关联表的ID
    return this.property.foreignDatabaseId !== other.foreignDatabaseId;
  }

  protected override shouldCheckCircularReference(other: LinkFieldProperty): boolean {
    // 关联表切换必须检查循环引用
    return this.property.foreignDatabaseId !== other.foreignDatabaseId;
  }

  /**
   * 关联字段属性变更
   * 如果发生关联表变更:
   * 1. 原关联表的双向关联字段转换为单行文本字段
   * 2. 清空本表所有使用此关联字段作为引用的字段
   * 3. 新关联表创建一个双向关联的字段, 并且新关联表的所有视图都隐藏该字段
   */
  protected override async buildUpdatePropertyOperation(
    user: UserSO,
    updateFieldBO: DatabaseLinkField,
  ): Promise<DistributedOperation> {
    // 双向关联字段的配置属性更改(切换关联表)
    const { foreignDatabaseId: newForeignDatabaseId } = updateFieldBO.property;
    if (!newForeignDatabaseId) {
      throw new ServerError(errors.database.relate_database_is_required);
    }
    const foreignDatabaseId = this.getForeignDatabaseId();
    if (foreignDatabaseId && foreignDatabaseId === newForeignDatabaseId) {
      // 关联表没有变更
      return super.buildUpdatePropertyOperation(user, updateFieldBO);
    }
    // 更改关联表, 不需要理会updateFieldBO.property.brotherFieldId, 有也不理会, 因为前端不可能知道
    // 关联表变了, 删除对面关联表的双向关联字段(不抛异常, 有可能对面表已经不存在)
    const prismaOperations: PrismaPromise<unknown>[] = [];
    const mongoOperations: MongoTransactionCB[] = [];
    // 原关联表的处理(注意原来可能是关联自己而已)
    if (foreignDatabaseId !== this.databaseId) {
      // 处理不关联自己的表
      const originRelateDatabase = await this.getForeignDatabase();
      const brotherFieldId = this.getBrotherFieldId();
      if (originRelateDatabase && brotherFieldId) {
        const originBrotherField = originRelateDatabase.findFieldByFieldKey(brotherFieldId);
        if (originBrotherField) {
          // 好像不需要删除, 需要转换为单文本字段
          this.needConvertBrotherField = true;
          // 删掉recordLink表的记录
          mongoOperations.push(async (session) => {
            await RecordLinkSO.deleteByFieldId(this.id, { session });
          });
        }
      }
    }

    // 清空本表里所有使用此关联字段作为引用的字段
    const resetOperations = await this.resetRelatedLookupFieldsOperations();
    prismaOperations.push(...resetOperations);

    if (newForeignDatabaseId !== this.databaseId) {
      // 新关联表创建一个双向关联的字段(外表变换为自我关联不予处理)
      const database = await this.getDatabase();
      const relateDatabase = await DatabaseSO.init(newForeignDatabaseId);
      const relateField = relateDatabase.createNewLinkFieldBO(database, this.id);
      const relateFieldBOProcessor = FieldBOProcessorFactory.getProcessor(relateField);
      const relateFieldInput = relateFieldBOProcessor.toPrismaInput(user.id, relateDatabase);
      prismaOperations.push(db.prisma.databaseField.create({ data: relateFieldInput }));

      // 当创建双向关联字段时，无法知道新增字段要在对面表的哪些视图中显示，因此默认从所有视图中隐藏
      const relateDatabaseViews = await relateDatabase.getViews();
      // 关联表的所有视图都隐藏该字段
      prismaOperations.push(
        ...relateDatabaseViews.map((view) =>
          view.addFieldOperations(user.id, { id: relateFieldBOProcessor.getId(), hidden: true }),
        ),
      );
      // 更新本表的字段信息
      prismaOperations.push(
        this.getUpdateModelOperation(user.id, {
          ...updateFieldBO,
          property: { ...updateFieldBO.property, brotherFieldId: relateField.id },
        }),
      );
    } else {
      // 只改成了自己关联
      prismaOperations.push(
        this.getUpdateModelOperation(user.id, {
          ...updateFieldBO,
          property: { ...updateFieldBO.property, brotherFieldId: this.id },
        }),
      );
    }

    // 使用该字段的引用字段也清空
    const lookupFields = await this.getRelatedLookupFields();

    for (const lookupField of lookupFields) {
      // 继续递归往下查找被谁依赖, 全部清空
      mongoOperations.push(async (session) => {
        await lookupField.unsetCellAsPage(user, { session });
      });
    }

    return { prismaOperations, mongoOperations };
  }

  /**
   * 关联字段被转换为其它类型
   * 1. 清空本表所有使用此关联字段作为引用的字段
   * 2. 对面表的双向关联字段转换为单行文本字段(不放在本次, 脱离事务处理, 因为可能会触发深度依赖链路)
   */
  protected override async buildConvertOperation(
    user: UserSO,
    updateFieldBO: DatabaseField,
  ): Promise<DistributedOperation> {
    // 关联字段被转换为其它类型时，需要将被引用的 Lookup 字段的关联信息清空
    const operations = await super.buildConvertOperation(user, updateFieldBO);

    // 重置依赖的引用字段的属性
    const resetOperations = await this.resetRelatedLookupFieldsOperations();
    operations.prismaOperations.push(...resetOperations);

    // 使用该字段的引用字段单元格数据也清空
    const lookupFields = await this.getRelatedLookupFields();
    for (const lookupField of lookupFields) {
      // 继续递归往下查找被谁依赖, 全部清空
      operations.mongoOperations.push(async (session) => {
        await lookupField.unsetCellAsPage(user, { session });
      });
    }

    // 对面表的双向关联字段转换为单行文本字段(不放在本次, 脱离事务处理, 因为可能会触发深度依赖链路)
    const foreignDatabaseId = this.getForeignDatabaseId();
    if (foreignDatabaseId !== this.databaseId) {
      // 处理不关联自己的表
      const originRelateDatabase = await this.getForeignDatabase();
      const brotherFieldId = this.getBrotherFieldId();
      if (originRelateDatabase && brotherFieldId) {
        const originBrotherField = originRelateDatabase.findFieldByFieldKey(brotherFieldId);
        if (originBrotherField) {
          // 好像不需要删除, 需要转换为单文本字段
          this.needConvertBrotherField = true;
          // TODO 更新原关联表的记录? 删掉recordLink表的记录?
        }
      }
    }

    return operations;
  }

  async convertBrotherFieldToSingleText(user: UserSO): Promise<void> {
    // 转换双向关系表为单行文本字段
    const brotherField = await this.getBrotherField();
    if (!brotherField) {
      return;
    }

    const prismaOperations: PrismaPromise<unknown>[] = [];
    const mongoOperations: MongoTransactionCB[] = [];

    // 重置依赖的引用字段的属性
    const resetOperations = await brotherField.resetRelatedLookupFieldsOperations();
    prismaOperations.push(...resetOperations);

    // 使用该字段的引用字段单元格数据也清空
    const lookupFields = await brotherField.getRelatedLookupFields();
    for (const lookupField of lookupFields) {
      // 继续递归往下查找被谁依赖, 全部清空
      mongoOperations.push(async (session) => {
        await lookupField.unsetCellAsPage(user, { session });
      });
    }

    // 清理它的Lookup字段
    await db.mongo.transaction(
      async (session) => {
        // console.log(`=========> 执行原关联字段转换单行文本`);
        // 关联表的双向关联字段转换为单行文本字段
        const singleTextFieldBO: DatabaseSingleTextField = {
          ...brotherField.toBO(),
          type: 'SINGLE_TEXT',
          property: null,
        };
        await brotherField.refreshCellAsPage(
          user,
          { ...singleTextFieldBO, id: brotherField.id },
          async (records, recordStartIndex) =>
            // 字段转换, 刷新单元格
            brotherField.convertCells(user, records, singleTextFieldBO, { recordStartIndex, session }),
          {
            pageSize: 100,
            session,
            ignoreDependentFieldIds: [this.id],
          },
        );

        for (const mongoOperation of mongoOperations) {
          await mongoOperation(session);
        }

        // brother字段的PG操作
        const updateModelOperation = brotherField.getUpdateModelOperation(user.id, singleTextFieldBO);
        await db.prisma.$transaction([updateModelOperation, ...prismaOperations]);
      },
      {
        readPreference: 'primary',
        readConcern: { level: 'majority' },
        writeConcern: { w: 'majority' },
      },
    );
  }

  override async deleteAfter(user: UserSO): Promise<void> {
    // 删除关联字段, 需要将双向关联表的对应字段转换成文本字段
    await this.convertBrotherFieldToSingleText(user);
  }

  override async updateAfter(user: UserSO, _updateFieldBO: DatabaseField): Promise<void> {
    console.log(`更改字段后, 是否需要转换对面表的双向关联字段为单行文本: ${this.needConvertBrotherField}`);
    if (!this.needConvertBrotherField) {
      return;
    }
    await this.convertBrotherFieldToSingleText(user);
  }

  /**
   * 关联的目标字段是否存在
   */
  async checkBrotherFieldIsExists(sync: boolean): Promise<boolean> {
    if (!sync && this.brotherFieldIsExists !== null) {
      return this.brotherFieldIsExists;
    }

    const { foreignDatabaseId, brotherFieldId } = this.property;

    this.brotherFieldIsExists = false;
    try {
      // 没有关联关系
      if (!foreignDatabaseId || !brotherFieldId) {
        return false;
      }

      // 关联表不存在
      const foreignDatabaseSO = await DatabaseSO.getDatabaseById(foreignDatabaseId);
      if (!foreignDatabaseSO) {
        return false;
      }

      // 关联字段不存在
      const foreignFieldSO = foreignDatabaseSO.getFields().find((field) => field.id === brotherFieldId);
      if (!foreignFieldSO) {
        return false;
      }

      // 关联字段不是双向关联字段
      if (!FieldUtils.isOnlyLinkFieldSO(foreignFieldSO)) {
        return false;
      }
    } catch (e) {
      // 抛出异常，未知情况
      this.brotherFieldIsExists = null;
      throw e;
    }

    this.brotherFieldIsExists = true;
    return true;
  }

  override async getDependencies(): Promise<FieldSO[]> {
    // 获取双向关联表
    const foreignDatabase = await this.getForeignDatabase();
    if (!foreignDatabase) {
      // 关联表可能已经不存在, 那就不依赖
      return [];
    }
    // 获取首字段
    const primaryField = foreignDatabase.getPrimaryField();
    return [primaryField];
  }

  /**
   * 刷新本表里指定行数的查找引用字段单元格
   */
  async reloadRelateLookupCells(
    user: UserSO | null,
    recordIds: string[], // 当前表记录
    options?: { session?: mongoose.ClientSession },
  ): Promise<void> {
    // 刷新本表使用此关联字段的引用字段
    const lookupFields = await this.getRelatedLookupFields();
    for (const lookupField of lookupFields) {
      console.debug(`刷新本关联字段的查找引用字段: ${lookupField.name}`);
      await lookupField.reloadCells(user, recordIds, options);
    }
  }

  /**
   * 增加关联记录关系
   */
  private async createRecordLinks(
    recordId: string,
    brotherFieldId: string,
    brotherRecordIds: string[],
    options?: { session?: mongoose.ClientSession },
  ): Promise<void> {
    // 批量添加关联记录关系
    // console.debug(`[${this.id}]添加关联记录关系: ${recordId} -> ${brotherFieldId} ${brotherRecordIds.join(', ')}`);
    const recordLinks: DatabaseRecordLinkModel[] = brotherRecordIds.map((brotherRecordId) => ({
      // 关联字段ID
      fieldId: this.id,
      // 当前表的记录
      recordId,
      // 对面关联字段ID
      brotherFieldId,
      // 对面表的记录
      brotherRecordId,
    }));
    await RecordLinkSO.createMany(recordLinks, { session: options?.session });
  }

  /**
   * 移除关联记录关系
   */
  private async removeRecordLinks(
    recordId: string,
    brotherRecordIds: string[],
    options?: { session?: mongoose.ClientSession },
  ): Promise<void> {
    // 批量删除关联记录关系
    await RecordLinkSO.deleteLinkRecords(this.id, recordId, brotherRecordIds, { session: options?.session });
  }

  /**
   * 更新指定行数的单元格值
   */
  async bulkUpdateCells(
    user: UserSO | null,
    updateRecordCells: UpdateFieldRecordCell[], // 更新的记录单元格值
    options?: { session?: mongoose.ClientSession },
  ): Promise<RecordChanges> {
    const foreignDatabase = await this.getForeignDatabase();
    const brotherField = await this.getBrotherField();
    if (!foreignDatabase || !brotherField) {
      // 没有关联表, 跳过
      console.warn(`关联表不存在, 跳过: ${this.id}`);
      return [];
    }
    // 对应每一行的单元格值更新
    const recordIds = updateRecordCells.map((cell) => cell.recordId);
    const database = await this.getDatabase();
    // 需要更新的记录
    const toUpdatedRecords = await database.getRecords(recordIds, options?.session);
    const bulkUpdates: mongoose.AnyBulkWriteOperation<DatabaseRecordModel>[] = [];
    const recordChanges: RecordChanges = [];
    for (const { recordId, cell } of updateRecordCells) {
      const toUpdatedRecord = toUpdatedRecords.find((record) => record.id === recordId);
      if (!toUpdatedRecord) {
        // 没有找到对应的记录, 跳过
        console.warn(`没有找到需要更新的记录: ${recordId}`);
        continue;
      }
      // 字段单元格对象
      const linkCell = toUpdatedRecord.getCellSO(this.id) as LinkCellSO;
      const beforeRecordIds = linkCell.getData() ?? [];
      // 要更新的值
      const afterRecordIds = cell.data ? (cell.data as string[]) : [];
      // 关联记录差异
      const diff = linkCell.getDiffCellData(afterRecordIds);
      // 有变化才需要更新
      if (diff.changed) {
        // console.debug(`更新[${this.name}]关联记录差异: ${JSON.stringify(diff)}`);
        // 维护关联记录关系
        // 新增和移除的记录都要在关联表那边更新和刷新
        if (diff.added.length > 0) {
          // 关联表兄弟字段单元格值追加本记录
          const recordAddMap: Record<string, string[]> = {};
          for (const addedRecordId of diff.added) {
            if (!recordAddMap[addedRecordId]) {
              recordAddMap[addedRecordId] = [recordId];
            } else {
              recordAddMap[addedRecordId].push(recordId);
            }
          }
          await brotherField.addLinkRecord(user, recordAddMap, { session: options?.session });
        }
        if (diff.removed.length > 0) {
          // 关联表兄弟字段单元格值移除本记录
          const recordDeleteMap: Record<string, string[]> = {};
          for (const removedRecordId of diff.removed) {
            if (!recordDeleteMap[removedRecordId]) {
              recordDeleteMap[removedRecordId] = [recordId];
            } else {
              recordDeleteMap[removedRecordId].push(recordId);
            }
          }
          await brotherField.removeLinkRecord(user, recordDeleteMap, { session: options?.session });
        }
        recordChanges.push({
          recordId: toUpdatedRecord.id,
          cellChanges: [{ field: this.toBO(), from: beforeRecordIds, to: afterRecordIds }],
        });
      }
      // 关联记录首字段值, 空数组就代表清空
      const primaryValues: (string | null)[] = await foreignDatabase.getPrimaryFieldValueOfRecords(afterRecordIds, {
        session: options?.session,
        locale: user?.locale,
      });
      const newCell: CellModel = { data: afterRecordIds, values: primaryValues };
      const updateOne = toUpdatedRecord.buildUpdateQuery(user, {
        [this.id]: { bo: this.toBO(), cell: newCell },
      });
      if (updateOne) {
        bulkUpdates.push({ updateOne });
      }
    }
    // 执行更新
    await db.mongo.databaseRecord(this.spaceId).bulkWrite(bulkUpdates, { session: options?.session });

    const updatedRecordIds = updateRecordCells.map((cell) => cell.recordId);
    // 刷新表内依赖关联字段的引用字段单元格, 因为它不属于链路依赖, 主要单元格值发生变化时, 才会触发
    await this.reloadRelateLookupCells(user, updatedRecordIds, options);

    return recordChanges;
  }

  /**
   * 重新刷新指定行数的字段单元格值
   * 给切换表时使用, 关联字段的单元格值全清空
   */
  async refreshCells(
    _user: UserSO,
    records: RecordSO[],
    updateFieldBO: DatabaseLinkField,
  ): Promise<RecordCellModelMap> {
    // 切换表后, 单元格值全清空
    // 使用此关联字段的原来的查找引用字段单元格值也清空, 具体看`buildUpdatePropertyOperation`方法
    const recordCellModelMap: RecordCellModelMap = {};
    for (const record of records) {
      // 只清理有关联记录的单元格
      const cellData = record.getCellData(this.id);
      if (cellData && Array.isArray(cellData) && cellData.length > 0) {
        recordCellModelMap[record.id] = {
          [this.id]: { bo: updateFieldBO, cell: { data: [], computed: undefined, values: [] } },
        };
      }
    }
    return recordCellModelMap;
  }

  /**
   * 双向关联字段的首列单元格值发生变化, 重新计算当前列的指定批次的记录
   * 依赖传入记录来更新
   */
  override async updateCellsWithDepend(
    user: UserSO | null,
    fromField: DatabaseFieldWithId,
    dependRecords: DatabaseRecordModel[], // 依赖的记录
    options?: { session?: mongoose.ClientSession },
  ): Promise<DatabaseRecordModel[]> {
    console.log(`依赖更新: 关联字段名称 ${this.name}`);
    // 关联字段的单元格值重新计算, 只会因为双向关联表的首字段单元格值发生变化
    const brotherFieldId = this.getBrotherFieldId();
    if (!brotherFieldId) {
      // 双向关联字段不存在, 搞错了?
      return [];
    }
    // 关联记录匹配更新
    const dependRecordIds = dependRecords.map((record) => record.id);
    if (dependRecordIds.length === 0) {
      // 没有依赖的记录, 不需要更新
      return [];
    }
    const database = await this.getDatabase();
    // 关联字段只会依赖双向关联的首字段ID
    const dependencies = await this.getDependencies();
    // console.log(`======> 关联字段单元格被动触发更新: ${this.id}-${this.name}(${this.databaseId})`);
    // console.log(`依赖[${fromField.id}]的字段单元格数据: ${JSON.stringify(dependRecords)}`);
    // console.log(
    //   `======> 关联字段单元格被动触发更新, 依赖的字段: ${dependencies.map((f) => `${f.id}(${f.databaseId})`)}`,
    // );
    if (dependencies.length !== 1 && dependencies[0].id !== fromField.id) {
      // 依赖的字段不对, 不是链路更新过来的
      throw new Error(`depend field not match, ${this.id} ${fromField.id}`);
    }
    // const primaryFieldOnForeignDatabase = dependencies[0];
    // const recordCellModelMap: RecordCellModelMap = {};
    const updatedRecords: DatabaseRecordModel[] = [];
    const bulkUpdates: mongoose.AnyBulkWriteOperation<DatabaseRecordModel>[] = [];
    // 根据对面的关联字段字段ID来查本表的记录
    // console.debug(`根据关联字段ID(${brotherFieldId})和依赖记录ID(${dependRecordIds})查询关联记录`);
    const recordIds = await RecordLinkSO.getLinkRecordIds(brotherFieldId, dependRecordIds, {
      session: options?.session,
    });
    // console.debug(`关联记录ID: ${JSON.stringify(recordIds)}`);
    if (recordIds.length === 0) {
      // 没有关联记录, 不需要更新
      return [];
    }
    // 需要更新的表记录
    const toUpdateRecords = await database.getRecords(Array.from(new Set(recordIds)), options?.session);
    // console.debug(`需要更新的记录: ${JSON.stringify(toUpdateRecords.map((r) => r.model))}`);
    const foreignDatabase = await this.getForeignDatabase();
    if (!foreignDatabase) {
      // 关联表可能已经不存在, 那就不依赖
      console.warn(`关联表不存在, 无法更新关联字段: ${this.name}`);
      return [];
    }
    for (const toUpdateRecord of toUpdateRecords) {
      // 关联记录ID值
      const linkRecordIds = toUpdateRecord.getCellData(this.id);
      // console.debug(`关联记录ID: ${JSON.stringify(linkRecordIds)}`);
      if (linkRecordIds && isStringArray(linkRecordIds) && linkRecordIds.length > 0) {
        // 只刷新有值的单元格, 获取依赖的单元格值
        const primaryValues: (string | null)[] = await foreignDatabase.getPrimaryFieldValueOfRecords(linkRecordIds, {
          session: options?.session,
          locale: user?.locale,
        });
        const cellModel: CellModel = { data: linkRecordIds, values: primaryValues };
        const fieldCellModelMap: FieldCellModelMap = { [this.id]: { bo: this.toBO(), cell: cellModel } };
        const updateOne = toUpdateRecord.buildUpdateQuery(user, fieldCellModelMap);
        if (updateOne) {
          bulkUpdates.push({ updateOne });
          const recordModel = toUpdateRecord.mergeCellModelToNewModel(fieldCellModelMap);
          updatedRecords.push(recordModel);
        }
      }
    }
    // 执行更新
    await db.mongo.databaseRecord(this.spaceId).bulkWrite(bulkUpdates, { session: options?.session });
    // 返回已经更新过的单元格值, 供下一个依赖此字段的单元格刷新
    console.log(`结束依赖更新: 关联字段名称 ${this.name}`);
    // console.debug(`更新的记录: ${JSON.stringify(updatedRecords)}`);
    return updatedRecords;
  }
}

/**
 * 单向关联字段
 * 未开放的功能
 */
export class OneWayLinkFieldSO extends AbstractLinkFieldSO<DatabaseOneWayLinkField> {
  override assertValueType(input: Exclude<CellValue, null>): boolean {
    // 只能字符串数组, 允许多个
    return this.isCellValueValidStringArray(input, true);
  }

  override toTemplate(opts?: ToTemplateOptions): DatabaseOneWayLinkField {
    const property = this.property as OneWayLinkFieldProperty;
    if (opts?.getTemplateId && property.foreignDatabaseId && !opts.getTemplateId(property.foreignDatabaseId)) {
      throw new Error(`database OneWayLink field [${this.id}] linked database not in current folder`);
    }
    const bo = super.toTemplate(opts);

    if (opts?.getTemplateId && property.foreignDatabaseId && !property.foreignDatabaseTemplateId) {
      property.foreignDatabaseTemplateId = opts.getTemplateId(property.foreignDatabaseId);
      property.foreignDatabaseId = undefined;
    }
    return { ...bo, property } as DatabaseOneWayLinkField;
  }
}
