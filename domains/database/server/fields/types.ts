import { MemberSO } from '@bika/domains/unit/server/member-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { DatabaseRecordModel, Prisma } from '@bika/server-orm';
import { CellValue, DatabaseFieldWithId } from '@bika/types/database/bo';
import { CellModel } from '../cells/types';

// 字段BO存储对象, 落库存储
export type DatabaseFieldModel = Prisma.DatabaseFieldGetPayload<Prisma.DatabaseFieldDefaultArgs>;

/**
 * 创建记录时候的上下文
 */
export type CreateRecordContext = {
  user: UserSO | null;
  member: MemberSO | null;
  now: Date;
};

/**
 * 更新记录时候的上下文
 */
export type UpdateRecordContext = {
  user: UserSO | null;
  member: MemberSO | null;
  now: Date;
};

// 记录ID数据类型
type RecordId = string;
type FieldId = string;

// 以字段列作为键的记录单元格值
export type UpdateFieldRecordCell = {
  recordId: RecordId;
  cell: CellModel;
};
export type UpdateFieldRecordCellMap = Record<FieldId, UpdateFieldRecordCell[]>;

/**
 * 构建单元格模型的选项
 */
export type BuildCellModelOptions = {
  input: Exclude<CellValue, null>;
  context: CreateRecordContext;
};

export type UpdateCellModelOptions = {
  // 不接受undefined/null, 如果是则视为清空单元格数据
  input: Exclude<CellValue, null>;
  context: UpdateRecordContext;
};

export type FieldCellDiff = Record<
  FieldId /* fieldId */,
  {
    from: CellModel;
    to: CellModel;
  }
>;
export type RecordDiff = [RecordId, FieldCellDiff];

export type FieldCellChange = {
  field: DatabaseFieldWithId;
  from: CellValue; // 变更前的单元格值
  to: CellValue; // 变更后的单元格值
};

export type RecordChange = {
  recordId: string; // 记录ID
  cellChanges: FieldCellChange[]; // 字段对应单元格值的变更
};

export type RecordChanges = RecordChange[]; // 记录变更列表

export type BatchRecordChange = {
  changes: RecordChange[]; // 变更的记录
  previousFields: DatabaseFieldWithId[]; // 变更前的字段列表, 必定存在, 无字段不成记录
  previousRecords: Record<RecordId, DatabaseRecordModel>; // 变更前的记录列表, 必定存在, 无记录不成变更
};
