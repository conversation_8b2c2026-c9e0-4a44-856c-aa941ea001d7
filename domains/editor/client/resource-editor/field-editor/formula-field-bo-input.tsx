import { type FC, useState, useMemo } from 'react';
import type { ILocaleContext } from '@bika/contents/i18n';
import { FormulaHelper } from '@bika/domains/database/shared';
import { FormulaModal } from '@bika/domains/shared/ui';
import type { DatabaseFormulaField } from '@bika/types/database/bo';
import { useNodeResourceApiContext } from '@bika/types/node/context';
import { Box } from '@bika/ui/layouts';
import { FormulaInput } from '../formula/formula-input';

interface IProps {
  value: DatabaseFormulaField;
  onChange: (value: DatabaseFormulaField) => void;
  locale: ILocaleContext;
  databaseId: string;
}

export const FormulaFieldBoInput: FC<IProps> = (props) => {
  const { t, lang } = props.locale;
  const api = useNodeResourceApiContext();
  const { data: columns } = api.database.getFieldsBO(props.databaseId);

  const expression = props.value.property.expression ?? props.value.property.expressionTemplate ?? '';

  const expressionValue = useMemo(() => {
    if (!columns) {
      return '';
    }
    return FormulaHelper.expressionTransform(expression, { fields: columns }, 'name', { locale: lang });
  }, [columns, expression, lang]);

  const [showFormulaModal, setShowFormulaModal] = useState(false);

  return (
    <Box sx={{ marginY: '16px' }}>
      <FormulaInput
        label={t.database_fields.formula.property.expression}
        placeholder={t.database_fields.formula.property.expression_placeholder}
        value={expressionValue}
        onClick={() => {
          setShowFormulaModal(true);
        }}
      />
      {showFormulaModal && (
        <FormulaModal
          value={expression}
          field={props.value}
          onConfirm={(value) => {
            props.onChange({
              ...props.value,
              property: {
                ...props.value.property,
                expression: value,
              },
            });
            setShowFormulaModal(false);
          }}
          onClose={() => {
            setShowFormulaModal(false);
          }}
        />
      )}
    </Box>
  );
};
