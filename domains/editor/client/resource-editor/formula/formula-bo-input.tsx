import Fuse from 'fuse.js';
import { isEmpty } from 'lodash';
import Image from 'next/image';
import React, { useState, useMemo, useRef, useEffect } from 'react';
import { type FormulaConfig, getFormulaTypesConfig } from '@bika/contents/config/client/database/formulas';
import type { ILocaleContext } from '@bika/contents/i18n/context';
import { type Token, TokenType, FormulaExprLexer, FormulaHelper } from '@bika/domains/database/shared';
import type { FormulaType, DatabaseFormulaField } from '@bika/types/database/bo';
import type { INodeResourceApi } from '@bika/types/node/context';
import { useSpaceRouter } from '@bika/types/space/context';
import { Chip } from '@bika/ui/chip';
import { FieldTypeIconComponent } from '@bika/ui/database/record-detail/field-type-icon-component';
import { Box, Stack } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/text-components';
import { FormulaColour } from './formula-colour';
import { FormulaEditor } from './formula-editor';

interface Props {
  value: string;
  onChange: (value: string) => void;
  field?: DatabaseFormulaField;
  type: FormulaType;
  locale: ILocaleContext;
  api: INodeResourceApi;
  expError?: string;
  setExpError?: (value: string) => void;
  setLoading?: (value: boolean) => void;
}

type HoverType =
  | {
      key: 'formula';
      type: FormulaType;
    }
  | {
      key: 'field';
      type: string;
    };

export function FormulaBOInput(props: Props) {
  const { type, locale, value, api, field, expError, setExpError, setLoading } = props;

  const { t, i, lang } = locale;

  // console.log('当前语言:', lang);

  const [currentType, setCurrentType] = useState<HoverType>({
    key: 'formula',
    type,
  });

  const [tokens, setTokens] = useState<Token[]>();
  const [activeToken, setActiveToken] = useState<Token>();

  const [cursorOffset, setCursorOffset] = useState<number>(value.length);
  const [activeNodeIndex, setActiveNodeIndex] = useState<number>(0);
  const inputRef = useRef<HTMLDivElement>(null);
  const formulaInputEleRef = useRef<HTMLElement>();

  const { useParams } = useSpaceRouter();
  const params = useParams<{ nodeId: string }>();
  const databaseId = params.nodeId;

  const allFormulas = getFormulaTypesConfig(locale);

  const { data: columns, isLoading } = api.database.getFieldsBO(databaseId);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    setLoading?.(isLoading);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoading]);

  const currentFormulaConfig = currentType.key === 'formula' ? allFormulas[currentType.type] : undefined;
  const currentFieldConfig =
    currentType.key === 'field' ? columns?.find((column) => column.id === currentType.type) : undefined;

  useEffect(() => {
    if (activeToken) {
      let tokenValue = activeToken.value;
      if (allFormulas[tokenValue as FormulaType]) {
        setCurrentType({
          key: 'formula',
          type: tokenValue as FormulaType,
        });
      }
      tokenValue = tokenValue.replace(/[{}]/g, '');
      const column = columns?.find((col) => i(col.name) === tokenValue);
      if (column) {
        setCurrentType({
          key: 'field',
          type: column.id as string,
        });
      }
    }
  }, [activeToken]);

  // 解析表达式是否正确, 不正确就抛出异常
  const parse = (expr: string) => {
    // console.log('解析表达式:', expr);
    try {
      if (!field || !columns) {
        return;
      }
      const lexer = new FormulaExprLexer(expr);
      setTokens(lexer.fullMatches);
      const parseRlt = FormulaHelper.parse(
        expr,
        {
          fields: columns,
          field: {
            ...field,
            id: '', // UI不可能选择自己
          },
        },
        'name',
        { locale: lang },
      );
      if ('errors' in parseRlt && Array.isArray(parseRlt.errors)) {
        setExpError?.(parseRlt.errors[0].toString());
      } else {
        setExpError?.('');
      }
    } catch (e) {
      if (typeof e === 'string') {
        setExpError?.(e);
      } else if (e instanceof Error) {
        // console.error(e);
        setExpError?.(e.message);
      }
    }
  };

  // 展示的值是转换后的表达式
  const displayValue = useMemo(() => {
    if (!columns || columns.length === 0) {
      return '';
    }
    // console.log('展示的值:', value);
    const i18nExpression = FormulaHelper.expressionTransform(value, { fields: columns }, 'name', { locale: lang });
    parse(i18nExpression);
    return i18nExpression;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value, columns]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const filteredColumns = useMemo(() => {
    if (!columns) {
      return [];
    }
    const fieldColumns = columns.filter((column) => column.id !== field?.id);
    const fuse = new Fuse(fieldColumns, {
      findAllMatches: true,
      keys: ['name'],
    });
    let searchValue = activeToken?.value || '';
    searchValue = searchValue[0] === '{' ? searchValue.slice(1) : searchValue;
    searchValue = searchValue[searchValue.length - 1] === '}' ? searchValue.slice(0, -1) : searchValue;

    return searchValue ? fuse.search(searchValue) : fieldColumns.map((c, idx) => ({ item: c, refIndex: idx }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [columns, activeToken]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    const element = document.getElementById('FORMULA_TEXTAREA_ELEMENT');
    if (element) {
      formulaInputEleRef.current = element;
      formulaInputEleRef.current.innerText = displayValue;
      formulaInputEleRef.current.focus();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const filteredGroupedFormulas = useMemo(() => {
    const Formulas = Object.values(allFormulas).filter((formula) => formula.display !== 'HIDDEN');
    const fuse = new Fuse(Formulas, {
      findAllMatches: true,
      keys: ['name'],
    });
    let searchValue = activeToken?.value || '';
    searchValue = searchValue[0] === '{' ? searchValue.slice(1) : searchValue;
    searchValue = searchValue[searchValue.length - 1] === '}' ? searchValue.slice(0, -1) : searchValue;
    const filteredList = searchValue
      ? fuse.search(searchValue)
      : Formulas.map((c, idx) => ({ item: c, refIndex: idx }));

    const grouped = filteredList
      .map((fl) => fl.item)
      .reduce(
        (acc, val) => {
          if (!acc[val.type]) {
            acc[val.type] = [];
          }
          acc[val.type].push({ key: val.name, ...val });
          return acc;
        },
        {} as Record<string, (FormulaConfig & { key: FormulaType })[]>,
      );

    return Object.entries(grouped);
  }, [allFormulas, activeToken]);

  const insertText = (text: string, start: number, length = 0) => {
    let _start = start;
    let _length = length;
    if (isEmpty(formulaInputEleRef.current?.childNodes)) {
      const newTextNode = document.createTextNode('');
      formulaInputEleRef.current?.appendChild(newTextNode);
    }
    let textNode = formulaInputEleRef.current?.childNodes[activeNodeIndex];
    if (!textNode) {
      _start = 0;
      _length = text.length;
      textNode = formulaInputEleRef.current?.childNodes[activeNodeIndex];
    }
    if (!textNode) {
      console.error('InsertText textNode not found');
      return;
    }

    // 修复：确保偏移量不超过文本节点的长度
    const nodeLength = textNode.textContent?.length || 0;
    _start = Math.min(_start, nodeLength);
    _length = Math.min(_length, nodeLength - _start);

    const Range = document.createRange();
    Range.setStart(textNode, _start);
    Range.setEnd(textNode, _start + _length);
    const sel = window.getSelection();
    sel?.removeAllRanges();
    sel?.addRange(Range);
    document.execCommand('insertHTML', false, text);

    if (text[text.length - 1] === ')') {
      const len = text.length;
      Range.setStart(textNode, _start + len - 1);
      Range.setEnd(textNode, _start + len - 1);
      sel?.removeAllRanges();
      sel?.addRange(Range);
    }
  };

  const onItemClick = (key: string, itemType: HoverType['key']) => {
    const _key = itemType === 'field' ? key.replace(/[{}\\]/g, '\\$&') : key;
    if (activeToken) {
      const activeTokenLength = activeToken.value.length;
      const start = activeNodeIndex > 0 ? cursorOffset - activeTokenLength : activeToken.index;
      insertText(itemType === 'field' ? `{${_key}}` : `${_key}()`, start, activeTokenLength);
      setCursorOffset(start + _key.length + 1);
    } else {
      insertText(itemType === 'field' ? `{${_key}}` : `${_key}()`, cursorOffset);
      setCursorOffset(cursorOffset + _key.length + 2);
    }
    if (itemType !== 'field') {
      setActiveToken(undefined);
    }
  };

  const findTokenByIndex = (index: number): Token | undefined => {
    if (!tokens) {
      return undefined;
    }
    const currToken = tokens.find((token) => token.index < index && token.index + token.value.length >= index);
    if (currToken?.value.startsWith('{fld')) {
      const columnId = currToken.value.replace(/[{}]/g, '');
      const column = columns?.find((col) => col.id === columnId);
      return {
        ...currToken,
        value: `{${i(column?.name)}}`,
      };
    }
    return currToken;
  };

  // 处理输入框的变化
  const handleChange = (text: string) => {
    // console.log('输入变化:', text);
    const transformedValue = FormulaHelper.expressionTransform(text, { fields: columns ?? [] }, 'name', {
      locale: lang,
    });
    parse(transformedValue);
    const storeText = FormulaHelper.expressionTransform(text, { fields: columns ?? [] }, 'id');
    // 外边存储的值是{fieldId}格式的值
    props.onChange(storeText);
  };

  const onSelectStart = () => {
    const selection = document.getSelection();
    if (!selection) {
      return;
    }

    let { anchorNode, anchorOffset } = selection;
    let finalAnchorOffset = anchorOffset; // The position offset of the current node where the cursor is located
    let anchorNodeIndex = 0; // Index of the current node in all nodes
    const originAnchorOffset = anchorOffset; // Cursor original position offset

    while (anchorNode?.previousSibling && anchorNode?.previousSibling?.nodeName !== 'H2') {
      anchorNodeIndex++;
      if (anchorNodeIndex === 1) {
        finalAnchorOffset = anchorNode.nodeName === 'BR' ? originAnchorOffset : anchorOffset;
      }
      anchorNode = anchorNode.previousSibling;
      anchorOffset += anchorNode.textContent?.length || 0;
      if (anchorNode.nodeName === 'BR') {
        anchorOffset += 1;
      }
    }

    const token = findTokenByIndex(anchorOffset);
    if (
      token &&
      (token.type === TokenType.Value ||
        token.type === TokenType.PureValue ||
        token.type === TokenType.Call ||
        token.type === TokenType.Number)
    ) {
      setActiveToken(token);
    } else {
      setActiveToken(undefined);
    }

    setActiveNodeIndex(anchorNodeIndex);
    setCursorOffset(finalAnchorOffset);
  };

  return (
    <Stack spacing={3}>
      <Stack spacing={1}>
        <Typography
          fontSize="16px"
          fontWeight={800}
          textColor={'var(--text-primary)'}
          sx={{
            mx: 1,
          }}
        >
          {t.formula.input_formula}
        </Typography>
        <FormulaEditor
          value={displayValue}
          onChange={handleChange}
          placeholder={t.formula.input_formula}
          onSelectStart={onSelectStart}
          inputRef={inputRef}
        />
        {expError && (
          <Typography
            level="b1"
            textColor={'var(--status-danger)'}
            sx={{
              mx: 1,
              mt: 1,
            }}
          >
            {expError}
          </Typography>
        )}
      </Stack>
      <Stack spacing={1}>
        <Typography level="b2" fontWeight={600} textColor={'var(--text-primary)'} pl={1}>
          {t.formula.select_a_formula}
        </Typography>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            width: '100%',
            gap: 2,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              width: '30%',
              height: '400px',
              overflowY: 'auto',
              borderRight: '1px solid var(--border-default)',
            }}
          >
            {filteredColumns.length > 0 && (
              <Stack>
                <Typography
                  textColor={'var(--text-secondary)'}
                  sx={{ px: 1, py: 0.5, fontWeight: 'bold', fontSize: '14px' }}
                >
                  {t.resource.title_field_id}
                </Typography>
                {filteredColumns.map((column) => (
                  <Box
                    key={column.item.id}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      px: 1,
                      height: '32px',
                      borderRadius: 4,
                      gap: 1,
                      backgroundColor: column.item.id === currentType.type ? 'var(--hover)' : 'transparent',
                      cursor: 'pointer',
                      '&:hover': {
                        backgroundColor: 'var(--hover)',
                      },
                    }}
                    onMouseEnter={() => {
                      if (!column.item.id) {
                        return;
                      }
                      setCurrentType({
                        key: 'field',
                        type: column.item.id,
                      });
                    }}
                    onClick={() => {
                      // click 插入字段的名称对齐编辑器，方便编辑时光标的定位
                      onItemClick(i(column.item.name), 'field');
                    }}
                  >
                    <FieldTypeIconComponent type={column.item.type} size={16} />
                    <Typography
                      level="b2"
                      sx={{
                        color: 'var(--text-primary)',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {i(column.item.name)}
                    </Typography>
                  </Box>
                ))}
              </Stack>
            )}
            {filteredGroupedFormulas.map(([groupType, formulas]) => (
              <Box key={groupType} sx={{ marginTop: 2 }}>
                <Typography
                  textColor={'var(--text-secondary)'}
                  sx={{ px: 1, py: 0.5, fontWeight: 'bold', fontSize: '14px' }}
                >
                  {t(`formula.${groupType.toLowerCase()}`)}
                </Typography>
                {formulas.map((formula) => (
                  <Box
                    key={formula.label}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      px: 1,
                      height: '32px',
                      borderRadius: 4,
                      gap: 1,
                      backgroundColor: formula.key === currentType.type ? 'var(--hover)' : 'transparent',
                      cursor: formula.display === 'COMING_SOON' ? 'not-allowed' : 'pointer',
                      '&:hover':
                        formula.display === 'COMING_SOON'
                          ? {}
                          : {
                              backgroundColor: formula.key === currentType.type ? 'var(--hover)' : 'var(--hover)',
                            },
                    }}
                    onMouseEnter={() => {
                      if (formula.display === 'COMING_SOON') {
                        return;
                      }
                      setCurrentType({
                        key: 'formula',
                        type: formula.key,
                      });
                    }}
                    onClick={() => {
                      if (formula.display === 'COMING_SOON') {
                        return;
                      }
                      onItemClick(formula.key, 'formula');
                    }}
                  >
                    <Image src={formula.iconPath} alt={formula.iconPath} width={16} height={16} />
                    <Typography
                      level="b2"
                      sx={{
                        color: 'var(--text-primary)',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {formula.label}
                      {formula.display === 'COMING_SOON' && (
                        <Chip
                          sx={{
                            backgroundColor: 'var(--selected)',
                            color: 'var(--text-primary)',
                            ml: 0.5,
                          }}
                        >
                          {t.action.coming_soon}
                        </Chip>
                      )}
                    </Typography>
                  </Box>
                ))}
              </Box>
            ))}
          </Box>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              width: '70%',
              height: '400px',
              overflowY: 'auto',
              alignItems: 'flex-start',
              justifyContent: 'flex-start',
              gap: 2,
            }}
          >
            <Stack spacing={1}>
              <Typography level="h3" textColor={'var(--text-primary)'}>
                {currentFormulaConfig?.label.toUpperCase() || i(currentFieldConfig?.name)}
              </Typography>
              {currentFormulaConfig && (
                <Typography level="b4" textColor={'var(--text-secondary)'}>
                  {currentFormulaConfig.summary}
                </Typography>
              )}
            </Stack>
            <Stack spacing={2}>
              <Stack spacing={1}>
                <Typography level="b2" textColor={'var(--text-primary)'}>
                  {t.formula.usage}{' '}
                </Typography>
                <Box sx={{ bgcolor: 'var(--bgControlsDegradeHigh)', borderRadius: '4px' }}>
                  <code
                    style={{
                      padding: '12px',
                      display: 'inline-block',
                      color: 'var(--text-primary)',
                    }}
                  >
                    {currentFormulaConfig?.definition || `{${i(currentFieldConfig?.name)}}`}
                  </code>
                </Box>
              </Stack>
              <Stack spacing={1}>
                <Typography level="b2" textColor={'var(--text-primary)'}>
                  {t.formula.example}
                </Typography>
                <Box sx={{ bgcolor: 'var(--bgControlsDegradeHigh)', borderRadius: '4px' }}>
                  <code style={{ padding: '12px', display: 'inline-block' }}>
                    <FormulaColour expression={currentFormulaConfig?.example || `{${i(currentFieldConfig?.name)}}`} />
                  </code>
                </Box>
              </Stack>
            </Stack>
          </Box>
        </Box>
      </Stack>
    </Stack>
  );
}
